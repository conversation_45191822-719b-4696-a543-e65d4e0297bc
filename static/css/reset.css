/**
 * <PERSON>'s Reset CSS v2.0 (http://meyerweb.com/eric/tools/css/reset/)
 * http://cssreset.com
 */
@font-face {
  font-family: 'Alp Ekran';
  src: url('../fonts/AlpEkran.ttf') format('truetype');
  font-style: normal;
}

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video,
input,
button {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font-weight: normal;
  vertical-align: baseline;
  font-family: 'Alp Ekran';
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: none;
}

table {
  /* border-collapse: collapse; */
  border-collapse: initial;
  border-spacing: 0;
}

/* custom */
a {
  /* color: #7e8c8d; */
  color: rgba(255, 255, 255, 0.5);
  text-decoration: none;
  -webkit-backface-visibility: hidden;
}

li {
  list-style: none;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}

::-webkit-scrollbar-track-piece {
  -webkit-border-radius: 0px;
}

::-webkit-scrollbar-thumb:vertical {
  height: 5px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px;
}

::-webkit-scrollbar-thumb:horizontal {
  width: 5px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px;
}

html,
body {
  width: 100%;
  line-height: 1;
  font-weight: 200;
  /* font-family: 'PingFang SC', 'STHeitiSC-Light', 'Helvetica-Light', arial, sans-serif; */
}

body {
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.menu-item .el-tabs__nav-wrap.is-scrollable {
  padding-left: 0;
  padding-right: 90px;
  height: 100%;
}

.top .menu .menu-item .el-tabs__header {
  border-bottom: none;
  height: 100%;
}

.top .menu .menu-item .el-tabs__nav {
  border: none;
  height: 100%;
}

.top .menu .menu-item .el-tabs__nav-wrap {
  height: 100%;
}

.top .menu .menu-item .el-tabs__nav-scroll {
  height: 100%;
  overflow-x: scroll;
}

.top .menu .menu-item .el-tabs__nav-scroll::-webkit-scrollbar {
  display: none;
}

.top .menu .menu-item .el-tabs__item {
  border: none;
  color: #ffffff;
  font-size: 22px;
  padding: 0 20px;
  direction: rtl;
}

.top-zh .menu .menu-item .el-tabs__item {
  direction: ltr;
}

.top-ug .menu .menu-item .el-tabs__item {
  direction: rtl;
}

.top .menu-zh .menu-item .el-tabs__item {
  border: none;
  color: #ffffff;
  font-size: 22px;
  padding: 0 20px;
  direction: ltr;
}

.menu .menu-item .el-tabs__nav-prev {
  right: 50px;
  left: unset;
  font-size: 20px;
  color: #ffffff;
  background: #1a1a1a;
  padding: 0 5px;
  /* margin-top: 5px; */
  height: 40px;
}

.topp .menu .menu-item .el-tabs__nav-prev {
  margin-top: 5px;
}

.topp .menu .menu-item .el-tabs__nav-next {
  margin-top: 5px;
}

.menu .menu-item .el-tabs__nav-next {
  font-size: 20px;
  right: 10px;
  color: #ffffff;
  background: #1a1a1a;
  padding: 0 5px;
  /* margin-top: 5px; */
  height: 40px;
}

.tops .menu .menu-item .el-tabs__nav-prev {
  margin-top: 0;
}

.tops .menu .menu-item .el-tabs__nav-next {
  margin-top: 0;
}

.menu .menu-item .el-tabs__nav-prev .el-icon-arrow-left,
.menu .menu-item .el-tabs__nav-next .el-icon-arrow-right {
  font-weight: bold;
}

.menu .menu-item .is-active {
  background: #139d59;
}

.food .menu .menu-item .is-active {
  background: none;
  border-bottom: 3px solid;
}

.food .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  border-bottom: 5px solid #139d59;
  color: #139d59;
  height: 100%;
  line-height: 50px;
}

.el-message__content {
  width: 100%;
}

.header .cell {
  color: #cccccc;
  font-size: 22px;
  padding-top: 3px;
}

.headers .cell {
  color: #1a1a1a;
  font-size: 22px;
  padding-top: 3px;
}

.wraps .el-table tr {
  background-color: #f2f2f2;
  cursor: pointer;
}

.wraps .row .cell {
  font-size: 22px;
  padding-top: 3px;
}

.staff .row .cell {
  font-size: 20px;
  padding-top: 3px;
}

.wraps .row td {
  border-bottom: 1px solid #cccccc;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.item .el-select {
  width: 100%;
}

.row .el-select {
  width: 100%;
}

.checks-ug .el-checkbox__label {
  padding-left: 0;
  padding-right: 10px;
  font-size: 20px;
}

.checks .el-checkbox__label {
  font-size: 20px;
}

.checks-ug .el-checkbox+.el-checkbox {
  margin-left: 0;
  margin-right: 30px;
}

.checks-ug.bar .el-checkbox-group .el-checkbox+.el-checkbox:nth-child(6) {
  margin-right: 0;
  /* margin-top: 10px; */
}

.boxs_ug .el-checkbox__label {
  padding-left: 0;
  padding-right: 10px;
  font-size: 26px;
}

.boxs .el-checkbox__label {
  font-size: 26px;
  vertical-align: -4px;
}

.boxs .el-checkbox+.el-checkbox {
  margin-left: 0;
}

.boxs .el-checkbox__inner {
  width: 20px;
  height: 20px;
}

.boxs .el-checkbox__inner::after {
  left: 7px;
  top: 3px;
}

.boxs .el-checkbox {
  width: 50%;
  margin-bottom: 20px;
}

.el-form-item__error {
  width: 100%;
  font-size: 16px;
}

.items-ug .el-form-item__label {
  float: right;
  padding: 0 0 0 12px;
}

.items .el-form-item__label {
  font-size: 20px;
}

.items-ug .avatar-uploader {
  /* float: right; */
}

.form-ug .el-form-item__label {
  float: right;
}

.form-ug .el-form-item__content {
  margin-left: 0 !important;
  margin-right: 250px;
}

.wraps .el-date-editor .el-range-separator {
  width: 7%;
  font-family: auto;
  color: #ffffff;
  font-size: 25px;
}

.detail .el-table th>.cell,
.detail .el-table .cell {
  color: #1a1a1a;
  font-size: 22px;
  padding-top: 3px;
}

/* .detail-ug .el-table th>.cell,.etail-ug .el-table .cell{
    direction: rtl
} */
/* .detail-zh .el-table th>.cell,.detail-zh .el-table .cell{
    text-align: right;
} */
/* .detail-zh .el-table .names .cell{
    text-align: left;
} */
.detail-zh .el-table {
  direction: rtl
}

.input-item .el-form-item {
  margin-bottom: 0;
}

.input-item .el-form-item__error {
  text-align: center;
  font-size: 16px;
}

.type .el-form-item {
  width: 46%;
}

.uy-input .el-input__inner::placeholder {
  direction: rtl;
  text-align: left;
}

.zh-input .el-input__inner::placeholder {
  direction: ltr;
  text-align: left;
}

.input-ug .el-input__inner {
  direction: rtl;
}

.uy-input .el-textarea__inner::placeholder {
  direction: rtl;
  text-align: left;
}

.el-textarea__inner::placeholder {
  font-family: 'Alp Ekran';
}

.el-textarea__inner {
  font-family: 'Alp Ekran';
}

.uy-toast {
  direction: rtl;
}

.uy-toast .el-message__content {
  font-size: 26px;
  margin-bottom: -5px;
}

.uy-toast .el-message__icon {
  margin-right: 0;
  margin-left: 10px;
  font-size: 20px;
}

.zh-toast .el-message__content {
  font-size: 22px;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px white inset;
}

.top .el-date-editor .el-range__icon {
  margin-top: -3px;
  font-size: 23px;
  color: #cccccc;
  margin-right: 10px;
}

.top .el-date-editor .el-range__close-icon {
  margin-top: -5px;
  font-size: 18px;
  color: #cccccc;
}

.top .el-date-editor .el-range-input {
  font-size: 20px;
  width: 50%;
  background: #4d4d4d;
  color: #ffffff;
}

.top .el-range-editor.el-input__inner {
  background: #4d4d4d;
  border: none;
  width: 500px;
}

.uy-date .el-date-range-picker__content.is-right .el-date-range-picker__header div {
  direction: rtl;
}

.uy-date .el-date-range-picker__header div {
  direction: rtl;
}

.uy-date .el-button+.el-button,
.zh-date .el-button+.el-button {
  font-size: 20px;
}

.uy-date .el-button--text,
.zh-date .el-button--text {
  font-size: 20px;
}

.top .el-input__inner {
  background-color: #4d4d4d;
  color: #ffffff;
  border: none;
  font-size: 22px;
}

.top .el-select .el-input .el-select__caret {
  font-size: 22px;
}

.el-table__header .headers .cell {
  text-overflow: unset;
}

.el-table__empty-text {
  font-size: 20px;
}

/* .table .el-table__header-wrapper{
    position: fixed;
    top: 182px;
    z-index: 999;
 } */
/* @media screen and (max-width: 1366px) {
    .table .el-table__header-wrapper{
        top: 160px;
     }
  } */
.row {
  font-size: 22px;
}

.el-button--text .iconfont {
  font-size: 22px;
}

.row .el-button.is-circle {
  padding: 0;
}

.wrap .row .cell {
  padding-top: 3px;
}

.input-ug input::placeholder {
  direction: rtl;
  text-align: left;
}

.input-ug input {
  direction: rtl;
}

.line {
  color: #fff;
  border-left: 1px solid #ccc;
  padding-right: 10px;
}

.wrap .danger {
  color: #ff5151;
}

.table-list .el-table th>.cell {
  padding-top: 5px;
  font-size: 22px;
  color: #1a1a1a;
}

.table-list .table-header {
  border-bottom: none !important;
}

.table-list .el-table .cell {
  padding-top: 5px;
  font-size: 22px;
  color: #666666;
}

.table-list .el-table--border th,
.table-list .el-table--border td {
  border-color: #cccccc;
}

.table-list .el-table th.is-leaf,
.table-list .el-table td {
  border-color: #cccccc;
}

.wrap .el-form-item__label {
  font-size: 20px;
}
.el-form-item__label-zh {
  font-size: 20px;
  text-align: left;
}
.wrap .order-btns {
  padding: 20px 40px;
  border-radius: 0;
}

.uy-select .el-select-dropdown__item {
  direction: rtl;
}

.wraps .table-list .el-table th.food-name {
  background: rgb(102, 102, 102) !important;
  color: #ffffff;
}

.wraps .table-list .el-table th.food-name .cell {
  color: #ffffff;
}

.wraps .el-table .grenn-num td .cell {
  color: #139d59;
  font-weight: bold;
}

@media screen and (max-width: 1366px) {
  .table-list .el-table th>.cell {
    font-size: 18px;
  }

  .headers .cell {
    font-size: 17px;
  }
}

.ug-op .el-input--suffix .el-input__inner {
  direction: rtl;
}
