.el-select-dropdown__list{
	direction: rtl;
}
.el-popper[x-placement^=bottom] .popper__arrow{
	left: initial !important;
	right: 35px;
}
.el-message__icon {
  margin-left: 10px;
}
.el-message {
  direction: rtl;
}
.el-table td,
.el-table th{
	padding: 6px 0;
}
.select-box input{
	width: 95%;
}
.el-dialog__header{
	direction: rtl;
}
.el-message-box__headerbtn,.el-dialog__headerbtn {
	right: initial;
	left: 20px;
}
.el-dialog__body{
	direction: rtl;
}
.el-form-item__label{
	float: none;
	float: right;
	margin-left: 0px !important;
}
.el-message-box__title{
	text-align: right;
}
.el-message-box__content{
	direction: rtl;
	text-align: right;
}
.el-message-box__status+.el-message-box__message {
  padding-left: 12px;
  padding-right: 36px;
}
.el-dialog .el-form-item__content {
	margin-left: 0 !important;
	margin-right: 120px;
}
.ltr-layout{
	direction: ltr;
}

.el-table__expanded-cell .el-form-item__label{
	padding-right: 0;
	padding-left: 12px;
	font-size: 12px;
	font-weight: 500;
	color: #99a9bf;
	text-align: right;
	width: 20%;
}
.el-table__expanded-cell .el-form-item__label:after {
  content: ":";
}
.el-table__expanded-cell .el-form-item__content{
	width: 75%;
}
.el-table__body .el-icon-arrow-right:before{
	content: "\E600";
}
.el-table__body .el-form-item {
	width: 50%;
	float: right;
	text-align: right;
	margin: 0;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
	right: initial;
	left: 20px;
}
.el-select .el-tag{
	margin-right: 6px;
}
.el-select .el-tag__close.el-icon-close {
  right: 7px;
}
.button-box-ug .el-icon-back{
	transform: rotate(180deg);
}
.el-pagination .btn-prev .el-icon{
	transform: rotate(180deg);
}
.el-pagination .btn-next .el-icon{
	transform: rotate(-180deg);
}
.el-upload-list__item .el-icon-close-tip{
	left:5px;
}
.order .el-table_1_column_6{
	direction: ltr;
}
.form-content .level-radio {
    margin-left: 30px;
}
.form-content .el-radio__label {
    padding-right: 5px;
}
.level-radio-button .el-radio-group{
   direction: ltr;
}
.el-upload-list--picture-card .el-upload-list__item{
	margin: 0 0px 8px 8px;
}
.img-upload .el-form-item__content{
	margin-right: 0;
}
.add-dialog .el-upload--picture-card{
	width: 100px;
	height: 100px;
	line-height: 102px;
}
.add-dialog .el-upload-list--picture-card .el-upload-list__item{
	width: 100px;
	height: 100px;
}
.add-dialog .el-form-item__content{
	margin-right: 125px;
}
.add-dialog .mgr0 .el-form-item__content{
    margin-right: 0;
}
.el-textarea__inner {
		font-family: 'Alp Ekran';
}
.print-wrap .el-table__footer-wrapper .cell{
    font-size: 15px;
    color: #111;
}
.print-wrap .el-table__footer-wrapper tbody td, .print-wrap .el-table__header-wrapper tbody td{
    background-color: #fff;
}
.print-wrap .el-table td, .print-wrap .el-table th.is-leaf{
  border-bottom: 1px solid #333;
}
.print-wrap .el-table--border td,.print-wrap .el-table--border th,.print-wrap .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed{
	border-right: 1px solid #333;
}
.print-wrap  .el-table--border, .el-table--group{
	border-top:1px solid #333;
	border-left:1px solid #333;
	padding: 1px;
	font-size:18px; 
	color:#000;
}
.print-wrap  .el-table__footer-wrapper td{
	border-top:1px solid #333;
}
.print-wrap  .el-table__footer-wrapper .cell{
	font-size: 19px;
  /* font-weight: 100; */
  color: #000;
}
.print-wrap  .has-gutter .el-table_1_column_7,.print-wrap  .has-gutter .el-table_2_column_18 {
	font-size: 14px;
}
.print-wrap  .has-gutter th{
	color:#000;
}
.upload-btn .el-button{
	transition: .1s;
  padding: 12px 20px;
  font-size: 14px;
  border-radius: 4px;
	background: #409EFF;
}
.upload-btn .el-upload-list.el-upload-list--text{
	display: none;
}
.el-input__suffix{
	right:initial;
	left:20px;
}
.goods-wrap .el-button--mini{
	font-size: 14px;
}
.el-table .warning-row {
  background: oldlace;
}
.el-table .success-row {
	background: #f0f9eb;
}
.el-table .yellow-row {
	background: #fcfde6;
}
.el-table .blue-row {
	background: #e6f7fd;
}
.el-dialog__header {
  background: #ebeef5;
}
.data-box .el-button:not(:first-child) {
  margin-right: 50px;
}
.title-box .el-radio__input.is-checked+.el-radio__label{
  color:#ff682c;
}
.title-box .el-radio__input.is-checked .el-radio__inner{
	border-color: #ff682c;
	background: #ff682c;
}
.send .el-form-item__content{
	margin-right: 85px;
}
.send .el-input{
	width: 70%;
}