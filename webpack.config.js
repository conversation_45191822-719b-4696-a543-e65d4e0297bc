var path = require("path");
var webpack = require("webpack");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const moment = require("moment");

const days = moment().format("YYYY-MM-DD");
const isProduction = process.env.NODE_ENV === 'production';
module.exports = {
  entry: "./src/main.js",
  output: {
    path: isProduction ? path.resolve(__dirname, `./dist/dist_${days}`) : path.resolve(__dirname, './dist'),
    filename: "build.js", // 使用内容哈希
    publicPath: isProduction ? `./dist_${days}/` : "/"
  },
  externals: {
    "electron-edge-js": "commonjs2 electron-edge-js"
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: ["vue-style-loader", "css-loader"]
      },
      {
        test: /\.vue$/,
        loader: "vue-loader",
        options: {
          loaders: {}
          // other vue-loader options go here
        }
      },
      {
        test: /\.js$/,
        loader: "babel-loader"
        // exclude: /node_modules/,
        // include: [resolve('src'), resolve('test'),resolve('/node_modules/_vue-socket.io')]
      },
      {
        test: /\.(png|jpg|gif|svg|jpeg|ico)$/,
        loader: "file-loader",
        options: {
          name: "[name].[ext]?[hash]"
        }
      },
      {
        test: /\.(eot|svg|ttf|woff|woff2)(\?\S*)?$/,
        loader: "file-loader"
      },
      {
        test: /\.less$/,
        loader: "less-loader"
      }
    ]
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      vue$: "vue/dist/vue.esm.js"
    },
    extensions: ["*", ".js", ".vue", ".json"]
  },
  devServer: {
    historyApiFallback: true,
    noInfo: true,
    overlay: true,
    open: false,
    hot: true,
    port: 8087,
    host: "127.0.0.1",
    progress: true
  },
  performance: {
    hints: false
  },
  // devtool: "#source-map",
  plugins: [
    isProduction ? new CleanWebpackPlugin({
      cleanOnceBeforeBuildPatterns: [
        path.join(__dirname, "dist/dist_*"), // 只清理 dist_xxx 子目录
        path.join(__dirname, "dist/*.js") // 清理可能存在的旧 JS 文件
      ]
    }) : null, // 每次构建前清理dist目录
    new HtmlWebpackPlugin({
      filename: isProduction ? path.resolve(__dirname, "./dist/index.html") : 'index.html', // HTML 输出到 dist 根目录
      template: "index.html",
      hash: true, // 为引入的JS/CSS添加哈希查询参数
      minify: isProduction ? {
        removeComments: true,
        collapseWhitespace: true,
        removeAttributeQuotes: true
      } : false
    }),
    new webpack.HotModuleReplacementPlugin()
  ].filter(Boolean)
};

if (process.env.NODE_ENV === "production") {
  module.exports.devtool = "#source-map";
  // http://vue-loader.vuejs.org/en/workflow/production.html
  module.exports.plugins = (module.exports.plugins || []).concat([
    new webpack.DefinePlugin({
      "process.env": {
        NODE_ENV: '"production"'
      }
    }),
    new webpack.optimize.UglifyJsPlugin({
      sourceMap: false,
      compress: {
        warnings: false
      }
    }),
    new webpack.LoaderOptionsPlugin({
      minimize: true
    })
  ]);
}

function resolve(dir) {
  return path.join(__dirname, "..", dir);
}
