const { autoUpdater } = require("electron-updater");
const path = require("path");
const { app, ipcMain } = require("electron");

const checkUpdate = () => {
  if (!app.isPackaged) {
    autoUpdater.forceDevUpdateConfig = true;
    autoUpdater.updateConfigPath = path.join(__dirname, "./../build/win-unpacked/resources/app-update.yml");
  }

  autoUpdater.setFeedURL({
    provider: "generic",
    // url: "http://127.0.0.1:7001/public/"
    url: "https://ros-api.mulazim.com/api/v1/win-update"
    // url: "https://ros-api.d.almas.biz/api/v1/win-update"
  });
  autoUpdater.autoDownload = false; // 自动下载
  autoUpdater.autoInstallOnAppQuit = true; // 应用退出后自动安装
  autoUpdater.disableWebInstaller = true;   // 下载提示要求设置为true
  autoUpdater.disableDifferentialDownload = true; // 关闭差异升级功能

  // 监听渲染进程的 check 事件，触发检测更新
  ipcMain.on("check-update", async () => {
    // 检测是否有更新包并通知
    autoUpdater.checkForUpdatesAndNotify();
  });

  // 监听错误
  autoUpdater.on("error", () => {

  })

  // 监听渲染进程的 download 事件，触发下载更新包
  ipcMain.on("update-download", (event, type) => {
    if (type == "download") {
      autoUpdater.downloadUpdate(); // 开始下载
    } else if (type == "install") {
      autoUpdater.quitAndInstall();
    }
  });

  // 监听渲染进程的 install 事件，触发退出应用并安装
  // ipcMain.handle("install", () => );

  autoUpdater.on("update-available", info => {
    // console.log("有新版本需要更新", info);
    global.window.send("update-toast", "update-available", info);
  });

  autoUpdater.on("download-progress", prog => {
    // console.log("prog", prog);
    global.window.send("update-toast", "download-progress", prog);
  });
  autoUpdater.on("update-downloaded", info => {
    console.log("下载完成");
    global.window.send("update-toast", "downloaded", info);
  });
};

module.exports = checkUpdate;
