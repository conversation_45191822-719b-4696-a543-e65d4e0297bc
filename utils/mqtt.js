// 开源sdk地址：https://github.com/mqttjs/MQTT.js
const mqtt = require("mqtt");
const CryptoJS = require("crypto-js");
const { ipcMain, app } = require("electron");
const { execFile } = require("child_process");
const fs = require("fs");
const path = require("path");
const queueFile = app.isPackaged
  ? path.resolve(__dirname, "./../../config/printQueue.json")
  : path.resolve(__dirname, "./../resources/config/printQueue.json"); // 保存打印队列的文件路径
const { v4 } = require("uuid");
const { logWarn, logInfo, logSuccess } = require("./log.js")
const moment = require("moment");


//账号 accesskey，从账号系统控制台获取
//阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维。
//强烈建议不要把AccessKey ID和AccessKey Secret保存到工程代码里，否则可能导致AccessKey泄露，威胁您账号下所有资源的安全。
//可以把AccessKey ID和AccessKey Secret保存在环境变量。运行本代码示例之前，请先配置环境变量MQTT_AK_ENV和MQTT_SK_ENV
//例如：export MQTT_AK_ENV=access_key_id
//     export MQTT_SK_ENV=access_key_secret
//需要将access_key_id替换为已准备好的AccessKey ID，access_key_secret替换为AccessKey Secret
let accessKey = "LTAI5tFURyzvge7PATf9WjJU";
// let accessKey = '0fb0d54848e9a5f6';
//账号的的 SecretKey，在阿里云控制台查看
let secretKey = "******************************";
// let secretKey='JTtcMNS14N1QZ9A3QJ9B0k7pBE7IH3GEIibqxmVcg2aGA'

let instanceId = "post-cn-i7m2gijq00t";

let client = null;

// 打印状态数组
const actionArr = [
  "提交订单",
  "加菜",
  "退菜",
  "全单退菜",
  "订单换台",
  "拼单",
  "已结账",
  "预结账",
  "催单",
  "会员充值",
  "营业统计",
  "美食统计",
  "换班",
  "测试"
];

let lang = 1; // 打印语言
let merchant_no = ""; // 商户号
let printerTimer = null; // 打印定时器
let printKey = true;
let manualClose = false;
let rejisteryTimer = null;
let disablePrint = false;

// 监听发送打印时间
ipcMain.on("record-print-time", (event, data) => {
  printTimes.push(data);
});

const printTimes = [];

// 退出登录后mqtt关闭连接
ipcMain.handle("close-mqtt", async () => {
  closeMqtt();
});

function closeMqtt() {
  if (client != null) {
    manualClose = true;
    client.end();
    client = null;
    clearInterval(rejisteryTimer);
    rejisteryTimer = null;
    global.window && global.window.send && global.window.send("connected-mqtt", false); // 发送连接状态
  }
}

const connectMQTT = async (event, obj) => {
  // if (client != null) {
  //   client.end();
  //   client = null;
  //   clearInterval(rejisteryTimer);
  //   rejisteryTimer = null;
  // }
  closeMqtt();

  manualClose = false;
  lang = obj.lang;
  merchant_no = obj.merchantNo;
  disablePrint = obj.disablePrint;

  let clientId = "GID_0001@@@" + obj.uuid;
  let username = "Signature|" + accessKey + "|" + instanceId;
  let password = CryptoJS.HmacSHA1(clientId, secretKey).toString(
    CryptoJS.enc.Base64
  );
  let topic = "print_topic";
  let registerTopic = topic + "/p2p/GID_0001@@@9998";
  if (obj.url) {
      console.log("url", obj.url.includes("mulazim.com"));
      if (obj.url.includes("mulazim.com")) {
        registerTopic = topic + "/p2p/GID_0001@@@9998";
      } else {
        registerTopic = topic + "/p2p/GID_0001@@@9991";
      }
  }

  console.log("registerTopic", registerTopic);

  let options = {
    username: username,
    password: password,
    clientId: clientId,
    keepalive: 90,
    connectTimeout: 3000
  };

  //tls安全连接："tls://host:8883"
  client = mqtt.connect(
    "tcp://post-cn-i7m2gijq00t.mqtt.aliyuncs.com:1883",
    options
  );

  // 连接成功监听函数
  client.on("connect", function() {
    console.log("connected mqtt");

    try {
      // 注册mqtt的topic
      client.subscribe(topic, { qos: 1 }, function(err, res) {
        console.log("subscribe success", err, res);
        if (err == null) {
          client.publish(
            registerTopic,
            JSON.stringify({
              action: "register",
              merchantNo: obj.merchantNo,
              uuid: obj.uuid
            })
          );
          global.window && global.window.send && global.window.send("connected-mqtt", true); // 发送连接状态
          return;
        }
      });
    } catch (error) {

    }

    clearInterval(printerTimer);
    printerTimer = null;
    processQueue();
  });

  // 接收消息监听函数
  client.on("message", function(topic, message) {
    const data = JSON.parse(message);
    console.log(
      "topic:" +
        topic +
        ` time -> ${new Date().getMinutes()}:${new Date().getSeconds()}` +
        " msg:",
      JSON.stringify(data)
    );
    watchMQTT(data);
    global.window && global.window.send && global.window.send("mqtt-message", data);
    // if (disablePrint == "true") return;
    // data.acceptTime = moment().format("YYYY-MM-DD HH:mm:ss.SSS");
    // printMessage(data);
  });

  // 连接断开监听函数
  client.on("close", function() {
    console.log("close mqtt");
    if (client != null) {
      client.end();
      global.window && global.window.send && global.window.send("connected-mqtt", false); // 发送连接状态
    }
    if (!manualClose) {
      clearInterval(rejisteryTimer);
      rejisteryTimer = null;
      global.window.send("re-connected-mqtt"); // 发送连接状态
    }
  });

  // 定时注册
  rejisteryTimer = setInterval(function() {
    client.publish(
      registerTopic,
      JSON.stringify({
        action: "register",
        merchantNo: obj.merchantNo,
        uuid: obj.uuid
      })
    );
    console.log("run register", registerTopic, new Date().toISOString());
  }, 1000 * 60 * 10);
};

// 登录后连接mqtt
ipcMain.handle("connect-mqtt", connectMQTT);

// 获取mqtt的数据并判断订单是否需要打印
const printMessage = async message => {
  if (actionArr.includes(message.action)) {
    // 订单相关部分特殊处理
    if (
      message.action == "提交订单" ||
      message.action == "加菜" ||
      message.action == "退菜" ||
      message.action == "全单退菜" ||
      message.action == "催单"
    ) {
      let list = [];
      if (message.order.order_details && message.order.order_details[0] && message.order.order_details[0]?.many_printers && message.order.order_details[0]?.many_printers[0] && message.order.order_details[0]?.many_printers[0]?.split_print == 1) {
        // console.log("food print");
        for (let i = 0; i < message.order.order_details.length; i++) {
          const item = message.order.order_details[i];
          if (item.many_printers && item.many_printers.length == 0) continue;
          // 云打印不打印
          if (item.many_printers[0].internet_type == 1) continue

          const data = {
            ...message,
            order: {
              ...message.order,
              printers: { ...item.many_printers[0], lang },
              order_details: [item]
            }
          };

          for(let i = 0; i < printTimes.length; i++) {
            if (printTimes[i].action == message.action && printTimes[i].orderDetailId == item.id) {
              data[printTimes[i].type] = printTimes[i].time
            }
          }

          if (message.action == "全单退菜") {
            if (item.canceled_foods.length) {
              list.push(data);
            }
          } else {
            list.push(data);
          }
        }
      } else {
        const printObj = {};
        for (let i = 0; i < message.order.order_details.length; i++) {
          const item = message.order.order_details[i];
          if (!item.many_printers || item.many_printers.length == 0) continue;
          if (item.many_printers[0].internet_type == 1)continue;

          if (printObj[item.many_printers[0].printer_ip]) {
            printObj[item.many_printers[0].printer_ip].order.order_details.push(
              item
            );
          } else {
            printObj[item.many_printers[0].printer_ip] = {
              ...message,
              order: {
                ...message.order,
                printers: { ...item.many_printers[0], lang },
                order_details: [item]
              }
            };

            for(let i = 0; i < printTimes.length; i++) {
              if (printTimes[i].action == message.action && printTimes[i].orderDetailId == item.id) {
                printObj[item.many_printers[0].printer_ip][printTimes[i].type] = printTimes[i].time
              }
            }
          }
        }

        for (const item in printObj) {
          list.push(printObj[item]);
          // let res = await execFiles(printObj[item]);
          // if (res == "fail") {
          //   // 打印失败并添加队列
          //   addQueueList(printObj[item]);
          // }
        }
      }

      addQueueList(list, "list");
    } else {
      let data = {};
      if (message.action == "订单换台" || message.action == "拼单") {
        if (message.order.printers[0].internet_type == 1) {
          return;
        }
        data = {
          ...message,
          order: {
            ...message.order,
            printers: [{ ...message.order.printers[0], lang }]
          }
        };
        for(let i = 0; i < printTimes.length; i++) {
          if (printTimes[i].action == message.action && printTimes[i].orderDetailId == item.id) {
            data[printTimes[i].type] = printTimes[i].time
          }
        }
      } else {
        if (message.order.printers.internet_type == 1) {
          return;
        }
        data = {
          ...message,
          order: {
            ...message.order,
            printers: { ...message.order.printers, lang }
          }
        };
        for(let i = 0; i < printTimes.length; i++) {
          if (printTimes[i].action == message.action && printTimes[i].orderDetailId == item.id) {
            data[printTimes[i].type] = printTimes[i].time
          }
        }
      }
      addQueueList(data);
    }

    clearInterval(printerTimer);
    printerTimer = null;
    processQueue();
  }
};

// 发送打印机
const execFiles = (message, type) => {
  return new Promise((resolve, reject) => {
    const filePath = app.isPackaged
      ? path.resolve(__dirname, "./../../dll/AlmasPrinter/AlmasPrinter.exe")
      : path.resolve(
          __dirname,
          "./../resources/dll/AlmasPrinter/AlmasPrinter.exe"
        );
    if (message.action !== "测试") {
      message.startPrint = moment().format("YYYY-MM-DD HH:mm:ss.SSS")
      logInfo({param: message, message: "发送打印机"}, "printer")
    }
    execFile(filePath, [JSON.stringify(message)], (error, stdout, stderr) => {
      console.log("stdout -> ", stdout);
      console.log("stderr -> ", stderr);

      if (stdout.indexOf("elfa connect error") > -1) {
        global.window.send("print-message", "rePrinter");
        if (message.action !== "测试") {
          message.successTime = moment().format("YYYY-MM-DD HH:mm:ss.SSS")
          logWarn({param: message, message: "找不到打印机"}, "printer")
        }
        resolve("fail");
        return;
      }

      // 打印成功
      if (stdout.indexOf("success print") > -1) {
        console.log("打印成功");
        if (message.action !== "测试") {
          message.successTime = moment().format("YYYY-MM-DD HH:mm:ss.SSS")
          logSuccess({param: message, message: "打印成功"}, "printer")
        }
        resolve("success");
        return;
      }

      if (message.action !== "测试") {
        message.successTime = moment().format("YYYY-MM-DD HH:mm:ss.SSS")
        logWarn({param: message, message: `打印失败报错内容 -> ${JSON.stringify(error)}`}, "printer")
      }
      resolve("fail");
      if (error) {
        console.log("execFiles error -> ", error);
      }

      if (stdout.indexOf("start print") > -1) {
      }
    });
  });
};

// 打印失败后保存队列文件里
const addQueueList = (message, type) => {
  if (true){
    return
  }
  let queue = {};
  try {
    queue = JSON.parse(fs.readFileSync(queueFile));
  } catch (error) {
    console.log("addQueueList error -> ", error);
  }

  if (type == "list") {
    for (let i = 0; i < message.length; i++) {
      const merchantNo = message[i].merchant_no;
      const data = {
        queueData: message[i],
        time: new Date().getTime(),
        id: v4()
      };
      if (queue[merchantNo] && queue[merchantNo].length >= 0) {
        queue[merchantNo].push(data);
      } else {
        queue[merchantNo] = [data];
      }
    }
  } else {
    const merchantNo = message.merchant_no;
    const data = { queueData: message, time: new Date().getTime(), id: v4() };
    if (queue[merchantNo] && queue[merchantNo].length >= 0) {
      queue[merchantNo].push(data);
    } else {
      queue[merchantNo] = [data];
    }
  }

  fs.writeFileSync(queueFile, JSON.stringify(queue));
};

let tempKey = false;
// 从队列中获取打印任务并发送给打印机
const processQueue = async () => {
  if (true){
    return
  }
  console.log("printKey", printKey);
  if (!printKey) {
    tempKey = true;
    return;
  }
  printKey = false;

  let queue = {};
  try {
    queue = JSON.parse(fs.readFileSync(queueFile));
  } catch (err) {
    console.error("processQueue error -> ", err);
  }

  console.log("queue[merchant_no]", queue[merchant_no]?.length);
  if (queue[merchant_no] && queue[merchant_no].length > 0) {
    const currentData = new Date().getTime();
    const twoHours = 1000 * 60 * 60 * 2; // 两个小时后过期

    for (let i = 0; i < queue[merchant_no].length; i++) {
      const printJob = queue[merchant_no][i];
      // 判断打印数据是否过期
      if (currentData - printJob.time >= twoHours) {
        console.log("打印数据过期");

        try {
          const list = JSON.parse(fs.readFileSync(queueFile));
          for (let j = 0; j < list[merchant_no].length; j++) {
            console.log();
            if (printJob.id == list[merchant_no][j].id) {
              list[merchant_no].splice(j, 1);
            }
          }
          fs.writeFileSync(queueFile, JSON.stringify(list));
        } catch (err) {
          console.error("processQueue error -> ", err);
        }
        continue;
      }

      console.log("发送打印机");
      // 发送打印任务给打印机
      const res = await execFiles(printJob.queueData, "queue");
      if (res == "success") {
        try {
          const list = JSON.parse(fs.readFileSync(queueFile));
          for (let j = 0; j < list[merchant_no].length; j++) {
            if (printJob.id == list[merchant_no][j].id) {
              list[merchant_no].splice(j, 1);
            }
          }
          fs.writeFileSync(queueFile, JSON.stringify(list));
        } catch (err) {
          console.error("processQueue error -> ", err);
        }
        continue;
      }

      console.log("队列打印失败");
    }

    printKey = true;
    if (tempKey) {
      clearInterval(printerTimer);
      printerTimer = null;
      processQueue();
      tempKey = false;
    } else {
      clearInterval(printerTimer);
      printerTimer = null;
      printerTimer = setInterval(processQueue, 1000 * 30);
    }
  } else {
    printKey = true;
    clearInterval(printerTimer);
  }
};

// 从页面上的数据发送打印
ipcMain.on("custom-print", async (event, data) => {
  if (!data.order.printers.printer_ip || data.order.printers.internet_type == 1) {
    return;
  }
  if (disablePrint == "true") return;
  addQueueList(data);
  processQueue();
});

// 监听 mqtt 更新桌子信息
const watchMQTT = data => {
  console.log("watchMqtt ->");
  if (
    data.action == "开台" ||
    data.action == "订单已取消" ||
    data.action === "提交订单" ||
    data.action === "已结账" ||
    data.action === "退菜" ||
    data.action === "全单退菜" ||
    data.action === "订单换台" ||
    data.action === "拼单" ||
    data.action === "加菜"
  ) {
    global.window.send("watch-mqtt", data);
  }
};

