// 监听来自主进程的消息
const edge = require("electron-edge-js");

process.on('message', (val) => {
  if (val.type === "byte"){
    printByte(val)
  }else if (val.type === "text"){
    printText(val)
  }
});

const edgeByte = edge.func({
  assemblyFile: "./resources/dll/AlmasPrinter/AlmasPrinterDll.dll",
  typeName: "usb_final.UsbPrint",
  methodName: "usbPrintByte"
});

const edgeText = edge.func({
  assemblyFile: "./resources/dll/AlmasPrinter/AlmasPrinterDll.dll",
  typeName: "AlmasPrinterDll.Print",
  methodName: "printText"
});


function printByte(val){
  edgeByte(val.json, (err, result) => {
    console.log("dll data", result)
    // 向主进程发送消息
    if (!result || result.indexOf("error") != -1) {
      console.log("dll error", err);
      process.send({ success: false , data:val.data, printer_ip:val.data.printers.printer_ip, err: result });
    }else {
      process.send({ success: true ,  printer_ip:val.data.printers.printer_ip, data: val.data});
    }
  });
}

function printText(val){
  edgeText(val.json, (err, result) => {
    console.log("dll data", result)
    // 向主进程发送消息
    if (!result || result.indexOf("error") != -1) {
      console.log("dll error", err);
      process.send({ success: false , data:val.data, printer_ip:val.data.printers.printer_ip, err: result  });
    }else {
      process.send({ success: true, printer_ip:val.data.printers.printer_ip, data: val.data  });
    }
  });
}

