const logger = require("electron-log");
const moment = require("moment");
const ALY = require("aliyun-sdk");
const { app, ipcMain} = require("electron");
const os = require("os");

logger.transports.file.level = "debug";
logger.transports.console.level = false;
logger.transports.file.maxSize = 1024 * 1024 * 10; // 10M
logger.transports.file.format =
  "[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}]{scope} -> {text}";
ipcMain.on("slsLogSend",(event,data)=>{

  if (data && data.content && !data.content.message){
    data.content.message = "no_message"
  }

  if (data.level_name === "commonInfo") {
    info(data.json)
  } else if (data.level_name === "warn"){
    warn(data.json,"printer")
  } else if (data.level_name === "success"){
    success(data.json, "printer")
  } else {
    info(data.json, "printer")
  }
})


const printer = logger.scope("printer");

logger.success = param => {
  logger.info(param);
};

printer.success = param => {
  printer.info(param);
};

const sls = new ALY.SLS({
  // 本示例从环境变量中获取AccessKey ID和AccessKey Secret。
  accessKeyId: "LTAI5tQMsiAcuKGkEp6ctknb",
  secretAccessKey: "******************************",
  //日志服务的域名。此处以杭州为例，其它地域请根据实际情况填写。
  endpoint: "http://cn-beijing.log.aliyuncs.com",
  //SDK版本号，固定值。
  apiVersion: "2015-06-01"
});

function formatParam(param) {
  return {
    action: param?.action,
    merchant_no: param?.merchant_no,
    time: param?.time,
    table_id: param?.table_id,
    user: param?.order?.user,
    table_name_ug: param?.order?.table_name_ug,
    table_name_zh: param?.order?.table_name_zh,
    order_no: param?.order?.order_no,
    merchant: param?.order?.merchant,
    printers: param?.order?.printers,
    price: param?.order?.price,
    order_details: param?.order?.order_details
  };
}

function writeLog(data) {
  const param = {
    projectName: "zhihuicanting",
    logStoreName: "cashier-windows-client",
    logGroup: {
      // 必选，写入的日志数据。
      logs: [
        {
          time: Math.floor(new Date().getTime() / 1000),
          contents: [
            {
              key: "channel",
              value: app.isPackaged ? "production" : "development"
            },
            { key: "merchant_no", value: data?.content?.param?.merchant_no },
            { key: "version_name", value: app.getVersion() },
            { key: "platform", value: os.platform() },
            { key: "platform_code", value: os.release() },
            { key: "level_name", value: data?.level_name },
            { key: "message", value: data?.content?.message },
            { key: "content", value: JSON.stringify(formatParam(data?.content?.param)) }
          ]
        }
      ],
      topic: "pirnter",
      source: "windows"
    }
  };
  if (data?.content?.param?.error) {
    let error = "";
    if (typeof data.content.param.error == 'object') {
      error = JSON.stringify(data?.content?.param?.error.stack);
    } else if (typeof data.content.param.error =='string') {
      error = data?.content?.param?.error
    }
    param.logGroup.logs[0].contents.push({
      key: "error",
      value: error
    });
  }
  sls.putLogs(param, function(err, res) {
    if (err) {
      console.log("sls log error -> ", err)
      logger.transports.file.resolvePathFn = () =>
        `${app.getPath("userData")}/logs/${
          data.content?.param?.merchant_no
        }-${moment().format("YYYY")}/${moment().format("YYYY-MM-DD")}.log`;
      printer[data.level_name](
        JSON.stringify({
          dataTime: moment().format("YYYY-MM-DD HH:mm:ss"),
          content: data.content
        })
      );
    } else {
      console.log("写入日志成功");
    }
  });
}

function CommonWriteLog(data) {
  const param = {
    projectName: "zhihuicanting",
    logStoreName: "cashier-windows-client",
    logGroup: {
      // 必选，写入的日志数据。
      logs: [
        {
          time: Math.floor(new Date().getTime() / 1000),
          contents: [
            {
              key: "channel",
              value: app.isPackaged ? "production" : "development"
            },
            { key: "level_name", value: data?.level_name },
            { key: "message", value: data?.content?.message },
            { key: "version_name", value: app.getVersion() },
          ]
        }
      ],
      topic: "common",
      source: "windows"
    }
  };

  sls.putLogs(param, function(err, res) {
    if (err) {
      console.log("sls log error -> ", err)
      logger.transports.file.resolvePathFn = () =>
        `${app.getPath("userData")}/logs/${
          data.content?.param?.merchant_no
        }-${moment().format("YYYY")}/${moment().format("YYYY-MM-DD")}.log`;
      printer[data.level_name](
        JSON.stringify({
          dataTime: moment().format("YYYY-MM-DD HH:mm:ss"),
          content: data.content
        })
      );
    } else {
      console.log("写入日志成功");
    }
  });
}

function info(param, type = "default") {
  if (type == "printer") {
    writeLog({
      level_name: "info",
      message_type: "printer",
      content: param
    });
  } else {
    CommonWriteLog({
      level_name: "info",
      content: param
    })
  }
}
function success(param, type = "default") {
  if (type == "printer") {
    writeLog({
      level_name: "success",
      message_type: "printer",
      content: param
    });
  } else {
    CommonWriteLog({
      level_name: "success",
      content: param
    })
  }
}
function warn(param, type = "default") {
  if (type == "printer") {
    writeLog({
      level_name: "warn",
      message_type: "printer",
      content: param
    });
  } else {
    CommonWriteLog({
      level_name: "warn",
      content: param
    })
  }
}
function error(param, type = "default") {
  if (type == "printer") {
    writeLog({
      level_name: "error",
      message_type: "printer",
      content: param
    });
  } else {
    CommonWriteLog({
      level_name: "error",
      content: param
    })
  }
}
function debug(param, type = "default") {
  if (type == "printer") {
    writeLog({
      level_name: "debug",
      message_type: "printer",
      content: param
    });
  } else {
    CommonWriteLog({
      level_name: "debug",
      content: param
    })
  }
}
function verbose(param, type = "default") {
  if (type == "printer") {
    writeLog({
      level_name: "verbose",
      message_type: "printer",
      content: param
    });
  } else {
    CommonWriteLog({
      level_name: "verbose",
      content: param
    })
  }
}
function silly(param, type = "default") {
  if (type == "printer") {
    writeLog({
      level_name: "silly",
      message_type: "printer",
      content: param
    });
  } else {
    CommonWriteLog({
      level_name: "silly",
      content: param
    })
  }
}

module.exports = {
  logInfo: info,
  logSuccess: success,
  logWarn: warn,
  logError: error,
  logDebug: debug,
  logVerbose: verbose,
  logSilly: silly
};
