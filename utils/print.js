const {ipcMain} = require("electron");
const escpos = require('escpos');
escpos.Network = require('escpos-network')
const net = require('net');
const { logWarn, logInfo, logSuccess } = require("./log.js")

const config = {
  devise:[],
}
const queqe = []

ipcMain.on("printTask", (event, data) => {
  queqe.push(data)
  scan()
})

const { fork } = require('child_process');
const path = require('path');

// 创建一个子进程
const usbProcess = fork(path.join(__dirname, './worker-usb-process.js'));
// 监听来自子进程的消息
usbProcess.on('message', (msg) => {
  printState(msg.printer_ip, false)
  if (!msg.success){
    printFailed(msg.data)
    logWarn({param: {...msg.data.json, error: msg.err}, message: "c# 打印失败"}, "printer")
  } else {
    logSuccess({param: msg.data.json, message: "打印成功"}, "printer")
  }
  scan()
});

// 监听子进程的关闭事件
usbProcess.on('close', (code) => {
  console.log(`usbProcess process exited with code ${code}`);
});

function scan() {
  if (queqe.length !== 0) {
    const data = queqe.shift()

    try {
      if (data.printers.beautiful_print == 0 && !data.text_byte){
        textPrint(data)
      }else {
        let buffer = null;
        if (data.test === 1) {
          buffer = testByte()
        }else if (data.text_byte) {
          buffer = new Buffer.from(data.text_byte)
        }else {
          buffer = rasterImage(data)
        }
        if(data.printers.printer_ip.indexOf(":") === -1){
          printUSB(data,buffer)
        }else{
          printSocket(data,buffer)
        }
      }
    }catch (e) {
      console.log(e)
      logWarn({param: {...data.json, error: e}, message: "打印失败"}, "printer")
      printFailed(data);
    }
  }
}

function textPrint(data){
  if (printState(data.printers.printer_ip)) {
    queqe.push(data)
    console.log("USB 打印拒绝: ")
    return
  }
  console.log("USB 打印指令传送 ")

  printState(data.printers.printer_ip, true)

  const json = JSON.stringify(data.json)
  // 向子进程发送消息
  usbProcess.send({json:json, data:data, type: "text"});
  logInfo({param: data.json, message: "发送打印机"}, "printer")
}

function printUSB(data,buffer){
  if (printState(data.printers.printer_ip)) {
    queqe.push(data)
    console.log("USB 打印拒绝: ")
    return
  }
  printState(data.printers.printer_ip, true)

  console.log("USB 打印指令传送 ")
  data.image = null
  data.text_byte = null
  const json = JSON.stringify({image: buffer.toString('base64'),printKey:data.printers.printer_ip})
  // 向子进程发送消息
  usbProcess.send({json:json, data:data, type: "byte"});
  logInfo({param: data.json, message: "发送打印机"}, "printer")
}

function printSocket(data,buffer) {

  if (printState(data.printers.printer_ip)) {
    queqe.push(data)
    console.log("打印拒绝: ")
    return
  }
  logInfo({param: data.json, message: "发送打印机"}, "printer")
  data.image = null
  data.text_byte = null
  printState(data.printers.printer_ip, true)

  // 创建一个客户端
  let client = new net.Socket();
  const closed = ()=>{
    printState(data.printers.printer_ip, false)
    scan()
  }

  // 监听连接错误
  client.on('error', (err) => {
    printFailed(data);
    logWarn({param: {...data.json, error: err}, message: "node打印失败"}, "printer")
    closed()
  });
  // 监听连接关闭
  client.on('close', () => {
    closed()
  });
  // 监听数据接收
  client.on('data', (data) => {
    console.log('Received:', data.toString());
  });

  const ip = data.printers.printer_ip.split(":")[0]
  const port = data.printers.printer_ip.split(":")[1]

  // 连接到服务器
  client.connect(port, ip, () => {
    try {
      if (client.write(buffer)) {
        // 立即关闭连接
        client.end();
        logInfo({param: data.json, message: "打印成功"}, "printer")
      } else {
        // 如果写入缓冲区是满的，则等待 drain 事件
        client.once('drain', () => {
          client.end();
          logSuccess({param: data.json, message: "打印成功"}, "printer")
        });
      }
    } catch (err) {
      client.end(); // Ensure we end the connection even on error
      logWarn({param: {...data.json, error: err}, message: "node打印失败"}, "printer")
    }
  });


}


function rasterImage(data){
  let buffer = new escpos.Printer(null).align('ct').raster(new escpos.Image(data.image), 'NORMAL').cut().buffer;

  if (data.json.action == "提交订单" || data.json.action == "加菜" || data.json.action == "退菜" || data.json.action == "全单退菜" || data.json.action == "订单换台" || data.json.action == "拼单" || data.json.action == "催单") {
    if (data.printers && data.printers.enable_voice === 1){
      buffer.write('\x1b\x42\x02\x09')
    }
  }

  if (data.json.action == "已结账") {
    // const paidAt = new Date(data.json.order.paid_at);
    // if (isNaN(paidAt.getTime())) return false
    // const now = new Date();
    // const difference = now - paidAt;
    // const differenceInMinutes = difference / (1000 * 60);
    // if ( && differenceInMinutes <= 1)
    if (data.json && data.json.order && data.json.order.payment_type_id === 3){
      buffer.write('\x1b\x70\x01\xff\xff')
      console.log("收钱箱 开！")
    }
  }
  return buffer.flush()
}

function testByte(){
  return  new escpos.Printer(null, { encoding: "GB18030"})
    .font('a')
    .align('ct')
    .size(1,1)
    .text('测试打印')
    .print(' \n')
    .cut(3).buffer.flush()
}

function printState(ip, state) {
  if (!config.device) {
    config.device = [];
  }

  let device = config.device.find(d => d.ip === ip);

  if (!device) {
    device = { ip: ip, isPrint: false };
    config.device.push(device);
  }

  if (state !== undefined) {
    device.isPrint = state;
  }

  return device.isPrint;
}

function printFailed(data) {
  if(data.try===1){
    global.window.send("printFailed",data.json);
  }
}
