const {app, BrowserWindow, protocol, ipcMain} = require("electron")
const ChildProcess = require("child_process")
const Process = require("process")
const Path = require("path")
const FileSystem = require("fs").promises

const scheme = "mulazim"
protocol.registerSchemesAsPrivileged([{
    scheme,
    privileges: {standard: true, secure: true, corsEnabled: false}
}])
const serviceProcess = ChildProcess.exec(`"${Path.dirname(__dirname)}/PrinterService/Printools.exe"`, {}, (error, stdOut, stdError) => {
    if (error) console.error(error)
})
const partition = "persist:mulazim"
function createMainWindow() {
    const window = new BrowserWindow({
        autoHideMenuBar: true,
        frame: true,
        fullscreen: true,
        webPreferences: {
            preload: Path.resolve(__dirname, "preload.js")
        }
    })
    
    window.loadURL(`${scheme}://ros/index.html`)
    // window.loadURL(`http://0.0.0.0:9000`)
    const canBeOpenDevTools = Process.argv.filter(item => item == "--debug").length > 0
    if (canBeOpenDevTools) window.webContents.openDevTools()

    window.on("close", () => {
        serviceProcess.kill()
        console.log("Window is now closing...")
    })
    app.on("quit", () => {
        window.close()
    })
    ipcMain.on("closeWindow", (event, args) => {
        window.close()
    })
}
app.on("ready", () => {
    //For page redirection, for example: /login should be under /index.html
    protocol.registerFileProtocol(scheme, (request, callback) => {
        let url = request.url.replaceAll(`${scheme}://ros/`, "")
        const searchResult = url.search(/.+\..+$/)
        if (searchResult == -1) url = "index.html"
        const local = Path.normalize(`${__dirname}/${url}`)
        const extensionName = Path.extname(local)

        callback({
            path: local,
        })
    })
    createMainWindow()
});
