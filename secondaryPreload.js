const { contextBridge, ipc<PERSON><PERSON><PERSON>, webFrame } = require("electron");

contextBridge.exposeInMainWorld("electronAPI", {
  showPayQrCodeSecondary: (fn) => ipcRenderer.on("show-pay-qrcode-secondary", fn),
  closePayQrCodeSecondary: (fn) => ipcRenderer.on("close-pay-qrcode-secondary", fn),
  showRechargePayQrCodeSecondary: (fn) => ipcRenderer.on("show-recharge-pay-qrcode-secondary", fn),
  closeRechargePayQrCodeSecondary: (fn) => ipcRenderer.on("close-recharge-pay-qrcode-secondary", fn)
});

ipcRenderer.on("set-adaption", (event, scaleFactor) => {
  var devInnerHeight = 1080.0; // 开发时的InnerHeight
  var devDevicePixelRatio = 1.0; // 开发时的devicepixelratio
  var devScaleFactor = 1.1; // 开发时的ScaleFactor
  var zoomFactor =
    (window.innerHeight / devInnerHeight) *
    (window.devicePixelRatio / devDevicePixelRatio) *
    (devScaleFactor / scaleFactor);
    console.log("zoomFctor secondary", zoomFactor)
  webFrame.setZoomFactor(zoomFactor);
});
