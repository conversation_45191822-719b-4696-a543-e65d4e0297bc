{"name": "mulazim-cashier", "description": "mulazim cashier", "version": "1.1.2", "author": "Almas", "license": "MIT", "private": true, "main": "main.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules", "electron": "chcp 65001 && electron .", "run-with-electron": "electron electron.js", "electron-build": "chcp 65001 && electron-builder --win --x64 --ia32"}, "build": {"productName": "Mulazim收银端", "appId": "mulazim.cashier.electron", "directories": {"output": "build"}, "publish": [{"provider": "generic", "channel": "latest", "url": "http://127.0.0.1/"}], "nsis": {"oneClick": false, "perMachine": true, "allowElevation": true, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Mulazim收银端", "include": "installer.nsh"}, "extraResources": [{"from": "./resources/", "to": "./"}], "win": {"icon": "./static/images/logo.png", "requestedExecutionLevel": "requireAdministrator", "target": ["nsis"]}, "mac": {"target": ["dmg", "zip"]}}, "dependencies": {"aliyun-sdk": "^1.12.10", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "babel-preset-es2015": "^6.24.1", "chart.js": "^2.9.4", "core-js": "^3.7.0", "crypto-js": "^4.2.0", "electron-edge-js": "^30.0.2", "electron-log": "^5.1.5", "electron-squirrel-startup": "^1.0.0", "electron-updater": "^6.1.8", "element-ui": "^2.3.7", "es6-promise": "^4.2.5", "escpos": "^3.0.0-alpha.6", "escpos-network": "^3.0.0-alpha.5", "get-pixels": "^3.3.3", "html2canvas": "^1.4.1", "http-proxy": "^1.18.1", "iconv-lite": "^0.6.3", "mqtt": "^3.0.0", "qrcode": "^1.5.3", "qrcode.vue": "^1.7.0", "socket.io-client": "^2.3.0", "uuid": "^9.0.1", "vue": "^2.5.11", "vue-awesome-swiper": "^3.1.3", "vue-chartjs": "^3.5.1", "vue-i18n": "^8.2.1", "vue-router": "^3.1.5", "vue-socket.io": "^3.0.7", "vuex": "^3.1.2"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "devDependencies": {"babel-core": "^6.26.0", "babel-loader": "^7.1.2", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.6.0", "babel-preset-stage-3": "^6.24.1", "clean-webpack-plugin": "^3.0.0", "cross-env": "^5.0.5", "css-loader": "^0.28.7", "electron": "^21.4.4", "electron-builder": "^24.13.3", "element-theme-chalk": "^2.4.11", "file-loader": "^1.1.4", "html-webpack-plugin": "^3.2.0", "less": "^3.0.4", "less-loader": "^4.1.0", "style-loader": "^0.21.0", "uglifyjs-webpack-plugin": "^1.0.0-rc.0", "vue-loader": "^13.0.5", "vue-template-compiler": "^2.4.4", "webpack": "^3.6.0", "webpack-dev-server": "^2.9.1"}}