<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="AlmasPrinterDll">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>5ff04d66-7625-43f5-8858-17d6e6dd774b</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="food">
      <Query>
        <DataSourceName>AlmasPrinterDll</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="beautiful_print">
          <DataField>beautiful_print</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="combo_info">
          <DataField>combo_info</DataField>
          <rd:TypeName>System.Object</rd:TypeName>
        </Field>
        <Field Name="cost_price">
          <DataField>cost_price</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="device_id">
          <DataField>device_id</DataField>
          <rd:TypeName>System.Object</rd:TypeName>
        </Field>
        <Field Name="enable_voice">
          <DataField>enable_voice</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="food_format_id">
          <DataField>food_format_id</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="food_id">
          <DataField>food_id</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="food_image">
          <DataField>food_image</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="food_name">
          <DataField>food_name</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="food_name_ug">
          <DataField>food_name_ug</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="food_name_zh">
          <DataField>food_name_zh</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="foods_count">
          <DataField>foods_count</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="id">
          <DataField>id</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="internet_type">
          <DataField>internet_type</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="is_combo">
          <DataField>is_combo</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="lang">
          <DataField>lang</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="order_id">
          <DataField>order_id</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="original_price">
          <DataField>original_price</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="price">
          <DataField>price</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="printer_alias">
          <DataField>printer_alias</DataField>
          <rd:TypeName>System.Object</rd:TypeName>
        </Field>
        <Field Name="printer_ip">
          <DataField>printer_ip</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="printer_name">
          <DataField>printer_name</DataField>
          <rd:TypeName>System.Object</rd:TypeName>
        </Field>
        <Field Name="printer_size">
          <DataField>printer_size</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="remarks">
          <DataField>remarks</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="split_print">
          <DataField>split_print</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="state">
          <DataField>state</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="total_price">
          <DataField>total_price</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="user_name">
          <DataField>user_name</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="vip_price">
          <DataField>vip_price</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>AlmasPrinterDll</rd:DataSetName>
        <rd:TableName>OrderDetail</rd:TableName>
        <rd:ObjectDataSourceType>AlmasPrinterDll.OrderDetail, AlmasPrinterDll, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix4">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.2cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.2cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>6.19999cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>1.2cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>سانى</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox10">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>باھاسى</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox10</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox17">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>جەمئىي</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox17</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>تاماق</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>2cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="foods_count">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!foods_count.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>foods_count</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="price">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>￥</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=Fields!price.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>price</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="total_price">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>￥</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=Fields!total_price.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>total_price</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="food_name_ug">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!food_name_ug.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!food_name_zh.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>food_name_ug</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                            <Direction>RTL</Direction>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="详细信息2" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>food</DataSetName>
            <Top>10.7cm</Top>
            <Left>0.2cm</Left>
            <Height>3.2cm</Height>
            <Width>14.59999cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <FontFamily>Microsoft YaHei Ug</FontFamily>
            </Style>
          </Tablix>
          <Textbox Name="title">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!Title.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>32pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>title</rd:DefaultName>
            <Top>0.1cm</Top>
            <Left>0.2cm</Left>
            <Height>1.5cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="TicketType">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!TicketType.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>32pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>TicketType</rd:DefaultName>
            <Top>1.8cm</Top>
            <Left>0.20001cm</Left>
            <Height>1.5cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox2">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئۈستەل : </Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!Table.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value xml:space="preserve"> </Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>3.5cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox3">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئادەم سانى : </Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!PeopleCount.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>4.9cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox4">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>زاكاز نومۇرى : </Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!OrderNumber.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>6.3cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox5">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>تاماق يازغۇچى : </Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!OrderHolder.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>7.7cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox6">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!CreateTime.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> : ۋاقتى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>9.1cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Line Name="Line1">
            <Top>10.5cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Line Name="Line2">
            <Top>14.1cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>9</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Textbox Name="Textbox7">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئومۇمىي سوممىسى : </Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!TotalPrice.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>￥</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>14.3cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>10</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox11">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئالاقىلىشىش نومۇرى : </Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>18pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!Tel.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>18pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>17.3cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>11</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Line Name="Line3">
            <Top>18.7cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>12</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Textbox Name="SoftVendor">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!SoftVendor.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>18pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>SoftVendor</rd:DefaultName>
            <Top>18.9cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>13</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="SoftTel">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!SoftTel.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>18pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>SoftTel</rd:DefaultName>
            <Top>20.3cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>14</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Line Name="Line4">
            <Top>15.7cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>15</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Textbox Name="Textbox12">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئادرىسى : </Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>18pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!Address.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>18pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value xml:space="preserve"> </Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>18pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>15.9cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.59999cm</Width>
            <ZIndex>16</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>22.04369cm</Height>
        <Style />
      </Body>
      <Width>15.1113cm</Width>
      <Page>
        <PageHeight>18cm</PageHeight>
        <PageWidth>15cm</PageWidth>
        <LeftMargin>0cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="Title">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter2</Prompt>
    </ReportParameter>
    <ReportParameter Name="TicketType">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter2</Prompt>
    </ReportParameter>
    <ReportParameter Name="Table">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="PeopleCount">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="OrderNumber">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="OrderHolder">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="CreateTime">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="TotalPrice">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="Address">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="Tel">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="SoftVendor">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="SoftTel">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>1</NumberOfColumns>
      <NumberOfRows>14</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>Title</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>TicketType</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>4</RowIndex>
          <ParameterName>Table</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>5</RowIndex>
          <ParameterName>PeopleCount</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>6</RowIndex>
          <ParameterName>OrderNumber</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>7</RowIndex>
          <ParameterName>OrderHolder</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>8</RowIndex>
          <ParameterName>CreateTime</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>9</RowIndex>
          <ParameterName>TotalPrice</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>10</RowIndex>
          <ParameterName>Address</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>11</RowIndex>
          <ParameterName>Tel</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>12</RowIndex>
          <ParameterName>SoftVendor</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>13</RowIndex>
          <ParameterName>SoftTel</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>1715ff29-62a3-4bf9-ab70-f5d6894dea8c</rd:ReportID>
</Report>