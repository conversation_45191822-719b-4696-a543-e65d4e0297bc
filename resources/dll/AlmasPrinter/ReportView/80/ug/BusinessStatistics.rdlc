<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="AlmasPrinterDll">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>5ff04d66-7625-43f5-8858-17d6e6dd774b</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="headCashInfo">
      <Query>
        <DataSourceName>AlmasPrinterDll</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="name">
          <DataField>name</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="value">
          <DataField>value</DataField>
          <rd:TypeName>System.Single</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>AlmasPrinterDll</rd:DataSetName>
        <rd:TableName>HeadCashInfo</rd:TableName>
        <rd:ObjectDataSourceType>AlmasPrinterDll.HeadCashInfo, AlmasPrinterDll, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="order_proportion">
      <Query>
        <DataSourceName>AlmasPrinterDll</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="amount">
          <DataField>amount</DataField>
          <rd:TypeName>System.Single</rd:TypeName>
        </Field>
        <Field Name="order_count">
          <DataField>order_count</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="payment_type_id">
          <DataField>payment_type_id</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="payment_type_name">
          <DataField>payment_type_name</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="real_received">
          <DataField>real_received</DataField>
          <rd:TypeName>System.Single</rd:TypeName>
        </Field>
        <Field Name="refunded_amount">
          <DataField>refunded_amount</DataField>
          <rd:TypeName>System.Single</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>AlmasPrinterDll</rd:DataSetName>
        <rd:TableName>OrderProportion</rd:TableName>
        <rd:ObjectDataSourceType>AlmasPrinterDll.OrderProportion, AlmasPrinterDll, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="TicketType">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!TicketType.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>32pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>TicketType</rd:DefaultName>
            <Top>1.8cm</Top>
            <Left>0.2cm</Left>
            <Height>1.5cm</Height>
            <Width>14.6cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Table">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!begin_date.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>3.5cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>8.625cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox2">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>باشلىنىش ۋاقتى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>3.5cm</Top>
            <Left>9.00139cm</Left>
            <Height>1.2cm</Height>
            <Width>5.79861cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Table2">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!end_date.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>4.9cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>8.625cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox3">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئاخىرلىشىش ۋاقتى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>4.9cm</Top>
            <Left>9.00139cm</Left>
            <Height>1.2cm</Height>
            <Width>5.79861cm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox4">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>بېسىلغان ۋاقتى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>6.3cm</Top>
            <Left>9.00139cm</Left>
            <Height>1.2cm</Height>
            <Width>5.79861cm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox1">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!Title.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>32pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>0.1cm</Top>
            <Left>0.2cm</Left>
            <Height>1.5cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Line Name="Line1">
            <Top>7.7cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Textbox Name="Table4">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>تىجارەت ئەھۋالى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>26pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>7.9cm</Top>
            <Left>0.2cm</Left>
            <Height>1.5cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Line Name="Line2">
            <Top>11cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>9</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Textbox Name="Table5">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئالغان پۇل تەپسىلاتى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>26pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>11.2cm</Top>
            <Left>0.2cm</Left>
            <Height>1.5cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>10</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Tablix Name="Tablix4">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>7.3cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>7.3cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>1.2cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="value1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>￥</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>24pt</FontSize>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=Fields!value.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>24pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>value1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="name">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!name.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>24pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>name</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="详细信息" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>headCashInfo</DataSetName>
            <Top>9.6cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>11</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Tablix Name="Tablix5">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>7.3cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>7.3cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>1.2cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="real_received">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>￥</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>24pt</FontSize>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=Fields!real_received.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>24pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>real_received</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="payment_type_name1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!payment_type_name.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>24pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>payment_type_name1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="详细信息1" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>order_proportion</DataSetName>
            <Top>12.9cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>12</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Textbox Name="Table6">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>زاكاز ئەھۋالى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>26pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>14.5cm</Top>
            <Left>0.2cm</Left>
            <Height>1.5cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>13</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Table7">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!customers_count.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>16.2cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>5.395cm</Width>
            <ZIndex>14</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox5">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئومۇمىي خېرىدار سانى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>16.2cm</Top>
            <Left>5.77139cm</Left>
            <Height>1.2cm</Height>
            <Width>9.02861cm</Width>
            <ZIndex>15</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Table8">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!order_count.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>17.6cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>5.395cm</Width>
            <ZIndex>16</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox6">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئومۇمىي زاكاز سانى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>17.6cm</Top>
            <Left>5.77139cm</Left>
            <Height>1.2cm</Height>
            <Width>9.02861cm</Width>
            <ZIndex>17</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Table9">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>￥</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!order_avg.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>19cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>5.395cm</Width>
            <ZIndex>18</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox7">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئوتتۇرچە زاكاز سوممىسى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>19cm</Top>
            <Left>5.77139cm</Left>
            <Height>1.2cm</Height>
            <Width>9.02861cm</Width>
            <ZIndex>19</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Table10">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>￥</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!customers_avg.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>20.4cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>5.395cm</Width>
            <ZIndex>20</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox8">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئوتتۇرچە خېرىدار سوممىسى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>20.4cm</Top>
            <Left>5.77139cm</Left>
            <Height>1.2cm</Height>
            <Width>9.02861cm</Width>
            <ZIndex>21</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Line Name="Line3">
            <Top>14.3cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>22</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Textbox Name="Table11">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئەزا ئەھۋالى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>26pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>22cm</Top>
            <Left>0.2cm</Left>
            <Height>1.5cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>23</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Table12">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>￥</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!vip_count.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>23.7cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>6.45333cm</Width>
            <ZIndex>24</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox9">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>يېڭى قوشۇلغان ئەزا سانى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>23.7cm</Top>
            <Left>6.82972cm</Left>
            <Height>1.2cm</Height>
            <Width>7.97028cm</Width>
            <ZIndex>25</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Table13">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>￥</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!vip_recharge_amount.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>25.1cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>6.45333cm</Width>
            <ZIndex>26</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox10">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>ئومۇمىي قاچىلانغان پۇل</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>25.1cm</Top>
            <Left>6.82972cm</Left>
            <Height>1.2cm</Height>
            <Width>7.97028cm</Width>
            <ZIndex>27</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Table14">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>￥</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!vip_recharge_present_amount.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>26.5cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>6.45333cm</Width>
            <ZIndex>28</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox11">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>سوۋغات قىلىنغان پۇل</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>26.5cm</Top>
            <Left>6.82972cm</Left>
            <Height>1.2cm</Height>
            <Width>7.97028cm</Width>
            <ZIndex>29</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Table15">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>￥</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!vip_total_Balance.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Table</rd:DefaultName>
            <Top>27.9cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>6.45333cm</Width>
            <ZIndex>30</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox12">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>نەق پۇل</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>27.9cm</Top>
            <Left>6.82972cm</Left>
            <Height>1.2cm</Height>
            <Width>7.97028cm</Width>
            <ZIndex>31</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Line Name="Line4">
            <Top>21.8cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>32</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Textbox Name="Textbox13">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!now_date.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>22pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox13</rd:DefaultName>
            <Top>6.3cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>8.625cm</Width>
            <ZIndex>33</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>29.61959cm</Height>
        <Style />
      </Body>
      <Width>15cm</Width>
      <Page>
        <PageHeight>31cm</PageHeight>
        <PageWidth>15cm</PageWidth>
        <LeftMargin>0cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="Title">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter2</Prompt>
    </ReportParameter>
    <ReportParameter Name="TicketType">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter2</Prompt>
    </ReportParameter>
    <ReportParameter Name="begin_date">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="end_date">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="now_date">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="customers_count">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="order_count">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="order_avg">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="customers_avg">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="vip_count">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="vip_recharge_amount">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="vip_recharge_present_amount">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="vip_total_Balance">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>1</NumberOfColumns>
      <NumberOfRows>15</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>Title</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>TicketType</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>4</RowIndex>
          <ParameterName>begin_date</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>5</RowIndex>
          <ParameterName>end_date</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>6</RowIndex>
          <ParameterName>now_date</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>7</RowIndex>
          <ParameterName>customers_count</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>8</RowIndex>
          <ParameterName>order_count</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>9</RowIndex>
          <ParameterName>order_avg</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>10</RowIndex>
          <ParameterName>customers_avg</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>11</RowIndex>
          <ParameterName>vip_count</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>12</RowIndex>
          <ParameterName>vip_recharge_amount</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>13</RowIndex>
          <ParameterName>vip_recharge_present_amount</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>14</RowIndex>
          <ParameterName>vip_total_Balance</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>1715ff29-62a3-4bf9-ab70-f5d6894dea8c</rd:ReportID>
</Report>