<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="AlmasPrinterDllModels">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>fd25ba6a-94aa-46f2-9623-8cab94278700</rd:DataSourceID>
    </DataSource>
    <DataSource Name="AlmasPrinterDllModels1">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>c4e46d49-d830-4a9d-a6e6-bcefa7cdf76c</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="foodTypeSet">
      <Query>
        <DataSourceName>AlmasPrinterDllModels</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Amount">
          <DataField>Amount</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="FoodNameUg">
          <DataField>FoodNameUg</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FoodNameZh">
          <DataField>FoodNameZh</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>AlmasPrinterDll.Models</rd:DataSetName>
        <rd:TableName>FoodTypeStatistic</rd:TableName>
        <rd:ObjectDataSourceType>AlmasPrinterDll.Models.FoodTypeStatistic, AlmasPrinterDll, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="foodStatisticSet">
      <Query>
        <DataSourceName>AlmasPrinterDllModels1</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Amount">
          <DataField>Amount</DataField>
          <rd:TypeName>System.Single</rd:TypeName>
        </Field>
        <Field Name="Count">
          <DataField>Count</DataField>
          <rd:TypeName>System.Single</rd:TypeName>
        </Field>
        <Field Name="NameUg">
          <DataField>NameUg</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NameZh">
          <DataField>NameZh</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TotalAmount">
          <DataField>TotalAmount</DataField>
          <rd:TypeName>System.Single</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>AlmasPrinterDll.Models</rd:DataSetName>
        <rd:TableName>FoodStatistic</rd:TableName>
        <rd:ObjectDataSourceType>AlmasPrinterDll.Models.FoodStatistic, AlmasPrinterDll, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="Textbox1">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!title.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>32pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>0.1cm</Top>
            <Left>0.2cm</Left>
            <Height>1.5cm</Height>
            <Width>14.6cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox2">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!begin_date.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> : باشلىنىش ۋاقتى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>3.7cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox3">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!end_date.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> : ئاخىرلىشىش ۋاقتى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>5.1cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox5">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!print_date.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value> : بېسىلغان ۋاقتى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>6.5cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox6">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>تاماق تۈرى سېتىلىشى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>24pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>8.1cm</Top>
            <Left>0.2cm</Left>
            <Height>1.3cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Line Name="Line1">
            <Top>3.5cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Textbox Name="Textbox4">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!store_name.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>32pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>1.8cm</Top>
            <Left>0.2cm</Left>
            <Height>1.5cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Line Name="Line3">
            <Top>7.9cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>4.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>9.7cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>1.2cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Amount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>￥</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=Fields!Amount.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Amount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="FoodNameUg">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FoodNameUg.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>FoodNameUg</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                            <Direction>RTL</Direction>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="详细信息" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>foodTypeSet</DataSetName>
            <Top>9.6cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Textbox Name="Textbox7">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>تاماق سېتىلىشى</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>24pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>11.2cm</Top>
            <Left>0.2cm</Left>
            <Height>1.3cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>9</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>RTL</Direction>
            </Style>
          </Textbox>
          <Tablix Name="Tablix3">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.95cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.95cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.95cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>6.75cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>1.2cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>سانى</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox16</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox18">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>باھاسى</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox18</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox20">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>جەمئىي</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox20</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox22">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>تاماق</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox22</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>1.2cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Count">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Count.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Count</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Amount1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>￥</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=Fields!Amount.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Amount1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TotalAmount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>￥</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=Fields!TotalAmount.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TotalAmount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="NameUg">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NameUg.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>NameUg</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="详细信息1" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>foodStatisticSet</DataSetName>
            <Top>12.7cm</Top>
            <Left>0.2cm</Left>
            <Height>2.4cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>10</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Line Name="Line4">
            <Top>11cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>11</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
        </ReportItems>
        <Height>15.5298cm</Height>
        <Style />
      </Body>
      <Width>15cm</Width>
      <Page>
        <PageHeight>13cm</PageHeight>
        <PageWidth>15cm</PageWidth>
        <LeftMargin>0cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="title">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="store_name">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="begin_date">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="end_date">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="print_date">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>4</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>title</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>store_name</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>begin_date</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>end_date</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>print_date</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>9049b19b-9313-443d-9cd1-4f50e6463920</rd:ReportID>
</Report>