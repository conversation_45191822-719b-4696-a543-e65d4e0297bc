<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="Textbox1">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!title.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>32pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>0.1cm</Top>
            <Left>0.2cm</Left>
            <Height>1.5cm</Height>
            <Width>14.6cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox2">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>拼单人 ：</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!admin.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>1.8cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox3">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>原台 ：</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!old_table_name.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>3.2cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox4">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>时间 ：</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!time.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>6cm</Top>
            <Left>6.92598cm</Left>
            <Height>1.2cm</Height>
            <Width>7.87402cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox5">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>订单号 ：</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!order_no.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>4.6cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox6">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>人数 ：</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!user_count.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>6cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>6.54959cm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Line Name="Line1">
            <Top>7.4cm</Top>
            <Left>0.2cm</Left>
            <Height>0cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>2pt</Width>
              </Border>
            </Style>
          </Line>
          <Textbox Name="Textbox7">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>目标台 ：</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!new_table_name.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>7.6cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox8">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>目标订单 ：</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!new_order_no.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>9cm</Top>
            <Left>0.2cm</Left>
            <Height>1.2cm</Height>
            <Width>14.6cm</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>10.89958cm</Height>
        <Style />
      </Body>
      <Width>15cm</Width>
      <Page>
        <PageHeight>12cm</PageHeight>
        <PageWidth>15cm</PageWidth>
        <LeftMargin>0cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="title">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="old_table_name">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="user_count">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="admin">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="order_no">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="time">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="new_table_name">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="new_order_no">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>title</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>old_table_name</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>user_count</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>admin</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>order_no</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>time</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>new_table_name</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>new_order_no</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>9049b19b-9313-443d-9cd1-4f50e6463920</rd:ReportID>
</Report>