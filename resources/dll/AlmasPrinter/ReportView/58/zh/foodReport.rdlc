<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="AlmasPrinterDll">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>aab90547-fbc6-449f-9f84-0528948bd22e</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="FoodDataSet">
      <Query>
        <DataSourceName>AlmasPrinterDll</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Count">
          <DataField>Count</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="FoodNameUg">
          <DataField>FoodNameUg</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FoodNameZh">
          <DataField>FoodNameZh</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Remarks">
          <DataField>Remarks</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>AlmasPrinterDll.Models</rd:DataSetName>
        <rd:TableName>Food</rd:TableName>
        <rd:ObjectDataSourceType>AlmasPrinterDll.Models.Food, AlmasPrinterDll, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="Textbox1">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!title.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>30pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>0.1cm</Top>
            <Left>0.1cm</Left>
            <Height>1.3cm</Height>
            <Width>9.8cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox2">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!table_name.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>30pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>1.6cm</Top>
            <Left>0.1cm</Left>
            <Height>1.3cm</Height>
            <Width>9.8cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox3">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>人数 ：</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!user_count.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>3.1cm</Top>
            <Left>0.1cm</Left>
            <Height>1cm</Height>
            <Width>9.8cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox4">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!admin_name.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!admin.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>4.3cm</Top>
            <Left>0.1cm</Left>
            <Height>1cm</Height>
            <Width>9.8cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox5">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>订单号 ：</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!order_no.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>5.5cm</Top>
            <Left>0.1cm</Left>
            <Height>1cm</Height>
            <Width>9.8cm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Textbox Name="Textbox6">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>时间 ：</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                  <TextRun>
                    <Value>=Parameters!time.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>20pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox3</rd:DefaultName>
            <Top>6.7cm</Top>
            <Left>0.1cm</Left>
            <Height>1cm</Height>
            <Width>9.8cm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
          <Line Name="Line1">
            <Top>8cm</Top>
            <Left>0.1cm</Left>
            <Height>0cm</Height>
            <Width>9.8cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>3pt</Width>
              </Border>
            </Style>
          </Line>
          <Line Name="Line2">
            <Top>13.2cm</Top>
            <Left>0.1cm</Left>
            <Height>0cm</Height>
            <Width>9.8cm</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Width>3pt</Width>
              </Border>
            </Style>
          </Line>
          <Tablix Name="Tablix3">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>7.8cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>1cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox26">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>美食</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox26</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox28">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>数量</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox28</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>3.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Count">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FoodNameUg.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>24pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FoodNameZh.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>20pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Remarks.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>32pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Count</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Count1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Count.Value</Value>
                                  <Style>
                                    <FontFamily>Microsoft YaHei Ug</FontFamily>
                                    <FontSize>24pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Count1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                            <Direction>LTR</Direction>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="详细信息" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>FoodDataSet</DataSetName>
            <Top>8.3cm</Top>
            <Left>0.1cm</Left>
            <Height>4.6cm</Height>
            <Width>9.8cm</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Textbox Name="Textbox7">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!remarks.Value</Value>
                    <Style>
                      <FontFamily>Microsoft YaHei Ug</FontFamily>
                      <FontSize>34pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>13.5cm</Top>
            <Left>0.1cm</Left>
            <Height>1.5cm</Height>
            <Width>9.8cm</Width>
            <ZIndex>9</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
              <Direction>LTR</Direction>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>15.52978cm</Height>
        <Style />
      </Body>
      <Width>10.05229cm</Width>
      <Page>
        <PageHeight>16cm</PageHeight>
        <PageWidth>10cm</PageWidth>
        <LeftMargin>0cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="title">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="table_name">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="user_count">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="admin">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="order_no">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="time">
      <DataType>String</DataType>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="remarks">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="admin_name">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>title</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>table_name</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>user_count</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>admin</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>order_no</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>time</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>remarks</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>admin_name</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>9049b19b-9313-443d-9cd1-4f50e6463920</rd:ReportID>
</Report>