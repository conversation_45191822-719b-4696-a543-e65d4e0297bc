<template>
  <div class="wrapper" @click="removeBtn">
    <div class="food-list">
      <!-- 购物车 -->
      <div class="lists">
        <div class="list-wrap">
          <div
            class="tit"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            @click="tableDetail"
          >
            <div
              class="table-customer"
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            >
              <span class="table-num">
                <span class="num">{{ table_detail.no }}</span>
                -{{ gL("table") }}
              </span>
              <span class="customer"
                >{{ customer_detail.customers_count }}/{{
                  table_detail.seating_capacity
                }}</span
              >
            </div>
            <!-- <div class="icon">
              <span class="iconfont icon-shiliangzhinengduixiang"></span>
            </div> -->
          </div>
          <div
            class="time-name"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
          >
            <div class="name">{{ customer_detail.user }}</div>
            <div class="name">{{ customer_detail.order_no }}</div>
            <div class="time-day">
              <span class="time">{{
                formatDate(customer_detail.created_at, "HH:mm")
              }}</span>
            </div>
          </div>
          <div class="food_lists">
            <div
              class="food-item"
              v-for="item in cart_list"
              :key="item.id"
              @click.stop="clickFood(item)"
            >
              <div v-if="!item.active">
                <div
                  class="food-name"
                  :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                >
                  {{ gLang == 1 ? item.food_name_ug : item.food_name_zh }}
                </div>
                <div
                  class="add-price"
                  :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
                >
                  <div
                    class="price-remark"
                    :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                  >
                    <span class="price">￥{{ item.price }}</span>
                    <span class="remark" v-if="item.remarks">{{
                      item.remarks
                    }}</span>
                  </div>
                  <div class="old-price">￥{{ formatPrice(item) }}</div>
                  <div
                    class="add-minus"
                    :style="{ textAlign: gLang == 2 ? 'right' : 'left' }"
                  >
                    <span class="num" v-if="item.food_format_id == 1"
                      >x{{ item.foods_count }}</span
                    >
                    <span class="num" v-if="item.food_format_id == 2"
                      >{{ item.foods_count }}kg</span
                    >
                  </div>
                </div>
              </div>
              <div class="two-btn" v-if="item.active">
                <div @click="remarkBox(item)">{{ gL("retire") }}</div>
                <div @click="urgeDinner(item.id)">{{ gL("urgeDinner") }}</div>
              </div>
            </div>
            <div
              class="food-item"
              v-for="item in canceled_foods"
              :key="item.id"
              @click="retreated(1, item)"
            >
              <div
                class="food-name"
                :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
              >
                {{ gLang == 1 ? item.food_name_ug : item.food_name_zh }}
              </div>
              <div
                class="add-price"
                :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
              >
                <div
                  class="price-remark"
                  :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                >
                  <span class="price">￥{{ item.price }}</span>
                  <span class="retreated" @click="retreated(1, item)">{{
                    gL("retreated")
                  }}</span>
                </div>
                <div class="old-price">￥{{ formatPrice(item) }}</div>
                <div
                  class="add-minus"
                  :style="{ textAlign: gLang == 2 ? 'right' : 'left' }"
                >
                  <span class="num" v-if="item.food_format_id == 1"
                    >x{{ item.foods_count }}</span
                  >
                  <span class="num" v-if="item.food_format_id == 2"
                    >{{ item.foods_count }}kg</span
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="price-count">
            <div class="del-price-count">
              <!-- <span class="iconfont icon-qingkong" @click="removeMessage"></span> -->
              <span class="price">￥{{ $toMoney(originPrice) }}</span>
              <span class="count">x{{ originCount }}</span>
            </div>
          </div>
          <div
            class="remark-btn"
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          >
            <!-- <span>{{gL('orderRemark')}}</span> -->
            <textarea
              :placeholder="gL('orderRemark')"
              readonly
              rows="2"
              v-model="customer_detail.remarks"
            ></textarea>
          </div>
          <div class="cancel-btn">
            <div class="one">
              <div @click="retreated(2)">{{ gL("retire") }}</div>
              <div @click="urgeDinner()">{{ gL("urgeDinner") }}</div>
              <div @click="addFood">{{ gL("orderFood") }}</div>
            </div>
            <div class="one">
              <div @click="showTableBox(2)">{{ gL("changeTable") }}</div>
              <div @click="showSplitBox">{{ gL("splitOrder") }}</div>
              <div @click="showTableBox(1)">{{ gL("andSingle") }}</div>
            </div>
          </div>
          <!-- <div class="cancel-btn-max" @click="cancelOrder">
            <span>{{ gL("cancelOrder") }}</span>
          </div> -->
        </div>
        <div class="wave">
          <img src="../../static/images/wave.png" alt />
        </div>
      </div>
      <div class="btns">
        <div
          class="orders"
          v-if="table_detail.orders"
          :class="[
            table_detail.orders.length > 10 ? 'scrolls' : '',
            table_detail.orders.length > 5 ? 'scroll' : ''
          ]"
        >
          <span
            class="order-item"
            v-for="(item, index) in table_detail.orders"
            :key="item.id"
            :class="active_order == index ? 'active' : ''"
            @click="changeTable(index, item)"
            >{{ index + 1 }}</span
          >
        </div>
        <div
          class="new"
          :class="gLang == 2 ? 'new-zh' : ''"
          @click="showNumberBox"
        >
          {{ gL("newGuest") }}
        </div>
      </div>
    </div>
    <div class="wrap">
      <div class="pay-title" :style="{ direction: gLang == 1 ? 'rtl' : 'ltr' }">
        {{ gL("payTypeTitle") }}
      </div>
      <div class="pay-box">
        <div class="pay-list">
          <div
            class="pay-item"
            v-for="(item, index) in pay_list"
            :key="item.id"
            @click="payState(item)"
            :style="item.value > 0 ? '' : 'padding:20px 0 ;'"
          >
            <img :src="item.icon" alt width="34px" />
            <span v-if="gLang === '2'">{{ item.name_zh }}</span>
            <span v-else>{{ item.name_ug }}</span>
          </div>
        </div>
      </div>

      <div class="small-btn-box">
        <el-button class="small-btn" @click="showZeroBox">{{
          gL("smallChange")
        }}</el-button>
      </div>

      <!-- 备注 -->
      <div class="mask" v-if="remark_box">
        <div class="box">
          <div class="title">
            <span></span>
            <span :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">{{
              gL("retire")
            }}</span>
            <span
              class="iconfont icon-jia-copy"
              @click="cancelRemarkBox"
            ></span>
          </div>
          <div
            class="content-remark"
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          >
            <div class="count" v-if="state != 2">
              <div class="name">{{ remark_title }}</div>
              <div class="num" v-if="item_data.food_format_id == 1">
                <div
                  class="iconfont icon-jian"
                  :class="item_data.foods_count >= remarks_count ? 'green' : ''"
                  :style="{ color: remarks_count == 1 ? '#cccccc' : '' }"
                  @click="minusFood"
                ></div>
                <div class="number">{{ remarks_count }}</div>
                <div
                  class="iconfont icon-jia1"
                  :class="item_data.foods_count > remarks_count ? 'green' : ''"
                  @click="plusFood"
                ></div>
              </div>
              <div class="num" v-if="item_data.food_format_id == 2">
                <div>{{ remarks_count }}kg</div>
                <div
                  class="icon-edit"
                  @click.stop="clickFoodNow(item_data)"
                  v-if="item_data.food_format_id == 2"
                  :style="{
                    marginLeft: gLang == 2 ? '15px' : '',
                    marginRight: gLang == 1 ? '15px' : ''
                  }"
                >
                  <span class="iconfont icon-shiliangzhinengduixiang"></span>
                </div>
              </div>
            </div>
            <div class="remark">{{ gL("reason") }}</div>
            <div class="remark-list">
              <div
                class="remark-item"
                v-for="(item, index) in remarks_list"
                :key="index"
                :identifier="'r' + item.id"
                @click="remarkItem(item.name, item.id)"
              >
                {{ item.name }}
              </div>
            </div>
            <div class="input">
              <!-- <input type="text" :placeholder="gL('otherRemark')" > -->
              <textarea
                cols="30"
                :placeholder="gL('otherRemark')"
                rows="2"
                v-model="remark_input"
              ></textarea>
            </div>
          </div>
          <div class="btn" @click="remarkClick">{{ gL("confirm") }}</div>
        </div>
      </div>
      <!-- 并单换台 -->
      <div class="mask-box" v-if="table_box">
        <div class="box">
          <div class="title">
            <span></span>
            <span :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">{{
              table_title
            }}</span>
            <span class="iconfont icon-jia-copy" @click="cancelTableBox"></span>
          </div>
          <div class="content-remark">
            <tableBox
              ref="table_box"
              :table_id="table_id"
              :table_state="table_box_state"
              @getTtableId="getTtableId"
              @openMergeBox="openMergeBox"
            ></tableBox>
          </div>
          <div class="btn" @click="tableConfirim" v-if="table_box_state == 2">
            {{ gL("confirm") }}
          </div>
        </div>
      </div>
      <!-- 并单 -->
      <div class="mask-box merge" v-if="merge_box">
        <div class="box">
          <div class="title">
            <span></span>
            <span :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">{{
              table_title
            }}</span>
            <span class="iconfont icon-jia-copy" @click="cancelMergeBox"></span>
          </div>
          <div class="content-remark">
            <div class="lists">
              <div
                class="list-wrap"
                v-for="(item, index) in merge_table_orders.orders"
                :key="index"
                v-if="index === first_index || index === last_index"
                @click="activeItem(item)"
                v-show="order_id != item.id"
                :class="item.active ? 'active' : ''"
              >
                <div
                  class="tit"
                  :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
                >
                  <div
                    class="table-customer"
                    :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                  >
                    <span class="table-num">
                      <span class="num">{{ merge_table_orders.no }}</span>
                      -{{ gL("table") }}
                    </span>
                    <span class="customer"
                      >{{ item.customers_count }}/{{
                        merge_table_orders.seating_capacity
                      }}</span
                    >
                  </div>
                  <div class="icon"></div>
                </div>
                <div
                  class="time-name"
                  :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
                >
                  <div class="name">{{ item.user }}</div>
                  <div class="time-day">
                    <span class="time">{{ item.created_at }}</span>
                  </div>
                </div>
                <div class="food_lists">
                  <div
                    class="food-item"
                    v-for="items in item.order_details"
                    :key="items.id"
                  >
                    <div>
                      <div
                        class="food-name"
                        :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                      >
                        {{ gLang == 1 ? items.food_name_ug : items.food_name_zh }}
                      </div>
                      <div
                        class="add-price"
                        :style="{
                          flexDirection: gLang == 2 ? 'row' : 'row-reverse'
                        }"
                      >
                        <div
                          class="price-remark"
                          :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                        >
                          <span class="price">￥{{ items.price }}</span>
                          <span class="remark" v-if="items.remarks">{{
                            items.remarks
                          }}</span>
                        </div>
                        <div class="add-minus">
                          <span class="num">x{{ items.foods_count }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="food-item"
                    v-for="(items, index) in item.canceled_foods"
                    :key="index"
                  >
                    <div
                      class="food-name"
                      :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                    >
                      {{ gLang == 1 ? items.food_name_ug : items.food_name_zh }}
                    </div>
                    <div
                      class="add-price"
                      :style="{
                        flexDirection: gLang == 2 ? 'row' : 'row-reverse'
                      }"
                    >
                      <div
                        class="price-remark"
                        :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                      >
                        <span class="price">￥{{ items.price }}</span>
                        <span class="retreated" @click="retreated(1, items)">{{
                          gL("retreated")
                        }}</span>
                      </div>
                      <div class="add-minus">
                        <span class="num">x{{ items.count }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="price-count">
                  <div class="del-price-count">
                    <span class="price">￥{{ $toMoney(item.price) }}</span>
                    <span class="count">x{{ item.foods_count }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="btn" @click="tableConfirim">{{ gL("confirm") }}</div>
          <div class="left" @click="leftClick">
            <span class="iconfont icon-icon-arrow-top2-copy-copy-copy"></span>
          </div>
          <div class="right" @click="rightClick">
            <span
              class="iconfont icon-icon-arrow-top2-copy-copy-copy-copy"
            ></span>
          </div>
        </div>
      </div>
      <!--分单-->
      <div class="split-box" v-if="split_box">
        <div class="split">
          <div class="title">
            <span></span>
            <span :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">{{
              gL("splitOrder")
            }}</span>
            <span class="iconfont icon-jia-copy" @click="cancelSplitBox"></span>
          </div>
          <div class="content-remark">
            <div class="food-title">
              <div class="title-item">{{ gL("primaryFoodLists") }}</div>
              <div class="title-item">{{ gL("newFoodLists") }}</div>
            </div>
            <div class="foods-box">
              <div class="food_lists">
                <div
                  class="food-item"
                  v-for="item in splitFoodsList"
                  :key="item.id"
                  :identifier="'s' + item.id"
                  @click="splitFood(item.id, item)"
                >
                  <div>
                    <div
                      class="food-name"
                      :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                    >
                      {{ item.food_name }}
                    </div>
                    <div
                      class="add-price"
                      :style="{
                        flexDirection: gLang == 2 ? 'row' : 'row-reverse'
                      }"
                    >
                      <div
                        class="price-remark"
                        :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                      >
                        <span class="price">￥{{ item.price }}</span>
                        <span class="remark" v-if="item.remarks">{{
                          item.remarks
                        }}</span>
                      </div>
                      <div class="old-price">￥{{ formatPrice(item) }}</div>
                      <div
                        class="add-minus"
                        :style="{ textAlign: gLang == 2 ? 'right' : 'left' }"
                      >
                        <span class="num" v-if="item.food_format_id == 1"
                          >x{{ item.foods_count }}</span
                        >
                        <span class="num" v-if="item.food_format_id == 2"
                          >{{ item.foods_count }}kg</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="food_lists">
                <div
                  class="food-item"
                  v-for="item in splitList"
                  :key="item.id"
                  :identifier="'s' + item.id"
                  @click="primaryFood(item.id, item)"
                >
                  <div>
                    <div
                      class="food-name"
                      :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                    >
                      {{ item.food_name }}
                    </div>
                    <div
                      class="add-price"
                      :style="{
                        flexDirection: gLang == 2 ? 'row' : 'row-reverse'
                      }"
                    >
                      <div
                        class="price-remark"
                        :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                      >
                        <span class="price">￥{{ item.price }}</span>
                        <span class="remark" v-if="item.remarks">{{
                          item.remarks
                        }}</span>
                      </div>
                      <div class="old-price">￥{{ formatPrice(item) }}</div>
                      <div
                        class="add-minus"
                        :style="{ textAlign: gLang == 2 ? 'right' : 'left' }"
                      >
                        <span class="num" v-if="item.food_format_id == 1"
                          >x{{ item.foods_count }}</span
                        >
                        <span class="num" v-if="item.food_format_id == 2"
                          >{{ item.foods_count }}kg</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="customer-count">
              <div
                class="customer-item item-right"
                :style="{
                  direction: gLang == 1 ? 'rtl' : ''
                }"
              >
                {{ gL("primaryCustomerCount") }}
                <span>:{{ customer_detail.customers_count }}</span>
              </div>
              <div
                class="customer-item"
                :style="{
                  direction: gLang == 1 ? 'rtl' : ''
                }"
              >
                <span>{{ gL("newCustomerCount") }}</span>
                <input
                  class="count-input"
                  :style="{
                    marginRight: gLang == 1 ? '20px' : '',
                    marginLeft: gLang == 2 ? '20px' : ''
                  }"
                  type="number"
                  v-model="newCustomerCount"
                  :placeholder="gL('inputCount')"
                  maxlength="2"
                />
              </div>
            </div>
          </div>
          <div class="btn" @click="splitConfirim">{{ gL("confirm") }}</div>
        </div>
      </div>
      <!-- 模态框 -->
      <div class="vip-modal mask account" v-if="changePassBox">
        <div class="box">
          <div class="title">
            <span></span>
            <span>{{ vipBoxTitle }}</span>
            <span class="iconfont icon-jia-copy" @click="cancelVipBox"></span>
          </div>
          <div
            class="content-input"
            :class="{ 'content-input-ug': gLang == 1 }"
          >
            <div class="old in">
              <input
                type="number"
                oninput="if(value.length>11)value=value.slice(0,11)"
                @input="vipInput"
                v-model="ruleForm.name"
                :placeholder="gL('phoneFour')"
              />
              <div class="vip-list" v-if="vipList.length != 0">
                <div
                  v-for="item in vipList"
                  :key="item.id"
                  @click="choicePhone(item.mobile)"
                >
                  {{ item.mobile }}
                </div>
              </div>
            </div>
            <div class="new in">
              <input
                type="password"
                maxlength="6"
                v-model="ruleForm.pass"
                :placeholder="gL('password')"
                @keyup.enter="confirmVipBox"
              />
            </div>
          </div>
          <div class="btn" @click="confirmVipBox">{{ gL("confirm") }}</div>
        </div>
      </div>
      <!--抹零弹出框-->
      <div class="mask-zero" v-if="zero_box">
        <div class="box">
          <div class="title">
            <span :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">{{
              gL("smallChange")
            }}</span>
            <span
              class="iconfont icon-jia-copy"
              @click="cancelZeroBox('close')"
            ></span>
          </div>
          <div
            class="content"
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          >
            <div class="content-list">
              <div class="label">
                {{ gL("smallChange") }}
              </div>
              <input
                type="text"
                class="input"
                v-model="smallChangeAmount"
                style="color: #ff9c00; text-align: center"
              />
            </div>
            <div class="content-list small-item" style="padding: 15px 0">
              <div class="label">
                {{ gL("originalPrice") }}
              </div>
              <div class="text" style="direction: ltr;">
                ￥{{ originPrice }}
              </div>
            </div>
            <div class="content-list small-item" style="padding: 15px 0">
              <div class="label">
                {{ gL("finalPrice") }}
              </div>
              <div class="text" style="direction: ltr;">
                ￥{{ afterSmallChange }}
              </div>
            </div>
          </div>
          <div
            class="btn-list"
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          >
            <div class="btn" @click="confirmSmallChange">
              {{ gL("confirm") }}
            </div>
            <!-- <div class="btn btn-left" @click="cancelZeroBox('ignore')">
              {{ gL("cancelSmallChange") }}
            </div> -->
          </div>
        </div>
      </div>

      <rechargeVip
        :number_box="rechargeBox"
        :rowData="vipDetailData"
        @cancelRechargeBox="cancelRechargeBox"
        @recharge="recharge"
      ></rechargeVip>
      <startJob
        :number_box="job_box"
        @startBox="startBox"
      ></startJob>
    </div>
    <div class="payment-box" :style="{ direction: gLang == 1 ? 'rtl' : 'ltr' }">
      <div class="order-title">
        {{ gL("refundDialogFoodTitle") }}
      </div>
      <div class="payment-info">
        <div class="line-box">
          <span>{{ gL("foodTotalPrice") }}</span>
          <span class="price">￥{{ $toMoney(originPrice) }}</span>
        </div>
        <div class="line-box" v-if="customer_detail.ignore_price">
          <div class="payment-name">{{ gL("smallChangePrice") }}</div>
          <div class="item-info">
            <div class="payment-price">
              ￥{{ $toMoney(customer_detail.ignore_price) }}
            </div>
            <div class="payment-icon" @click="cancelZeroBox('ignore')">
              <img src="./../assets/images/cancel-payment.png" alt="" />
            </div>
          </div>
        </div>
        <div class="line-box">
          <span>{{ gL("realTotalPrice") }}</span>
          <span class="price"
            >￥{{ $toMoney(originPrice - customer_detail.ignore_price) }}</span
          >
        </div>
      </div>
      <div class="line"></div>
      <div class="payment-list">
        <div
          class="payment-item line-box"
          v-for="item in paymentList"
          :key="item.id"
        >
          <div class="payment-name">
            {{ gLang == 1 ? item.name_ug : item.name_zh }}
          </div>
          <div class="item-info">
            <div class="payment-state">
              <span v-if="item.state == 0" class="primary-text">{{
                gL("loadingPaiding")
              }}</span>
              <span v-if="item.state == 1" class="primary-text">{{
                gL("paiding")
              }}</span>
              <span v-if="item.state == 2" class="danger-text">{{
                gL("paymentFailed")
              }}</span>
              <span v-if="item.state == 3" class="danger-text">{{
                gL("refundPaiding")
              }}</span>
            </div>
            <div class="payment-price">￥{{ item.amount }}</div>
            <div
              class="payment-icon"
              @click="paymentClick(item)"
              v-if="item.state == 1"
            >
              <img src="./../assets/images/cancel-payment.png" alt="" />
            </div>
          </div>
        </div>
      </div>

      <div class="checkout-btn-box">
        <el-button
          class="check-btn"
          :class="originPrice != 0 && nowPrice <= 0 ? 'active' : ''"
          @click="checkOutAf"
          >{{ getCheckoutText }}</el-button
        >
        <div class="check-print">
          <el-button class="check-btn" @click="advancePrint">
            <i class="iconfont icon-dayinji-"></i>
            <span class="btn-text">{{ gL("checkPrint") }}</span>
          </el-button>
          <div class="checks">
            <div
              class="check"
              :class="checkoutPrintLang ? 'active' : ''"
              @click="checks(1)"
            >
              <div class="check-item">{{ gL("uyghurche") }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="mask" v-if="check_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ gL("tips") }}</span>
          <span class="iconfont icon-jia-copy" @click="check_cancel"></span>
        </div>
        <div class="content" :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">
          <p class="price-count" v-if="pay_state != 4">
            ￥{{ afterSmallChange ? $toMoney(afterSmallChange) : originPrice }}
          </p>
          <p class="price-count" v-if="pay_state == 4">
            ￥{{ price_count.vip_price }}
          </p>
          <p>{{ gL("sureCheckOut") }}</p>
        </div>
        <div class="btn" @click="checkOut">{{ gL("confirm") }}</div>
      </div>
    </div>
    <div class="mask" v-if="qrcode_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ gL("scanPay") }}</span>
          <span class="iconfont icon-jia-copy" @click="closeQrcode"></span>
        </div>
        <div class="content" :class="{ 'qrcode-box': itemPay.pay_method == 2 }">
          <div
            class="qrcode-svg"
            v-if="itemPay.pay_method == 2"
            v-html="qrcode"
          ></div>
          <span v-if="itemPay.pay_method == 1">{{ pay_content }}</span>
        </div>
      </div>
    </div>
    <kiloBox ref="mychild" :number_box="kilo_box" @cancel="cancel"></kiloBox>

    <numberBox
      :number_box="number_box"
      :table_id="table_id"
      @cancel="cancel"
    ></numberBox>
    <modal
      :number_box="modal_box"
      :modal_content="modal_content"
      :modal_title="modal_title"
      @cancel="cancel"
      @confirm="revokeCancel"
    ></modal>

    <!-- 当餐桌里的全部美食推掉后取消餐桌 -->
    <div class="mask" v-if="cancelOrderBox">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ cancelOrderBoxTitle }}</span>
          <span
            class="iconfont icon-jia-copy"
            @click="cancelOrderBoxHandler"
          ></span>
        </div>
        <div class="content">{{ cancelOrderBoxContent }}</div>
        <div class="btn" @click="cancelOrderConfrim">{{ gL("confirm") }}</div>
      </div>
    </div>

    <!-- 支付弹出框 -->
    <PayNumberDialog
      ref="payNumberDialog"
      :show.sync="showNumberDialog"
      @payConfirm="payConfirm"
    />

    <!-- vip支付 -->
    <vipCheckout
      ref="vipCheckoutDialog"
      :show.sync="vipDetail"
      @close="closeVipCheckoutDialog"
      @recharge="openRechargeBox"
      @vipConfrim="vipCheckoutHandler"
    />

    <!-- 挂账支付 -->
    <CreditCheckout ref="creditCheckoutDialog" @creditConfrim="creditConfrim" />
  </div>
</template>
<script>
import tableBox from "../components/tableBox.vue";
import numberBox from "../components/numberBox.vue";
import modal from "../components/modal.vue";
import rechargeVip from "../components/rechargeVip.vue";
import startJob from "../components/startJob.vue";
import kiloBox from "../components/kiloTwo.vue";
import moment from "moment";
import QRCode from "qrcode";
import { formatDate } from "../utils/utils.js";
import PayNumberDialog from "./components/PayNumberDialog.vue";
import { v4 as uuidv4 } from "uuid";
import vipCheckout from "./components/VipCheckout.vue";
import CreditCheckout from "./components/CreditCheckout.vue";
import {
  getTableDetailsAPI,
  getRemarksAPI,
  getPayStatusAPI,
  getVipPhoneListAPI,
  postPaymentOfflineAPI,
  postPaymentCustomerPayAPI,
  postNativePayAPI,
  postMicroPayAPI,
  getOrderUrgeAPI,
  getOrderAllUrgeAPI,
  postOrderDetailsCancelAPI,
  postOrderDetailsUndoCancelAPI,
  postOrderAllFoodsCancelAPI,
  postOrderChangeTableAPI,
  postOrderCollageAPI,
  postOrderSplitAPI,
  postCustomerVerifyAPI,
  postOrderCancelAPI,
  getOrderPaymentListAPI,
  postPaymentReverseAPI,
  postOrderCheckoutAPI,
  postOrderIgnorePriceAPI,
  postCreditPayAPI
} from "./../api/index.js";
import { mapState, mapMutations } from "vuex";
var self;
let requestKey = false;
const detail = {
  components: {
    tableBox,
    numberBox,
    modal,
    rechargeVip,
    startJob,
    kiloBox,
    PayNumberDialog,
    vipCheckout,
    CreditCheckout
  },
  mounted() {
    window.electronAPI &&
      electronAPI.watchMQTT((event, data) => {
        if (this.$route.params.id != data.table_id) return;
        if (this.$route.name == "checkOut" && requestKey) {
          if (
            data.action == "订单已取消" ||
            data.action == "已结账" ||
            data.action == "全单退菜" ||
            data.action == "订单换台" ||
            data.action == "拼单"
          ) {
            this.$router.replace("/table");
          } else {
            this.tableDetail();
          }
        }
      });
  },
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    if (self.$route.params.id) {
      self.table_id = self.$route.params.id;
    } else {
      self.$router.push("/table");
    }
    self.retreat = false; //找零
    self.retreat_num = ""; //找零数
    self.is_empty = false; //清空计算器
    self.difference = false; //找零
    self.number = ""; //计算内容
    self.table_detail = [];
    self.cart_list = [];
    self.customer_detail = {};
    self.vipDetailData = "";
    self.vipDetail = false;
    self.pay_card = true;
    self.changePassBox = false;
    self.check_box = false;
    self.modal_box = false;
    self.qrcode_box = false; //二维码窗口
    self.qrcode = ""; //二维码

    this.originPrice = 0;
    this.originCount = 0;
    this.nowPrice = 0;

    if (this.$route.query.id) {
      this.query_order_id = this.$route.query.id;
      this.tableDetail(this.query_order_id);
    } else {
      this.tableDetail();
    }

    //self.getPayList();
    self.afterSmallChange = "";
    this.smallChangeAmount = 0;

    // setTimeout(() => {
    //   this.$refs.priceInput && this.$refs.priceInput.focus();
    // }, 500);
    requestKey = false;
    setTimeout(() => {
      requestKey = true;
    }, 2000);

    this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
  },
  deactivated() {
    clearInterval(self.interFlag);
    document.removeEventListener("keypress", this.listenScanPay);
    this.empty();
  },
  watch: {
    smallChangeAmount(val) {
      self.afterSmallChange =
        Math.round(this.originPrice * 100 - self.smallChangeAmount * 100) / 100;
    }
  },
  data() {
    return {
      gLang: 1,
      number_box: false, //新客模态框
      modal_box: false, //模态框
      modal_title: "", //模态框
      modal_content: "", //模态框
      check_box: false, //结账模态框
      kilo_box: false, //公斤框
      table_id: "", //餐桌ID
      table_detail: [], //餐桌详情
      customer_detail: {}, //详情
      originPrice: 0,
      originCount: 0,
      nowPrice: 0,
      cart_list: [], //美食列表
      canceled_foods: [], //已退美食
      order_id: "", //订单ID
      order_no: "",
      pay_list: [], //支付类型
      pay_state: 0, //默认支付类型
      retreat: false, //找零
      retreat_num: "", //找零数
      is_empty: false, //清空计算器
      difference: false, //找零
      number: "", //计算内容
      active_order: 0,
      remark_box: false, //退菜备注
      remark_title: "", //退菜备注框标题
      remark_input: "", //退菜备注内容
      remarks_list: "", //退菜备列表
      remarks_count: "", //退菜
      item_data: "", //点击的美食数据
      retreated_data: "", //点击的美食数据
      state: 1, //1取消退餐2退菜
      table_title: "", //并单和换台标题
      table_box: false, //并单和换台
      table_box_state: "", //1并单2换台
      merge_id: "", //并单id
      merge_box: "", //并单模态框
      first_index: 0, //并单第一个单子
      last_index: 1, //并单第二个单子
      merge_table_orders: "", //被合并的订单
      merge_order_id: "", //被合并的订单ID
      changePassBox: false, //VIP登录窗口
      ruleForm: {
        //VIP账号和密码
        name: "",
        pass: ""
      },
      vipBoxTitle: "",
      vipDetail: false, //会员详细
      vipDetailData: "", //会员详细数据
      rechargeBox: false, //会员充值模态框,
      job_box: false,
      query_order_id: "",
      vipList: [],
      qrcode_box: false, //二维码窗口
      qrcode: "", //二维码
      interFlag: "", //刷码支付interwal
      pay_card: true, //刷卡支付禁止多次连续支付
      itemPay: "", //刷卡支付禁止多次连续支付
      pay_content: "",
      merge_name: "",
      merge_order_no: "", //并单订单号
      split_box: false, //分单
      splitList: [],
      splitFoodsList: [],
      newCustomerCount: "",
      zero_box: false,
      smallChangeAmount: 0,
      afterSmallChange: "",
      tableDetails: null,
      nextCode: null,
      nextTime: null,
      lastCode: null,
      lastTime: null,
      scanPayCode: "",
      cancelOrderBox: false,
      cancelOrderBoxTitle: "",
      cancelOrderBoxContent: "",
      printerKey: false,
      startJobKey: +new Date(),
      storeInfo: {},
      formatDate: formatDate,
      showNumberDialog: false,
      realAmount: 0,
      paymentList: []
    };
  },

  computed: {
    ...mapState(["checkoutPrintLang"]),
    getCheckoutText() {
      if (this.nowPrice == 0) {
        return this.gL("checkOut");
      } else if (this.nowPrice > 0) {
        return this.gL("lackPrice").replace("%n", this.nowPrice);
      } else {
        return this.gL("giveChange").replace("%n", Math.abs(this.nowPrice));
      }
    }
  },

  methods: {
    // 状态改变函数
    ...mapMutations(["setCheckoutPrintLang"]),

    // 确认挂账
    creditConfrim(item) {
      const payment_no = uuidv4().replaceAll("-", "");
      const data = {
        order_no: this.order_no,
        order_id: this.order_id,
        payment_no: payment_no,
        amount: item.orderPrice * 100,
        holder_id: item.id
      };

      postCreditPayAPI(data).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });

          this.getPaymentList();
        }
      });
    },

    // 计算剩余金额
    calcNowPrice() {
      let isVip = false;
      const paidPrice = this.paymentList.reduce((sum, payment) => {
        if (payment.state == 1) {
          if (payment.payment_id == 4) {
            isVip = true;
          }
          return sum + payment.amount;
        }
        return 0;
      }, 0);
      this.nowPrice =
        this.originPrice - this.customer_detail.ignore_price - paidPrice;

      if (isVip) {
        if (this.customer_detail.vip_price == paidPrice) {
          postOrderCheckoutAPI(this.order_id).then(response => {
            if (response.status >= 200 && response.status < 300) {
              self.$message({
                message: self.gL("successfulOperation"),
                type: "success",
                customClass: self.$toastClass(),
                offset: 120
              });
              self.$router.push({ path: "/table" });
            }
          });
        }
      } else {
        if (this.originPrice !== 0 && this.nowPrice == 0) {
          postOrderCheckoutAPI(this.order_id).then(response => {
            if (response.status >= 200 && response.status < 300) {
              self.$message({
                message: self.gL("successfulOperation"),
                type: "success",
                customClass: self.$toastClass(),
                offset: 120
              });
              self.$router.push({ path: "/table" });
            }
          });
        }
      }
    },

    // 关闭vip弹出框
    closeVipCheckoutDialog() {
      this.vipDetailData = "";
      this.vipDetail = false;
    },

    // 获取支付记录
    getPaymentList() {
      getOrderPaymentListAPI(this.order_id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.paymentList = response.data.data;
          this.calcNowPrice();
        }
      });
    },

    // 监听扫码器输入
    scanPayBox() {
      document.addEventListener("keypress", this.listenScanPay);
      this.nextCode = null;
      this.nextTime = null;
      this.lastCode = null;
      this.lastTime = null;
    },

    // 监听扫码器输入
    listenScanPay(e) {
      this.nextCode = e.charCode;
      this.nextTime = new Date().getTime();
      // 扫码器的录入速度远快过手动录入，两个字符间隔小于20MS判定录入设备为扫码器
      if (
        this.lastCode != null &&
        this.lastTime != null &&
        this.nextTime - this.lastTime <= 20
      ) {
        this.scanPayCode += String.fromCharCode(this.lastCode);
      } else if (
        this.lastCode != null &&
        this.lastTime != null &&
        this.nextTime - this.lastTime > 100
      ) {
        this.scanPayCode = "";
      }
      this.lastCode = this.nextCode;
      this.lastTime = this.nextTime;
      if (e.code == "Enter" && this.scanPayCode.length > 10) {
        this.getCode(this.scanPayCode);
        this.scanPayCode = "";
      }
    },
    // 弹出抹零弹出框
    showZeroBox() {
      this.zero_box = true;
      if (!this.smallChangeAmount) {
        this.smallChangeAmount =
          Math.round((this.originPrice % 10) * 100) / 100;
      }
      this.afterSmallChange =
        Math.round(this.originPrice * 100 - this.smallChangeAmount * 100) / 100;
    },

    // 确认抹零
    confirmSmallChange() {
      if (self.smallChangeAmount <= 0) {
        self.$message({
          message: self.gL("smallChangeAmountBelowZero"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      }

      if (self.smallChangeAmount > this.originPrice) {
        self.$message({
          message: self.gL("smallChangeAmountGreaterPrice"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      }

      if (this.smallChangeAmount > 0) {
        this.zero_box = false;

        postOrderIgnorePriceAPI(
          this.order_id,
          parseFloat(this.smallChangeAmount)
        ).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            this.tableDetail();
          }
        });
      }
    },

    // 取消抹零
    cancelZeroBox(type) {
      self.zero_box = false;
      self.smallChangeAmount = 0;
      self.afterSmallChange = 0;

      if (type == "ignore") {
        postOrderIgnorePriceAPI(this.order_id, 0).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            this.tableDetail();
          }
        });
      }
    },

    // 弹出输入公斤弹出框
    clickFoodNow(e) {
      self.kilo_box = true;
      self.$refs.mychild.openBox(e);
    },

    // 计算美食价格
    formatPrice(e) {
      var num = 0.0;
      if (e.foods_count) {
        num = Math.round(Number(e.foods_count) * Number(e.price) * 100) / 100;
      } else {
        num = Math.round(Number(e.count) * Number(e.price) * 100) / 100;
      }
      return num.toFixed(2);
    },

    // 获取订单数据
    tableDetail(e) {
      getTableDetailsAPI(self.table_id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          if (response.data.table.orders.length > 0) {
            this.tableDetails = response.data;
            self.table_detail = response.data.table;

            if (this.$route.query.active_order) {
              self.active_order = this.$route.query.active_order;
              self.order_id = response.data.table.orders[self.active_order].id;
              self.order_no =
                response.data.table.orders[self.active_order].order_no;
              self.cart_list =
                response.data.table.orders[self.active_order].order_details;
              self.customer_detail =
                response.data.table.orders[self.active_order];
              self.canceled_foods =
                response.data.table.orders[self.active_order].canceled_foods;

              this.originCount =
                response.data.table.orders[this.active_order].foods_count;
              this.originPrice =
                response.data.table.orders[this.active_order].price;
            } else {
              self.active_order = 0;
              self.order_id = response.data.table.orders[0].id;
              self.order_no =
                response.data.table.orders[self.active_order].order_no;
              self.cart_list = response.data.table.orders[0].order_details;
              self.customer_detail = response.data.table.orders[0];
              self.canceled_foods =
                response.data.table.orders[0].canceled_foods;
              this.originCount = response.data.table.orders[0].foods_count;
              this.originPrice = response.data.table.orders[0].price;
            }
            self.pay_list = response.data.pay_type;
            self.pay_state = response.data.pay_type[0].id;
            self.itemPay = response.data.pay_type[0];

            // 获取完订单信息后获取订单的支付列表信息
            this.getPaymentList();
          }
        }
      });
    },

    /**
     * 更换订单
     */
    changeTable(s, e) {
      if (e.canceled_foods.length == 0 && e.order_details.length == 0) {
        self.$router.push({
          name: "foodList",
          params: { id: self.table_id },
          query: { id: e.id }
        });
      }
      self.customer_detail = self.table_detail.orders[s];
      self.order_id = self.table_detail.orders[s].id;
      self.order_no = self.table_detail.orders[s].order_no;
      self.cart_list = self.table_detail.orders[s].order_details;
      self.cart_list.forEach(item => {
        self.$set(item, "active", false);
      });
      self.canceled_foods = self.table_detail.orders[s].canceled_foods;
      this.originCount = this.table_detail.orders[s].foods_count;
      this.originPrice = this.table_detail.orders[s].price;
      self.active_order = s;
      self.vipDetailData = "";
      self.empty();
      this.getPaymentList();
    },

    /**
     * 备注列表
     */
    getRemarkList(s) {
      getRemarksAPI({
        tag: "foodCancel"
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.remarks_list = response.data.data;
        }
      });
    },
    /**
     * 备注列表
     */
    retireLsit() {
      getRemarksAPI({
        tag: "orderCancel"
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.remarks_list = response.data.data;
        }
      });
    },
    /**
     * 点击支付类型
     */
    payState(e) {
      if (this.storeInfo.isHandOver) {
        self.check_box = false;
        self.job_box = true;
        return;
      }

      self.pay_state = e.id;
      self.itemPay = e;
      //如果是微信支付

      if (e.id == 4) {
        const isPayment = this.paymentList.some(item => item.state == 1);
        if (!isPayment) {
          self.vipList = [];
          self.changePassBox = true;
          self.vipBoxTitle = e.name;
        } else {
          if (e.id == 4) {
            self.$message({
              message: self.gL("vipCardPayTips"),
              type: "error",
              customClass: self.$toastClass(),
              offset: 120
            });
            return;
          }
        }
      } else {
        if (e.id == 11) {
          this.$refs.creditCheckoutDialog.showDialog(
            this.nowPrice
          );
        } else {
          this.$refs.payNumberDialog.showDialog(this.nowPrice);
        }
      }
    },

    // 点击撤销支付
    paymentClick(item) {
      postPaymentReverseAPI({
        order_id: this.order_id,
        order_no: item.order_no,
        payment_no: item.payment_no
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: response.data.message,
            type: "warning",
            customClass: self.$toastClass(),
            offset: 120
          });

          item.state = 3;
          this.calcNowPrice();
        }
      });
    },

    // 支付
    payConfirm(value) {
      this.realAmount = value;
      if (this.pay_state == 1) {
        if (this.itemPay.pay_method == 1) {
          self.qrcode_box = true;
          self.pay_content = self.gL("paid");
          this.scanPayBox();
        } else if (this.itemPay.pay_method == 2) {
          self.qrcode = "";
          self.qrcode_box = true;
          self.getQrcode();
        }
      } else if (this.pay_state == 3) {
        this.checkOut();
      }
    },

    // 关闭二维码
    closeQrcode() {
      self.qrcode_box = false;
      window.electronAPI && window.electronAPI.closePayQrCode();
      document.addEventListener("keypress", this.listenScanPay);
      this.scanPayCode = "";
      self.pay_state = 3;
      self.pay_list.forEach(item => {
        if (item.id == 3) {
          self.itemPay = item;
        }
      });
    },
    //二维码
    getQrcode() {
      const payment_no = uuidv4().replaceAll("-", "");
      const data = {
        order_id: this.order_id,
        order_no: this.order_no,
        payment_id: this.pay_state,
        amount: parseFloat(this.realAmount),
        payment_no: payment_no
      };

      postNativePayAPI(data)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            QRCode.toString(
              response.data.data,
              {
                errorCorrectionLevel: "L",
                width: 480,
                height: 480,
                margin: 1
              },
              (error, qrcode) => {
                if (error != null) {
                  console.log("create qrcode error", error);
                  return;
                }
                this.qrcode = qrcode;
                if (this.qrcode_box) {
                  window.electronAPI &&
                    window.electronAPI.showPayQrCode({
                      qrcode: qrcode,
                      foods: this.tableDetails.table.orders[this.active_order]
                    });
                }
                self.getPayStatus(payment_no);
              }
            );
          }
        })
        .catch(err => {
          this.qrcode_box = false;
          self.pay_state = 3;
        });
    },
    //是否扫码支付
    getPayStatus(payment_no) {
      self.interFlag = setInterval(() => {
        if (self.qrcode_box) {
          getPayStatusAPI(payment_no, {
            isNoMessage: true
          })
            .then(response => {
              if (response.status >= 200 && response.status < 300) {
                if (response.data.data.status == 1) {
                  clearInterval(self.interFlag);
                  self.qrcode_box = false;
                  window.electronAPI && window.electronAPI.closePayQrCode();

                  this.getPaymentList();
                }
              }
            })
            .catch(err => {
              if (err.response.status) {
                console.log(err.response);
              }
            });
        } else {
          clearInterval(self.interFlag);
        }
      }, 3000);
    },
    // 扫码支付
    getCode(e) {
      if (self.pay_state == 1) {
        if (self.pay_card) {
          self.pay_card = false;
          const payment_no = uuidv4().replaceAll("-", "");
          const data = {
            auth_code: e,
            order_no: this.order_no,
            order_id: this.order_id,
            amount: parseFloat(this.realAmount),
            payment_no: payment_no,
            payment_id: this.pay_state
          };

          postMicroPayAPI(data)
            .then(response => {
              if (response.status >= 200 && response.status < 300) {
                clearInterval(self.interFlag);
                window.electronAPI && window.electronAPI.closePayQrCode();
                self.$message({
                  message: self.gL("paymentSuccess"),
                  type: "success",
                  customClass: self.$toastClass(),
                  offset: 120
                });

                this.getPaymentList();
              }
            })
            .catch(err => {
              self.pay_state = 3;
            })
            .finally(() => {
              this.qrcode_box = false;
              self.pay_card = true;
            });
        }
      }
    },

    /**
     * 清空
     */
    empty() {
      self.number = "";
      self.smallChangeAmount = 0;
      self.afterSmallChange = 0;
      if (self.number.length == 0) {
        self.is_empty = false;
        self.retreat = false;
      }
    },

    // 切换语言
    checks(e) {
      if (e == 1) {
        this.setCheckoutPrintLang(!this.checkoutPrintLang);
      }
    },
    /**
     * 点击美食
     */
    clickFood(e) {
      self.cart_list.forEach(item => {
        self.$set(item, "active", false);
      });
      self.remark_title = e.food_name;
      self.remarks_count = e.foods_count;
      self.item_data = e;
      e.active = true;
    },
    /**
     * 清除
     */
    removeBtn() {
      self.cart_list.forEach(item => {
        self.$set(item, "active", false);
      });
    },
    /**
     * 催菜
     */
    urgeDinner(foodId = 0) {
      window.electronAPI &&
        window.electronAPI.recordPrintTime({
          time: moment().format("YYYY-MM-DD HH:mm:ss.SSS"),
          orderDetailId: foodId,
          action: "催单",
          type: "requestTime"
        });
      if (foodId) {
        getOrderUrgeAPI(this.table_id, this.order_id, foodId).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            window.electronAPI &&
              window.electronAPI.recordPrintTime({
                time: moment().format("YYYY-MM-DD HH:mm:ss.SSS"),
                orderDetailId: foodId,
                action: "催单",
                type: "responseTime"
              });
          }
        });
      } else {
        getOrderAllUrgeAPI(this.table_id, this.order_id).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            window.electronAPI &&
              window.electronAPI.recordPrintTime({
                time: moment().format("YYYY-MM-DD HH:mm:ss.SSS"),
                orderDetailId: foodId,
                action: "催单",
                type: "responseTime"
              });
          }
        });
      }
    },
    /**
     * 退菜减数量
     */
    minusFood() {
      if (self.remarks_count > 1) {
        self.remarks_count--;
      }
    },
    /**
     * 退菜加数量
     */
    plusFood() {
      if (self.remarks_count < self.item_data.foods_count) {
        self.remarks_count++;
      }
    },
    /**
     * 退菜
     */
    retire(e) {
      var data = {};
      data.foods_count = self.remarks_count;
      if (self.remark_input != "") {
        data.remarks = self.remark_input;
      }

      postOrderDetailsCancelAPI(self.order_id, self.item_data.id, data).then(
        response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            self.cancelRemarkBox();
            self.tableDetail();
            self.empty();
          }
        }
      );
    },
    /**
     * 取消退菜
     */
    retreated(s, e) {
      self.state = s;
      if (s == 1) {
        self.modal_box = true;
        self.retreated_data = e;
        const name = this.gLang == 1 ? e.food_name_ug : e.food_name_zh;
        self.modal_content = `《${name}》${this.gL("ni")} ${this.gL(
          "backWithdraw"
        )}`;
        self.modal_title = self.gL("withdraw");
        self.item_data = e;
      } else {
        self.retireLsit();
        self.remark_box = true;
        self.modal_content = self.gL("confirmRetire");
        self.modal_title = self.gL("retire");
      }
    },
    /**
     * 退菜
     */
    confirm() {
      if (self.state == 1) {
        let data = {};
        data.foods_count = self.remarks_count;
        data.remarks = self.remark_input;

        postOrderDetailsCancelAPI(self.order_id, self.item_data.id, data).then(
          response => {
            if (response.status >= 200 && response.status < 300) {
              self.$message({
                message: self.gL("successfulOperation"),
                type: "success",
                customClass: self.$toastClass(),
                offset: 120
              });
              self.cancelRemarkBox();
              self.tableDetail();
            }
          }
        );
      } else {
        postOrderAllFoodsCancelAPI(self.order_id, {
          remarks: self.remark_input
        }).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            self.$router.push({ path: "/table" });
            self.cancelRemarkBox();
            self.tableDetail();
          }
        });
      }
    },

    // 撤销退菜
    revokeCancel() {
      postOrderDetailsUndoCancelAPI(this.order_id, this.retreated_data.id).then(
        response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            this.cancel();
            self.tableDetail();
          }
        }
      );
    },

    /**
     * 备注关闭
     */
    cancelRemarkBox() {
      self.remark_box = false;
      self.remark_input = "";
      self.state = 1;
    },
    /**
     * 备注完成
     */
    remarkClick() {
      if (self.remark_input.length != "") {
        if (self.state == 2 || self.state == 1) {
          self.confirm();
          // self.$router.push({ path: "/table", query: { isCancel: true } });
        } else {
          self.retire();
        }
      } else {
        self.$message({
          message: self.gL("chooiseRetire"),
          type: "error",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    /**
     * 备注
     */
    remarkItem(e, v) {
      let remarks = document.querySelectorAll(".remark-item");
      remarks.forEach(element => {
        element.classList.remove("changeColor");
      });
      let remark = document.querySelector(
        ".remark-item[identifier=r" + v + "]"
      );
      remark.classList.add("changeColor");
      if (self.gLang == 1) {
        if (self.remark_input != "") {
          self.remark_input += "،" + e;
        } else {
          self.remark_input += e;
        }
      } else {
        if (self.remark_input != "") {
          self.remark_input += "，" + e;
        } else {
          self.remark_input += e;
        }
      }
    },
    /**
     * 显示退菜态框
     */
    showNumberBox() {
      self.number_box = true;
    },
    /**
     * 退菜
     */
    remarkBox(e) {
      self.remark_box = true;
      self.getRemarkList();
    },
    /**
     * 关掉模态框
     */
    cancelOrderBoxHandler(e) {
      self.cancelOrderBox = false;
    },

    // 取消订单
    cancelOrder(e) {
      self.cancelOrderBox = true;
      self.cancelOrderBoxTitle = self.gL("cancelOrder");
      self.cancelOrderBoxContent = self.gL("cancelOrderContent");
    },

    // 确认取消订单
    cancelOrderConfrim() {
      postOrderCancelAPI(self.order_id)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.tableDetail();
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
          }
        })
        .finally(() => {
          self.messageBox = false;
        });
    },

    /**
     * 关掉模态框
     */
    cancel(e, s) {
      self.kilo_box = false;
      self.number_box = false;
      self.modal_box = false;
      if (e == 1) {
        self.remarks_count = s;
      }
    },
    /**
     * 确认并单换台
     */
    tableConfirim() {
      if (self.table_box_state == 2) {
        if (self.merge_id != "") {
          postOrderChangeTableAPI(self.order_id, {
            table_id: self.merge_id
          }).then(response => {
            if (response.status >= 200 && response.status < 300) {
              self.$message({
                message: self.gL("successfulOperation"),
                type: "success",
                customClass: self.$toastClass(),
                offset: 120
              });
              self.tableDetail();
              self.table_box = false;
              self.$router.push({ path: "/table" });
            }
          });
        } else {
          self.$message({
            message: self.gL("chooiseTable"),
            type: "error",
            customClass: self.$toastClass(),
            offset: 120
          });
        }
      }
      if (self.table_box_state == 1) {
        if (self.merge_order_id != "") {
          postOrderCollageAPI(self.order_id, {
            target_order_id: self.merge_order_id
          }).then(response => {
            if (response.status >= 200 && response.status < 300) {
              self.$message({
                message: self.gL("successfulOperation"),
                type: "success",
                customClass: self.$toastClass(),
                offset: 120
              });
              self.tableDetail();
              self.table_box = false;
              self.merge_box = false;
              self.$router.push({ path: "/table" });
            }
          });
        } else {
          self.$message({
            message: self.gL("chooiseTable"),
            type: "error",
            customClass: self.$toastClass(),
            offset: 120
          });
        }
      }
    },
    /**
     * 确认并单换台
     */
    getTtableId(e) {
      self.merge_id = e.id;
      self.merge_name = e.name;
    },
    /**
     * 显示并单换台
     */
    showTableBox(e) {
      self.table_box_state = e;
      if (e == 1) {
        self.table_title = self.gL("andSingle");
      } else {
        self.table_title = self.gL("changeTable");
      }
      self.table_box = true;
    },
    /**
     * 分单
     */
    splitFood(e, v) {
      let foods = document.querySelectorAll(".food-item");
      foods.forEach(element => {
        element.classList.remove("active-item");
      });
      let food = document.querySelector(".food-item[identifier=s" + e + "]");
      food.classList.add("active-item");

      let existing = self.splitList.filter(item => item.id == v.id)[0];
      if (existing == null) {
        existing = JSON.parse(JSON.stringify(v));
        if (existing.food_format_id == 1) {
          existing.foods_count = 0;
          self.splitList.push(existing);
        }
      }
      if (v.food_format_id == 2) {
        self.splitList.push(v);
        self.splitFoodsList.remove(v);
      } else {
        existing.foods_count++;
        v.foods_count--;
      }
      if (v.foods_count == 0) {
        self.splitFoodsList.remove(v);
      }
    },

    // 分单部分
    primaryFood(e, v) {
      let foods = document.querySelectorAll(".food-item");
      foods.forEach(element => {
        element.classList.remove("active-item");
      });
      let food = document.querySelector(".food-item[identifier=s" + e + "]");
      food.classList.add("active-item");
      let existing = self.splitFoodsList.filter(item => item.id == v.id)[0];
      if (existing == null) {
        existing = JSON.parse(JSON.stringify(v));
        if (existing.food_format_id == 1) {
          existing.foods_count = 0;
          self.splitFoodsList.push(existing);
        }
      }
      if (v.food_format_id == 2) {
        self.splitFoodsList.push(v);
        self.splitList.remove(v);
      } else {
        existing.foods_count++;
        v.foods_count--;
      }
      if (v.foods_count == 0) {
        self.splitList.remove(v);
      }
    },

    // 确定分单
    splitConfirim() {
      let newCustomerCount = parseInt(self.newCustomerCount);
      if (!newCustomerCount) {
        self.$message({
          message: self.gL("pepoleErrot"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      } else if (newCustomerCount > self.customer_detail.customers_count) {
        self.$message({
          message: self.gL("pleaseInputCount"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        self.newCustomerCount = "";
        return;
      }
      let data = {};
      data.customers_count = newCustomerCount;
      data.order_details = [];
      for (let i = 0; i < self.splitList.length; i++) {
        const element = self.splitList[i];
        data.order_details[i] = {
          id: element.id,
          foods_count: element.foods_count
        };
      }

      postOrderSplitAPI(self.order_id, data).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
          self.tableDetail();
          self.split_box = false;
          self.splitList = [];
        }
      });
    },
    /**
     * 弹出分单弹出框
     */
    showSplitBox() {
      self.split_box = true;
      self.splitFoodsList = JSON.parse(JSON.stringify(self.cart_list));
    },
    /**
     * 关闭分单弹出框
     */
    cancelSplitBox() {
      if (self.splitList.length > 0) {
        const splitLength = self.splitList.length;
        const foods_array = JSON.parse(JSON.stringify(self.splitList));
        for (let i = 0; i < splitLength; i++) {
          for (let j = 0; j < foods_array[i].foods_count; j++) {
            self.primaryFood(self.splitList[0].id, self.splitList[0]);
          }
        }
      }
      self.newCustomerCount = "";
      self.split_box = false;
    },
    /**
     * 关闭并单换台
     */
    cancelTableBox() {
      self.table_box = false;
    },
    /**
     * 显示并单模态框
     */
    openMergeBox(e) {
      self.merge_id = e.id;
      self.merge_name = e.name;
      if (e.table_status == 3) {
        // getTableDetailItemAPI(e.id).then(response => {
        getTableDetailsAPI(e.id).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.merge_table_orders = response.data.table;
            self.merge_table_orders.orders.forEach((item, index) => {
              self.$set(item, "active", false);
              if (item.id == self.order_id) {
                self.merge_table_orders.orders.splice(index, 1);
              }
            });
            if (self.merge_table_orders.orders.length == 1) {
              self.merge_order_id = self.merge_table_orders.orders[0].id;
              self.merge_order_no = self.merge_table_orders.orders[0].order_no;
              self.tableConfirim();
            } else if (this.merge_table_orders.orders.length > 1) {
              self.merge_box = true;
            }
          }
        });
      }
      self.first_index = 0;
      self.last_index = 1;
    },
    /**
     * 关闭并单模态框
     */
    cancelMergeBox() {
      self.merge_box = false;
    },
    /**
     * 点击返回
     */
    leftClick() {
      var length = self.merge_table_orders.orders.length;
      if (length > 2) {
        if (self.first_index > 0) {
          self.first_index = self.first_index - 2;
          self.last_index = self.last_index - 2;
        }
      }
    },
    /**
     * 点击前进
     */
    rightClick() {
      var length = self.merge_table_orders.orders.length;
      if (length > 2) {
        if (self.last_index < length - 1) {
          self.first_index = self.first_index + 2;
          self.last_index = self.last_index + 2;
        }
      }
    },
    /**
     * 点击要并单的订单
     */
    activeItem(e) {
      self.merge_table_orders.orders.forEach(item => {
        self.$set(item, "active", false);
      });
      e.active = true;
      self.merge_order_id = e.id;
      self.merge_order_no = e.order_no;
    },
    /**
     * 关闭VIP登录窗口
     */
    cancelVipBox() {
      self.changePassBox = false;
      self.ruleForm = {
        name: "",
        pass: ""
      };
    },
    /**
     * 显示VIP充值登录窗口
     */
    openRechargeBox() {
      self.rechargeBox = true;
    },
    /**
     * 关闭VIP充值登录窗口
     */
    cancelRechargeBox() {
      self.rechargeBox = false;
    },
    //充值
    recharge(e) {
      this.$refs.vipCheckoutDialog.updateDialogData(e);
    },
    checkOutAf() {
      if (this.originPrice == 0) {
        return;
      } else if (this.nowPrice > 0) {
        return;
      } else if (this.nowPrice <= 0) {
        postOrderCheckoutAPI(this.order_id).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            self.$router.push({ path: "/table" });
          }
        });
      }
    },
    //结账
    checkOut() {
      var isHandOver = this.storeInfo.isHandOver;
        this.check_box = false;
        // let data = {};
        // console.log("this.number -> ", this.number);
        // data.give_change = self.retreat_num;
        // if (this.smallChangeAmount && this.smallChangeAmount > 0) {
        //   data.ignore_price = self.smallChangeAmount;
        // }
        // data.collected_amount = this.number;
        // if (self.vipDetailData.id) {
        //   data.payment_type_id = self.pay_state;
        //   data.customer_id = self.vipDetailData.id;
        // } else {
        //   data.payment_type_id = self.pay_state;
        // }
        // if (data.payment_type_id == 4) {
        //   if (self.vipDetailData.balance < self.customer_detail.vip_price) {
        //     self.$message({
        //       message: self.gL("checkoutError"),
        //       type: "warning",
        //       customClass: self.$toastClass(),
        //       offset: 120
        //     });
        //     self.check_cancel();
        //     return;
        //   }
        // }

        const payment_no = uuidv4().replaceAll("-", "");

        postPaymentOfflineAPI(
          {
            order_id: this.order_id,
            order_no: this.order_no,
            payment_id: this.pay_state,
            amount: parseFloat(this.realAmount),
            payment_no: payment_no
          },
          {
            lang: this.checkoutPrintLang ? "ug-CN" : "zh-CN"
          }
        ).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });

            this.getPaymentList();

            // self.tableDetail();
            // self.check_cancel();
            // self.$router.push({ path: "/table" });
          }
        });
    },

    // vip结账
    vipCheckoutHandler(data) {
      const payment_no = uuidv4().replaceAll("-", "");
      postPaymentCustomerPayAPI({
        order_id: this.order_id,
        order_no: this.order_no,
        amount: parseFloat(data.vipPrice),
        customer_id: data.id,
        payment_no: payment_no,
        password: data.vipPassword
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });

          this.getPaymentList();

          // self.tableDetail();
          // self.check_cancel();
          // self.$router.push({ path: "/table" });
        }
      });
    },

    check_cancel() {
      self.check_box = false;
    },

    /**
     * 交班模态框
     */
    startBox() {
      self.job_box = false;
      this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
    },
    /**
     * 继续加菜
     */
    addFood() {
      localStorage.setItem("isNewAddFood", true);
      self.$router.push({
        name: "foodList",
        params: { id: self.table_id },
        query: { id: self.order_id, isAppend: true }
      });
    },
    /**
     * 登录VIP
     */
    confirmVipBox() {
      if (self.ruleForm.name != "" && self.ruleForm.pass != "") {
        postCustomerVerifyAPI({
          mobile: self.ruleForm.name,
          password: self.ruleForm.pass
        }).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.vipDetailData = response.data.data;
            this.$refs.vipCheckoutDialog.showDialog({
              orderPrice: this.customer_detail.price,
              vipPrice: this.customer_detail.vip_price,
              vipPassword: self.ruleForm.pass,
              ...response.data.data
            });
            self.changePassBox = false;
            self.ruleForm = {
              name: "",
              pass: ""
            };
          }
        });
      } else {
        if (self.ruleForm.pass == "") {
          self.$message({
            message: self.gL("inputPassword"),
            type: "error",
            customClass: self.$toastClass(),
            offset: 120
          });
        }
        if (self.ruleForm.name == "") {
          self.$message({
            message: self.gL("pleasePhone"),
            type: "error",
            customClass: self.$toastClass(),
            offset: 120
          });
        }
      }
    },
    /**
     * 提前打印
     */
    advancePrint() {
      if (this.printerKey) {
        this.$message({
          message: this.gL("etcFiveSecond"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      let disablePrint = localStorage.getItem("disablePrint") == "true";
      if (disablePrint) {
        this.$message({
          message: this.gL("disablePrinter"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (
        !this.tableDetails.printer_info ||
        !this.tableDetails.printer_info.printer_ip
      ) {
        this.$message({
          message: this.gL("noPrinterInfo"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (this.storeInfo == "" || this.storeInfo == null) {
        this.$message({
          message: self.gL("noUserInfo"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      if (this.tableDetails == null) {
        this.$message({
          message: self.gL("noTable"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      this.$message({
        message: this.gL("sendPrinter"),
        type: "success",
        customClass: this.$toastClass(),
        offset: 120
      });

      const tableObj = {
        name_ug: "",
        name_zh: ""
      };
      if (this.tableDetails.table) {
        tableObj.name_ug = this.tableDetails.table.name_ug
          ? this.tableDetails.table.name_ug
          : this.tableDetails.table.name;
        tableObj.name_zh = this.tableDetails.table.name_zh
          ? this.tableDetails.table.name_zh
          : this.tableDetails.table.name;
      }

      let data = {
        action: "预结账",
        time: moment().format("YYYY-MM-DD HH:mm:ss"),
        table_id: this.table_id,
        merchant_no: this.storeInfo.merchant_no,
        order: {
          user: this.storeInfo.userName,
          operator: this.storeInfo.userName,
          table_name_ug: tableObj.name_ug,
          table_name_zh: tableObj.name_zh,
          customers_count: this.tableDetails.table.orders[0].customers_count,
          order_no: this.tableDetails.table.orders[0].order_no,
          order_details: this.tableDetails.table.orders[0].order_details,
          price: this.tableDetails.table.orders[0].price,
          original_price: this.tableDetails.table.orders[0].original_price,
          ignore_price: this.smallChangeAmount,
          merchant: {
            address_ug: this.storeInfo.merchant_address_ug,
            address_zh: this.storeInfo.merchant_address_zh,
            phone: this.storeInfo.merchant_phone,
            name_ug: this.storeInfo.merchant_name_ug,
            name_zh: this.storeInfo.merchant_name_zh
          },
          printers: {
            ...this.tableDetails.printer_info,
            lang: this.checkoutPrintLang ? 1 : 2,
            custom: true
          }
        }
      };

      this.printerKey = true;
      setTimeout(() => {
        this.printerKey = false;
      }, 5000);

      this.$bus.$emit("customPrint", data);
      window.electronAPI && window.electronAPI.customPrint(data);
    },
    //输入尾4位搜索会员
    vipInput() {
      if (self.ruleForm.name.length == 4) {
        getVipPhoneListAPI({
          keyword: self.ruleForm.name
        }).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.vipList = response.data.data;
          }
        });
      } else {
        self.vipList = [];
      }
    },
    //选择手机号
    choicePhone(e) {
      self.ruleForm.name = e;
      self.vipList = [];
    }
  }
};
export default detail;
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.warning {
  border: 1px solid #f40;
  color: #f40;
}
.content-password {
  padding: 30px;
  padding-bottom: 0;
  font-size: 26px;
  text-align: center;
}
.pass-input {
  border: 1px solid #ccc;
  width: 500px;
  padding: 15px;
  outline: none;
}
.changeColor {
  border: 1px solid #139d59;
  color: #139d59;
}
.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
}
.food-list {
  width: 30%;
  height: 100%;
}
.lists {
  width: 100%;
  height: 90%;
  background-color: #f2f2f2;
  position: relative;
  .list-wrap {
    padding: 0 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-bottom: 10px;
    .tit {
      display: flex;
      justify-content: space-between;
      padding: 12px 0 20px 0;
      .table-customer {
        color: @textColor;
        .table-num {
          font-size: 30px;
          .num {
            font-weight: bold;
          }
        }
        .customer {
          padding: 0 20px;
          font-size: 26px;
        }
      }
      .icon {
        .iconfont {
          font-size: 30px;
          color: @grayColor;
          cursor: pointer;
        }
      }
    }
    .time-name {
      display: flex;
      justify-content: space-between;
      font-size: 22px;
      color: @grayColor;
      border-bottom: 1px solid @graphiteColor;
      padding-bottom: 5px;
      .time-day {
        .time {
          padding: 0 5px;
        }
      }
    }
    .food_lists {
      height: 56%;
      overflow-y: scroll;
      padding: 0 10px;
      .food-item {
        padding-top: 15px;
        border-bottom: 1px dashed @graphiteColor;
        padding-bottom: 10px;
        cursor: pointer;
        .food-name {
          font-size: 26px;
        }
        .add-price {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 15px;
          .price-remark {
            display: flex;
            width: 50%;
            align-items: center;
            .price {
              font-size: 22px;
              // color:#ff5151;
            }
            .remark {
              font-size: 22px;
              color: @greenColor;
              width: 150px;
              display: inline-block;
              overflow: hidden;
              height: 20px;
              text-overflow: ellipsis;
              white-space: nowrap;
              padding: 2px 10px;
              height: 100%;
              width: 90%;
            }
            .retreated {
              font-size: 18px;
              background: @graphiteColor;
              padding: 5px;
              color: @grayColor;
              margin: 0 10px;
            }
          }
          .old-price {
            width: 29%;
            font-size: 22px;
            color: #d15e4b;
          }
          .add-minus {
            width: 21%;
            font-size: 22px;
          }
        }
        .two-btn {
          display: flex;
          justify-content: space-between;
          div {
            width: 48%;
            background-color: @graphiteColor;
            font-size: 26px;
            padding: 15px 0;
            text-align: center;
          }
        }
      }
    }
    .price-count {
      // height: 10%;
      .del-price-count {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        height: 100%;
        padding-top: 10px;
        // padding-top: 50px;
        .iconfont {
          font-size: 30px;
          color: @grayColor;
          cursor: pointer;
        }
        .price {
          font-size: 30px;
          color: #ff9c00;
          font-weight: bold;
        }
        .count {
          color: @grayColor;
          font-size: 26px;
        }
      }
    }
    .remark-btn {
      background: #ffffff;
      color: @grayColor;
      height: 8%;
      margin-top: 15px;
      padding: 10px;
      font-size: 22px;
      cursor: pointer;
      overflow: hidden;
      textarea {
        outline: none;
        color: #666666;
        width: 100%;
        resize: none;
        font-family: "Alp Ekran";
        border: none;
        font-size: 22px;
        cursor: pointer;
        height: 100%;
      }
    }
    .cancel-btn {
      .one {
        display: flex;
        justify-content: space-between;
        padding-top: 10px;
        font-size: 22px;
        div {
          width: 32%;
          background-color: @graphiteColor;
          text-align: center;
          padding: 10px 0;
          cursor: pointer;
        }
      }
      .two {
        display: flex;
        justify-content: space-between;
        padding-top: 10px;
        font-size: 22px;
        div {
          width: 49%;
          background-color: @graphiteColor;
          padding: 10px 0;
          text-align: center;
          cursor: pointer;
        }
      }
    }
  }
  .wave {
    position: absolute;
    bottom: -20px;
    width: 100%;
    img {
      width: 100%;
    }
  }
}
.lists::after,
.lists::before {
  content: "";
  display: block;
  width: 100%;
  position: absolute;
  border-top: 10px dotted #f2f2f2;
  transform: translateY(-50%);
  z-index: -1;
}
.lists::after {
  top: 0;
  transform: translateY(-50%);
  z-index: -1;
}
.lists::before {
  bottom: 0;
  transform: translateY(50%);
  z-index: -1;
}
.btns {
  font-size: 22px;
  display: flex;
  padding-top: 25px;
  justify-content: space-between;
  align-items: center;
  .new {
    padding: 10px;
    background-color: #d15e4b;
    color: #ffffff;
    cursor: pointer;
  }
  .new-zh {
    padding: 10px 40px;
  }
  .orders {
    .order-item {
      border: 2px solid @grayColor;
      border-radius: 5px;
      padding: 0 10px;
      margin-right: 5px;
      cursor: pointer;
    }
    .active {
      background-color: @greenColor;
      border-color: @greenColor;
      color: #ffffff;
    }
  }
  .scrolls {
    width: 71%;
    overflow-x: scroll;
    overflow-y: hidden;
    height: 40px;
    display: flex;
    padding-bottom: 5px;
  }
}
.wrap {
  height: 100%;
  width: 38.5%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .pay-title {
    padding: 14px 10px;
    font-size: 26px;
  }
  .pay-box {
    width: 100%;
    height: 100%;
    background-color: #cccccc;
    border-radius: 4px;
    position: relative;
    .pay-list {
      display: flex;
      padding: 20px;
      box-sizing: border-box;
      flex-wrap: wrap;
      .pay-item {
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 22px;
        cursor: pointer;
        border-radius: 10px;
        &:active {
          background: @greenColor;
          color: #ffffff;
          img {
            filter: brightness(100);
            // invert(100) brightness(100)
          }
        }
        img {
          margin-bottom: 10px;
        }
        span {
          text-align: center;
        }
      }
    }
    .all-price {
      color: #ffffff;
      font-size: 26px;
      padding: 0 20px 20px;
      &:last-child {
        padding-bottom: 0px;
      }
      .price {
        direction: ltr;
        font-size: 30px;
        color: #ff9c00;
        display: inline-block;
        font-weight: bold;
      }
    }

    .vip-price {
      display: flex;
      align-items: center;
      .old-price {
        text-decoration: line-through;
        font-size: 26px;
        color: @graphiteColor;
        padding: 0 20px;
      }
      .discount {
        color: @graphiteColor;
        .num {
          direction: ltr;
          display: inline-block;
        }
      }
    }
    .calculator-screen {
      padding: 0 20px;
      width: 100%;
      position: absolute;
      bottom: 20px;
      .box {
        border: 1px solid #4d4d4d;
        color: #ffffff;
        font-size: 30px;
        display: flex;
      }
      .calculation-screen {
        padding: 15px;
        color: @graphiteColor;
        width: 50%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .cash {
          font-size: 26px;
        }
        .iconfont {
          font-size: 21px;
          color: @grayColor;
          cursor: pointer;
        }
        .number {
          font-size: 30px;
          // font-weight: bold;
          color: #ffffff;
          background: none;
          outline: none;
          width: 150px;
        }
      }
      .retreat-screen {
        width: 50%;
        padding: 15px;
        border-left: 1px solid #4d4d4d;
        text-align: left;
        .text {
          font-size: 26px;
          color: @graphiteColor;
        }
        .num {
          font-weight: bold;
        }
      }
      .difference {
        color: #ff5151;
        .text {
          color: #ff5151;
        }
      }
    }
  }
  .vip {
    .tit {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      border-bottom: 1px solid #4d4d4d;
      height: 14%;
      .text {
        font-size: 26px;
        color: #ffffff;
      }
      .back {
        font-size: 22px;
        color: @textColor;
        background-color: #ffffff;
        padding: 10px 35px;
        cursor: pointer;
      }
    }
    .detail {
      display: flex;
      color: #ffffff;
      font-size: 26px;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px 0 45px;
      height: 86%;
      .detail-box {
        background-color: @textColor;
        padding: 30px 20px 0 20px;
        div {
          margin-bottom: 20px;
        }
        .recharge {
          font-size: 22px;
          background-color: #2e3033;
          padding: 10px 0;
          text-align: center;
          cursor: pointer;
        }
        .price {
          color: @greenColor;
          margin-bottom: 30px;
        }
      }
      .price-box {
        text-align: left;
        .price-item {
          padding-bottom: 40px;
          direction: rtl;
          &:last-child {
            padding-bottom: 0;
          }
          .num {
            font-size: 30px;
            display: inline-block;
            direction: ltr;
          }
        }
        .actual {
          .num {
            color: #ff9c00;
          }
        }
      }
    }
  }
  .btn-box {
    width: 100%;
    height: 40%;
    display: flex;
    justify-content: space-between;
    .numbers {
      width: 60%;
      display: flex;
      justify-content: space-between;
      .one {
        width: 75%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .item {
          width: 31.5%;
          background: #e6e6e6;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 10px;
          font-size: 30px;
          color: @grayColor;
          font-weight: bold;
          cursor: pointer;
        }
      }
      .two {
        width: 22%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 97%;
        div {
          background: #e6e6e6;
          height: 22%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 30px;
          color: @grayColor;
          font-weight: bold;
          cursor: pointer;
        }
        .iconfont {
          font-size: 33px;
        }
      }
    }
    .btns-price {
      width: 38%;
      height: 97%;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      .checks {
        display: flex;
        justify-content: space-between;
        height: 21%;
        font-size: 26px;
        .check {
          height: 100%;
          width: 48%;
          display: flex;
          cursor: pointer;
          background-color: #cccccc;
          color: #ffffff;
          .check-item {
            display: flex;
            justify-content: center;
            width: 60%;
            align-items: center;
            background-color: #666666;
          }
        }
        .active {
          flex-direction: row-reverse;
          .check-item {
            background-color: @greenColor;
          }
        }
      }
      .check-list {
        display: flex;
        justify-content: space-between;
        height: 21%;
        font-size: 26px;
        .print-order {
          background-color: @graphiteColor;
          font-size: 26px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          width: 48%;
          text-align: center;
          &:hover {
            opacity: 0.8;
          }
        }
        .check-pay {
          width: 48%;
          background: #ccc;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }
      .check-out {
        background-color: #ff9c00;
        font-size: 30px;
        height: 48%;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}

.payment-box {
  width: 30%;
  background-color: #fff;
  border-inline-end: 1px solid #ccc;
  padding: 10px 20px;
  display: flex;
  flex-direction: column;

  .order-title {
    padding: 14px 00px;
    font-size: 26px;
  }
  .payment-info {
    padding: 10px 0px;
  }
  .payment-list {
    padding: 10px 0px;
    flex: 1;
    overflow-y: auto;
  }

  .primary-text {
    color: #07c160;
  }

  .danger-text {
    color: #d15e4b;
  }

  .payment-state {
    font-size: 16px;
  }

  .item-info {
    display: flex;
    align-items: center;
    .payment-price {
      padding: 0px 14px;
    }
    .payment-icon {
      width: 26px;
      height: 26px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .check-btn {
    width: 100%;
    margin: 16px 0px;
    font-size: 22px;
    // outline: none;
    border: none;
    background-color: #878787;
    color: #fff;
    &.active {
      background-color: #ff9c00;
    }
  }
  .check-print {
    display: flex;
    column-gap: 10px;
    width: 100%;
    .check-btn {
      margin: 0px;
      flex: 3;
      background-color: #ccc;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: center;
      .iconfont {
        font-size: 22px;
      }
    }
    .checks {
      flex: 2;
      .check {
        height: 100%;
        width: 100%;
        display: flex;
        cursor: pointer;
        background-color: #cccccc;
        color: #ffffff;
        border-radius: 4px;
        .check-item {
          border-radius: 4px;
          display: flex;
          justify-content: center;
          width: 60%;
          align-items: center;
          background-color: #666666;
        }
      }
      .active {
        flex-direction: row-reverse;
        .check-item {
          background-color: @greenColor;
        }
      }
    }
  }
}

.line-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  padding: 10px 0px;
}

.line {
  width: 100%;
  height: 1px;
  background-color: #ccc;
}

.small-btn-box {
  margin-top: 16px;
  margin-bottom: 10px;
  font-size: 22px;
  .small-btn {
    width: 100%;
    font-size: 22px;
    border: none;
    background-color: #ccc;
    color: #333;
  }
}

//数字键盘提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 600px;
    font-size: 30px;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content-remark {
      padding: 30px;
      padding-bottom: 0;
      font-size: 26px;
      .remark {
        padding-bottom: 30px;
        color: @grayColor;
      }
      .remark-list {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .remark-item {
          width: 48%;
          background-color: #f5f5f5;
          padding: 15px 0;
          text-align: center;
          margin-bottom: 20px;
          overflow: hidden;
          cursor: pointer;
        }
      }
      .count {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 30px;
        .num {
          display: flex;
          align-items: center;
          .iconfont {
            font-size: 40px;
            color: @graphiteColor;
            cursor: pointer;
          }
          .green {
            color: @greenColor;
          }
          .number {
            width: 70px;
            text-align: center;
            border-bottom: 2px solid @textColor;
            margin: 0 15px;
            font-weight: bold;
          }
          .icon-edit {
            .iconfont {
              font-size: 26px;
              color: @greenColor;
            }
          }
        }
      }
    }
    .content {
      text-align: center;
      padding: 30px;
      .price-count {
        font-size: 30px;
        color: #ff9c00;
        font-weight: bold;
        padding-bottom: 20px;
      }
    }
    .content-input {
      padding: 40px 100px 20px 100px;
      .in {
        border: 1px solid #cccccc;
        text-align: center;
        padding: 10px 0;
        margin-bottom: 20px;
        position: relative;
        &:last-child {
          margin-bottom: 0;
        }
        input {
          width: 93%;
          outline: none;
          font-size: 22px;
        }
        .vip-list {
          position: absolute;
          background: #e6e6e6;
          width: 100%;
          z-index: 9;
          top: 52px;
          // padding-top:10px;
          div {
            padding: 15px 0;
            cursor: pointer;
            border-bottom: 1px solid #cccccc;
          }
        }
      }
    }
    .content-input-ug {
      div {
        input::-webkit-input-placeholder {
          text-align: right;
        }
      }
    }
    .input {
      border: 1px solid #e6e6e6;
      padding: 15px 20px 10px 20px;
      textarea {
        outline: none;
        color: #666666;
        width: 100%;
        resize: none;
        font-family: "Alp Ekran";
        border: none;
        font-size: 26px;
      }
    }
    .btn {
      margin: 20px 30px;
      background: @greenColor;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
  }
}

//并但换台
.mask-box {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 83%;
    font-size: 30px;
    height: 87%;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content-remark {
      padding-bottom: 0;
      height: 79%;
    }
    .btn {
      margin: 20px auto;
      background: @greenColor;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
      width: 35%;
    }
  }
}
//并单模态框
.merge {
  .box {
    position: relative;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .content-remark {
      // height:auto;
      padding: 30px 20px 0 20px;
      overflow: hidden;
      width: 85%;
      margin: 0 auto;
      .lists {
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        background: none;
        .list-wrap {
          background-color: #f2f2f2;
          cursor: pointer;
          border: 5px solid #e6e6e6;
          width: 47%;
          height: 100%;
          .food_lists {
            height: 76%;
          }
        }
        .active {
          border-color: @greenColor;
        }
      }
    }
    .left {
      position: absolute;
      left: 20px;
      bottom: 50%;
      border: 1px solid @graphiteColor;
      border-radius: 3px;
      padding: 3px;
      cursor: pointer;
      .iconfont {
        font-size: 22px;
        color: @graphiteColor;
      }
    }
    .right {
      position: absolute;
      right: 20px;
      border: 1px solid @graphiteColor;
      border-radius: 3px;
      bottom: 50%;
      padding: 3px;
      cursor: pointer;
      .iconfont {
        font-size: 22px;
        color: @graphiteColor;
      }
    }
  }
}
.qrcode {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: #ffffff;
  min-width: 1366px;
  min-height: 768px;
}
.account {
  .box {
    .btn {
      margin: 20px 100px;
    }
  }
}
//抹零
.mask-zero {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 800px;
    font-size: 30px;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content {
      padding: 15px 30px;
      padding-bottom: 0;
      font-size: 26px;
      text-align: center;
      display: flex;
      flex-direction: column;
      .content-list {
        display: flex;
        flex-direction: row;
        .label {
          display: flex;
          align-items: center;
          flex: 2;
        }
        .text {
          text-align: center;
          flex: 3;
          color: #ff9c00;
        }
        .input {
          border: 1px solid #ccc;
          padding: 15px;
          outline: none;
          flex: 3;
          direction: ltr;
        }
      }
      .small-item {
        height: 65px;
      }
    }
    .btn-list {
      display: flex;
      .btn {
        margin: 30px 15px;
        background: @greenColor;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        padding: 16px 0;
        cursor: pointer;
        flex: 1;
      }
      .btn-left {
        background: #ff9c00;
      }
    }
  }
}
//混合支付
.mask-blend {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 800px;
    font-size: 30px;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content {
      padding: 15px 30px;
      padding-bottom: 0;
      font-size: 26px;
      text-align: center;
      display: flex;
      flex-direction: column;
      .content-body {
        padding: 15px 0;
        .body-item {
          display: flex;
          padding-bottom: 20px;
          .item-left {
            flex: 1;
            display: flex;
            align-items: center;
          }
          .item-right {
            flex: 1;
            input {
              width: 300px;
              border: 1px solid #ccc;
              outline: none;
              padding: 8px;
              direction: ltr;
              text-align: center;
            }
          }
        }
      }
      .content-bottom {
        display: flex;
        flex-direction: row;
        .bottom-item {
          display: flex;
          flex: 1;
          justify-content: center;
          .label {
            color: #333;
          }
          .text {
            color: #ff9c00;
          }
        }
      }
    }
    .btn-list {
      display: flex;
      .btn {
        margin: 15px;
        background: @greenColor;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        padding: 16px 0;
        cursor: pointer;
        flex: 1;
      }
      .btn-left {
        background: #ff9c00;
      }
    }
  }
}

@media screen and (max-width: 1920px) {
  .food-list {
    .lists {
      .list-wrap {
        .food_lists {
          height: 59%;
        }
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .food-list {
    .lists {
      .list-wrap {
        .food_lists {
          height: 56%;
        }
      }
    }
  }
  .btns {
    .scroll {
      width: 65%;
    }
  }
}
@media screen and (max-width: 1366px) {
  .food-list {
    .lists {
      .list-wrap {
        .food_lists {
          height: 53%;
        }
      }
    }
  }
  .mask-box {
    .box {
      width: 85%;
    }
  }
  .merge {
    .box {
      .content-remark {
        .lists {
          .list-wrap {
            .food_lists {
              height: 70%;
            }
          }
        }
      }
    }
  }
  .mask-box {
    .box {
      .content-remark {
        height: 74%;
      }
    }
  }
  .btns {
    .scroll {
      width: 58%;
    }
  }
}
.split-box {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .split {
    background-color: #ffffff;
    width: 1020px;
    font-size: 30px;
    height: 780px;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content-remark {
      display: flex;
      flex-direction: column;
      background: #f5f5f5;
      height: 606px;
      .food-title {
        display: flex;
        flex-direction: row;
        padding: 32px 32px 0;
        .title-item {
          flex: 1;
          text-align: center;
        }
      }
      .customer-count {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 32px 32px 0;
        .customer-item {
          flex: 1;
          text-align: center;
          display: flex;
          align-items: center;
          .count-input {
            width: 250px;
            height: 50px;
            padding: 20px;
            outline: none;
            font-size: 22px;
            direction: ltr;
            text-align: center;
          }
        }
        .item-right {
          padding: 0 32px;
        }
        .customer-ug {
          direction: rtl;
        }
      }
      .foods-box {
        display: flex;
        flex-direction: row;
        .food_lists:last-child {
          border-right: none;
        }
        .food_lists {
          flex: 1;
          height: 415px;
          overflow-y: scroll;
          padding: 0 32px;
          margin-top: 32px;
          border-right: 1px dashed #ccc;
          .active-item {
            border: 1px solid #139d59;
            color: #139d59;
            background: #f5f5f5;
          }
          .food-item {
            padding: 15px 15px 10px;
            cursor: pointer;
            background: white;
            margin-bottom: 10px;
            border: 1px solid transparent;
            .food-name {
              font-size: 26px;
            }
            .add-price {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding-top: 15px;
              .price-remark {
                display: flex;
                width: 50%;
                align-items: center;
                .price {
                  font-size: 22px;
                  // color:#ff5151;
                }
                .remark {
                  font-size: 22px;
                  color: @greenColor;
                  width: 150px;
                  display: inline-block;
                  overflow: hidden;
                  height: 20px;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  padding: 2px 10px;
                  height: 100%;
                  width: 90%;
                }
                .retreated {
                  font-size: 18px;
                  background: @graphiteColor;
                  padding: 5px;
                  color: @grayColor;
                  margin: 0 10px;
                }
              }
              .old-price {
                width: 29%;
                font-size: 22px;
                color: #d15e4b;
              }
              .add-minus {
                width: 21%;
                font-size: 22px;
              }
            }
            .two-btn {
              display: flex;
              justify-content: space-between;
              div {
                width: 48%;
                background-color: @graphiteColor;
                font-size: 26px;
                padding: 15px 0;
                text-align: center;
              }
            }
          }
        }
      }
    }
    .btn {
      margin: 20px auto;
      background: @greenColor;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
      width: 35%;
    }
  }
}

/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

@-webkit-keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
  }
}

.vip-modal {
  .box {
    width: 700px;
  }
}

.cancel-btn-max {
  text-align: center;
  padding: 10px 0;
  background-color: @graphiteColor;
  font-size: 22px;
  margin-top: 10px;
  cursor: pointer;
}

.qrcode-box {
  min-height: 550px;
  .qrcode-svg {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
