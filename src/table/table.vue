<template>
  <div class="wrap">
    <!-- 菜单和搜索框 -->
    <div class="top tops">
      <div class="menu" :class="gLang == 2 ? 'menu-zh' : ''">
        <el-tabs
          v-model="activeName"
          type="card"
          class="menu-item"
          @tab-click="clickMenu"
        >
          <el-tab-pane
            v-for="item in tables"
            :key="item.id"
            :label="item.name"
            :name="item.item"
            :id="item.id"
          >
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="serach">
        <div class="input">
          <span class="iconfont icon-search search"></span>
          <input
            type="text"
            oninput="if(value.length>12)value=value.slice(0,12)"
            :placeholder="gL('tableName') + '/' + gL('num')"
            v-model="serachText"
            v-on:input="inputSearch"
          />
        </div>
        <div class="del" v-if="delete_text" @click="removeSerach">
          <span class="iconfont icon-jia-copy"></span>
        </div>
      </div>
    </div>
    <!-- 餐桌 -->
    <div class="table">
      <div class="box">
        <div
          class="table-item"
          v-for="(item, index) in table_list"
          :key="index"
          @click="showNumberBox(item.table_status, item.id)"
        >
          <div
            class="table-num"
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          >
            {{ gLang == 1 ? item.name_ug : item.name_zh }}
          </div>
          <div class="table-img">
            <span
              class="iconfont icon-zhuozi"
              v-if="item.table_status == 1"
            ></span>
            <span
              class="iconfont icon-zhuozi green"
              v-if="item.table_status == 2"
            ></span>
            <span
              class="iconfont icon-wodedingdan zhang"
              v-if="item.table_status == 3"
            ></span>
            <span
              class="order-count"
              v-if="item.table_status > 1 && item.orders_count > 1"
              >{{ item.orders_count }}</span
            >
          </div>
          <div class="table-place">
            <span>{{ item.customers_count }}</span>
            <span>/</span>
            <span>{{ item.seating_capacity }}</span>
          </div>
        </div>
        <div class="empty" v-if="table_list.length == 0">
          {{ gL("noMessage") }}
        </div>
      </div>
      <div class="checkout">
        <div>{{ gL("notSettle") }}:{{ order_info.order_count }}</div>
        <div class="price" :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">
          {{ gL("checkoutPrice") }}:
          <span style="display: inline-block"
            >￥{{ $toMoney(order_info.order_amount) }}</span
          >
        </div>
      </div>
    </div>
    <!-- 餐桌状态 -->
    <div class="order-type">
      <div class="btn all">
        <div
          class="box"
          @click="clickInfo(0)"
          :class="table_info_id == 0 ? 'active' : ''"
        >
          <span class="num" v-if="table_info.emptyTableCount"
            >({{
              table_info.emptyTableCount +
                table_info.hasCustomersTableCount +
                table_info.hasOrderTableCount
            }})</span
          >
          <span class="text">{{ gL("all") }}</span>
        </div>
      </div>
      <div class="btn not" @click="clickInfo(1)">
        <div class="box" :class="table_info_id == 1 ? 'active' : ''">
          <span class="iconfont icon-zhuozi"></span>
          <span class="num">({{ table_info.emptyTableCount }})</span>
        </div>
      </div>
      <div class="btn open" @click="clickInfo(2)">
        <div class="box" :class="table_info_id == 2 ? 'active' : ''">
          <span class="iconfont icon-zhuozi"></span>
          <span class="num">({{ table_info.hasCustomersTableCount }})</span>
        </div>
      </div>
      <div class="btn pri" @click="clickInfo(3)">
        <div class="box" :class="table_info_id == 3 ? 'active' : ''">
          <span class="iconfont icon-wodedingdan"></span>
          <span class="num">({{ table_info.hasOrderTableCount }})</span>
        </div>
      </div>
    </div>
    <!-- <div  v-if="number_box"> -->
    <numberBox
      :number_box="number_box"
      :table_id="table_id"
      @cancel="cancel"
    ></numberBox>
    <startJob :key="startJobKey" :number_box="job_box" @startBox="startBox"></startJob>
    <!-- </div> -->
  </div>
</template>

<script>
import numberBox from "../components/numberBox.vue";
import startJob from "../components/startJob.vue";
import { getAreasListAPI, getTablesListAPI } from './../api/index.js';
import { debounce } from "./../utils/utils.js"
var self;
var str;
export default {
  components: {
    numberBox,
    startJob
  },

  mounted() {
    window.electronAPI && electronAPI.watchMQTT((event, data) => {
      console.log("router -> ", this.$route);
      if (this.$route.path == "/table") {
        this.getTableList();
      }
    })
  },

  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    if (self.$route.query.is) {
      self.job_box = true;
      this.startJobKey = +new Date();
    }
    self.delete_text = false; //删除搜索框内容按钮
    self.serachText = ""; //搜索内容
    self.table_info_id = 0;

    //获取区域
    if (this.$route.query.isCancel) {
      setTimeout(() => {
        this.getTableList();
      }, 500);
    } else {
      this.getTableList();
    }
    this.getList();
  },

  data() {
    return {
      gLang: 1,
      tables: [],
      delete_text: false, //删除搜索框内容按钮
      serachText: "", //搜索内容
      active_menu: "", //选中的区或楼
      table_state: 1, //餐桌状态
      number_box: false, //新客模态框
      table_list: [], //列表
      table_status: "", //餐桌状态
      table_info: {}, //餐桌信息
      table_info_id: 0, //餐桌信息ID
      order_info: 0, //订单总额和数量
      table_id: "", //餐桌ID
      activeName: "first",
      job_box: false,
      num: 1,
      startJobKey: +new Date()
    };
  },
  methods: {
    //搜索输入框输入时候
    inputSearch() {
      debounce(self.getTableList, 1000);
      if (self.serachText.length != 0) {
        self.delete_text = true;
      } else {
        self.delete_text = false;
      }
    },
    //删除搜索内容
    removeSerach() {
      self.serachText = "";
      self.delete_text = false;
      self.getTableList();
    },
    //选择Top菜单
    clickMenu(tab, event) {
      self.active_menu = tab.$attrs.id;
      self.table_info_id = 0;
      self.getTableList();
    },
    /**
     * 显示新客模态框
     */
    showNumberBox(e, s) {
      self.table_id = s;
      if (e == 1) {
        self.number_box = true;
      } else if (e == 2) {
        self.$router.push({ name: "foodList", params: { id: self.table_id } });
      } else if (e == 3) {
        self.$router.push({ name: "checkOut", params: { id: self.table_id } });
      }
    },
    /**
     * 关掉模态框
     */
    cancel(e) {
      self.number_box = false;
    },
    /**
     * 获取区域
     */
    getList() {
      getAreasListAPI()
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            var all = { name: self.gL("all"), item: "first", id: 0 };
            response.data.data.unshift(all);
            self.tables = response.data.data;
          }
        });
    },
    /**
     * 餐桌列表
     */
    getTableList() {
      var data = {};
      if (self.active_menu != "") {
        data.area_id = self.active_menu;
      }
      if (self.table_info_id != "") {
        data.table_status = self.table_info_id;
      }
      if (self.serachText != "") {
        data.keyword = self.serachText;
      }

      getTablesListAPI(data)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.table_list = response.data.data.data;
            self.table_info = response.data.data.table_info;
            self.order_info = response.data.data.order_info;
            localStorage.setItem("order_count", self.order_info.order_count);
          }
        });
    },
    /**
     * 点击餐桌信息
     */
    clickInfo(e) {
      self.table_info_id = e;
      self.getTableList();
    },
    /**
     * 点击餐桌信息
     */
    startBox() {
      self.job_box = false;
      // self.$router.push({ path: "/table" });
    }
  },
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;

.empty {
  margin: 0 auto;
  font-size: 22px;
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -25px;
  margin-left: -100px;
  color: #666;
}
.wrap {
  //  position: absolute;
  //  top: 0;
  //  bottom: 0;
  height: 100%;
  width: 100%;
  //菜单和输入框
  .top {
    width: 100%;
    background-color: @bgColor;
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    .menu {
      width: 76%;
      display: flex;
      color: #ffffff;
      height: 100%;
      // overflow-x: scroll;
      .menu-item {
        width: 100%;
        height: 100%;
        // padding: 0 25px;
        // line-height: 40px;
        font-size: 26px;
        cursor: pointer;
      }
      .active {
        background-color: @greenColor;
      }
    }
    .serach {
      width: 250px;
      background: #ffffff;
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .input {
        width: 92%;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #666666;
        width: 70%;
      }
      .search {
        color: #666666;
        font-size: 24px;
        padding: 0 10px;
        vertical-align: -2px;
      }
      .del {
        width: 40px;
        height: 100%;
        background-color: #e6e6e6;
        text-align: center;
        line-height: 40px;
      }
    }
  }
  //餐桌
  .table {
    padding: 22px;
    padding-right: 0;
    background: #e5e5e5;
    height: 87.9%;
    width: 100%;
    position: relative;
    .box {
      overflow-y: scroll;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      height: 97%;
      position: relative;
      .table-item {
        width: 11%;
        min-width: 10%;
        margin-bottom: calc(11% / 7);
        background-color: #ffffff;
        padding: 10px 20px 0px 20px;
        height: 170px;
        position: relative;
        .table-num {
          font-size: 20px;
          color: #1a1a1a;
          padding-bottom: 18px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          padding-top: 5px;
          width: 100%;
          text-align: center;
        }
        .table-img {
          position: absolute;
          top: 50%;
          left: 50%;
          margin-top: -25px;
          margin-left: -25px;
          .iconfont {
            font-size: 55px;
            color: #e6e6e6;
          }
          .green {
            color: @greenColor;
          }
          .zhang {
            color: #ff9c00;
          }
          .order-count {
            position: absolute;
            top: -12%;
            right: 0;
            color: #ffffff;
            width: 25px;
            height: 25px;
            background: #d15e4b;
            border-radius: 30px;
            text-align: center;
            line-height: 26px;
          }
        }
        .table-place {
          font-size: 22px;
          color: #666666;
          background-color: #f2f2f2;
          width: 100%;
          text-align: center;
          padding: 10px 0;
          position: absolute;
          left: 0;
          bottom: 0;
        }
      }
      .table-item:not(:nth-child(8n)) {
        margin-right: calc(11% / 7);
      }
    }
    .checkout {
      font-size: 22px;
      color: #1a1a1a;
      display: flex;
      justify-content: center;
      padding-top: 10px;
      // position: absolute;
      // bottom: 20px;
      // width: 97%;
      .price {
        direction: rtl;
        padding-left: 70px;
      }
    }
  }
  .order-type {
    display: flex;
    //  padding: 5px;
    background: #ffffff;
    align-items: center;
    .btn {
      width: 25%;
      text-align: center;
      padding: 5px;
      // margin: 5px;
      border-right: 2px solid #e5e5e5;
      cursor: pointer;
      .box {
        // margin: 5px;
        padding: 10px 0;
      }
      .active {
        background-color: @bgColor;
        color: #ffffff;
      }
      .text {
        font-size: 22px;
      }
      .num {
        font-size: 22px;
      }
      .iconfont {
        font-size: 24px;
        padding-right: 5px;
      }
    }
    .all {
      direction: rtl;
    }
    .not {
      .iconfont {
        color: #cccccc;
      }
    }
    .open {
      .iconfont {
        color: @greenColor;
      }
    }
    .pri {
      .iconfont {
        color: #ff9c00;
      }
    }
  }
}
@media screen and (max-width: 1366px) {
  .wrap {
    .table {
      height: 87.9%;
    }
  }
}
</style>
