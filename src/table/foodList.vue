<template>
  <div class="wrapper">
    <div class="food-list">
      <!-- 购物车 -->
      <div class="lists">
        <div class="list-wrap">
          <div
            class="tit"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
          >
            <div
              class="table-customer"
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            >
              <span class="table-num">
                <span class="num">{{ table_detail.no }}</span>
                -{{ gL("table") }}
              </span>
              <span class="customer"
                >{{ table_detail.customers_count }}/{{
                  table_detail.seating_capacity
                }}</span
              >
            </div>
            <div class="icon" @click="backHandler">
              <span
                class="back-icon"
                :class="
                  gLang == 1 ? 'el-icon-arrow-left' : 'el-icon-arrow-right'
                "
              ></span>
            </div>
          </div>
          <div
            class="time-name"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
          >
            <div class="name">{{ customer_detail.user }}</div>
            <div class="name">{{ customer_detail.order_no }}</div>
            <div class="time-day">
              <!-- <span class="day">{{ day }}</span> -->
              <span class="time">{{
                formatDate(customer_detail.created_at, "HH:mm")
              }}</span>
            </div>
          </div>
          <div class="food_lists">
            <div
              class="food-item"
              v-for="(item, index) in cart_list"
              :key="index"
              @click="clickRemarkItem(item)"
            >
              <div
                class="food-name"
                :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
              >
                {{ gLang == 1 ? item.name_ug : item.name_zh }}
              </div>
              <div
                class="add-price"
                :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
              >
                <div
                  class="price-remark"
                  :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                >
                  <span class="price">￥{{ item.price }}</span>
                  <span class="remark" v-if="item.remark">{{
                    item.remark
                  }}</span>
                </div>
                <div
                  class="add-minus"
                  v-if="item.format_id == 1"
                  :style="{ justifyContent: gLang == 2 ? 'flex-end' : '' }"
                >
                  <span
                    class="iconfont icon-jian"
                    @click.stop="minusFood(item)"
                  ></span>
                  <span class="num">{{ item.count }}</span>

                  <span
                    class="iconfont icon-jia1"
                    @click.stop="clickFood(item)"
                  ></span>
                </div>
                <div class="kilo" v-if="item.format_id == 2">
                  {{ item.count }}kg
                </div>
                <div
                  class="icon-edit"
                  @click.stop="clickFoodNow(item)"
                  v-if="item.format_id == 2"
                  :style="{
                    marginLeft: gLang == 2 ? '15px' : '',
                    marginRight: gLang == 1 ? '15px' : ''
                  }"
                >
                  <span class="iconfont icon-shiliangzhinengduixiang"></span>
                </div>
              </div>
            </div>
          </div>
          <div class="price-count">
            <div class="del-price-count">
              <span
                class="iconfont icon-qingkong"
                @click="removeMessage"
                v-if="price_count.count > 0"
              ></span>
              <span class="price">{{
                price_count.price > 0 ? "￥" + $toMoney(price_count.price) : ""
              }}</span>
              <span class="count">{{
                price_count.count > 0 ? "x" + price_count.count : ""
              }}</span>
            </div>
          </div>
          <div
            class="remark-btn"
            @click="remarkAll"
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          >
            <!-- <span>{{gL('orderRemark')}}</span> -->
            <textarea
              :placeholder="gL('orderRemark')"
              v-model="all_remark_input"
              readonly
              rows="2"
            ></textarea>
          </div>
          <div
            class="cancel-btn"
            @click="cancelMessage"
            v-if="!$route.query.isAppend"
          >
            <span>{{ gL("cancelOrder") }}</span>
          </div>
        </div>
        <div class="wave">
          <img src="../../static/images/wave.png" alt />
        </div>
      </div>
      <div class="btns">
        <div
          class="orders"
          v-if="table_detail.orders"
          :class="[
            table_detail.orders.length > 10 ? 'scrolls' : '',
            table_detail.orders.length > 5 ? 'scroll' : ''
          ]"
        >
          <span
            class="order-item"
            v-for="(item, index) in table_detail.orders"
            :key="index"
            :class="active_order == index ? 'active' : ''"
            @click="changeTable(item, index)"
            >{{ index + 1 }}</span
          >
        </div>
        <div
          class="new"
          :class="gLang == 2 ? 'new-zh' : ''"
          @click="showNumberBox"
        >
          {{ gL("newGuest") }}
        </div>
      </div>
    </div>
    <div class="wrap">
      <!-- 菜单和搜索框 -->
      <div class="top">
        <div class="menu-wrap">
          <div class="menu">
            <el-tabs
              v-model="activeName"
              type="card"
              class="menu-item"
              @tab-click="clickMenu"
            >
              <el-tab-pane
                v-for="item in tables"
                :key="item.id"
                :label="gLang == 1 ? item.name_ug : item.name_zh"
                :name="item.item"
                :id="item.id"
              ></el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <div class="serach">
          <div class="input">
            <span class="iconfont icon-search search"></span>
            <input
              type="text"
              :placeholder="gL('shortcut') + '/' + gL('names')"
              v-model.trim="serachText"
              maxlength="8"
              v-on:input="inputSearch"
            />
          </div>
          <div class="del" v-if="delete_text" @click="removeSerach">
            <span class="iconfont icon-jia-copy"></span>
          </div>
        </div>
      </div>
      <!-- 美食 -->
      <div class="table">
        <div class="box">
          <div
            class="food-item"
            v-for="item in foodList"
            :key="item.id"
            @click="clickFoodNow(item)"
            :class="[
              item.active ? 'active-foods' : '',
              ((item.cell_clear_state == true) & (item.remaining_count == 0))
              | ((item.cell_clear_state == true) &
                (item.remaining_count - item.count == 0))
                ? 'over'
                : ''
            ]"
          >
            <div class="img">
              <img :src="item.image + '?x-oss-process=style/w20'" />
            </div>
            <div
              class="name"
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            >
              {{ gLang == 1 ? item.name_ug : item.name_zh }}
            </div>
            <div
              class="bottom"
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            >
              <span class="price">￥{{ item.price }}</span>
              <div
                class="remain"
                v-show="item.cell_clear_state == true"
                :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
              >
                <span>{{ gL("surplus") }}:</span>
                <span class="num" v-if="!item.count">{{
                  Number(item.remaining_count)
                }}</span>
                <span class="num" v-if="item.count">{{ fiexdNum(item) }}</span>
              </div>
            </div>
            <div class="maskk">
              <span>{{ gL("soldOut") }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 下单 -->
      <div class="order-type">
        <!-- <div
          class="check"
          :class="print_order ? 'active' : ''"
          @click="printOrder"
        >
          <div class="check-item">{{ gL("invoice") }}</div>
        </div> -->
        <!--<div class="order" :class="gLang==2?'order-zh':''"  @click="orderCommit">{{gL('ordering')}}</div>-->
        <el-button
          type="primary"
          class="order order-btns"
          @click="orderCommit"
          :loading="commitBt"
          >{{ gL("ordering") }}</el-button
        >
      </div>
      <!-- <div  v-if="number_box"> -->
      <numberBox
        :number_box="number_box"
        :table_id="table_id"
        @cancel="cancel"
      ></numberBox>
      <!-- </div> -->
      <!-- 模态框 -->
      <div class="mask" v-if="messageBox">
        <div class="box">
          <div class="title">
            <span></span>
            <span>{{ mesaage_title }}</span>
            <span
              class="iconfont icon-jia-copy"
              @click="cancelMessageBox"
            ></span>
          </div>
          <div class="content">{{ mesaage_content }}</div>
          <div class="btn" @click="messageClick">{{ gL("confirm") }}</div>
        </div>
      </div>
      <!-- 备注 -->
      <div class="mask" v-if="remark_box">
        <div class="box">
          <div class="title">
            <span
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
              style="display: inline-block"
              >{{ remark_title }}</span
            >
            <span
              class="iconfont icon-jia-copy"
              @click="cancelRemarkBox"
            ></span>
          </div>
          <div
            class="content-remark"
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          >
            <div class="remark">{{ gL("remark") }}</div>
            <div class="remark-list">
              <div
                class="remark-item"
                v-for="item in remark_list"
                :key="item.id"
                :identifier="'r' + item.id"
                @click="remarkItem(item.name, item.id)"
              >
                {{ item.name }}
              </div>
              <!-- <div class='remark-item' @click="remarkItem(gL('Spice'))">{{gL('Spice')}}</div>
              <div class='remark-item' @click="remarkItem(gL('hot'))">{{gL('hot')}}</div>
              <div class='remark-item' @click="remarkItem(gL('cold'))">{{gL('cold')}}</div>-->
            </div>
            <div class="input">
              <!-- <input type="text" :placeholder="gL('otherRemark')" > -->
              <textarea
                cols="30"
                :placeholder="gL('otherRemark')"
                rows="2"
                v-model="remark_input"
              ></textarea>
            </div>
          </div>
          <div class="btn" @click="remarkClick">{{ gL("confirm") }}</div>
        </div>
      </div>
    </div>
    <kiloBox ref="mychild" :number_box="kilo_box" @cancel="cancel"></kiloBox>

    <!-- 选择套餐弹出框 -->
    <div class="combos-modal">
      <el-dialog
        :title="gL('combosModalTitle')"
        :visible.sync="showCombosModal"
        width="50%"
        center
      >
        <div
          class="combos-box"
          :class="gLang == 1 ? 'combos-box-ug' : 'combos-box-zh'"
        >
          <div
            class="combos-item"
            v-for="(combos, combosIndex) in combosInfo.combos"
            :key="combos.id"
          >
            <div class="combos-title">
              <div>
                <span>{{ gL("combosName") }} : </span>
                <span>{{ gLang == 1 ? combos.name_ug : combos.name_zh }}</span>
              </div>
              <div class="combos-title-count">
                <span>{{ gL("count") }} : </span>
                <span>{{ combos.count }}</span>
              </div>
            </div>

            <div class="combos-check">
              <div
                class="check-item"
                :class="food.count > 0 ? 'active' : ''"
                @click="
                  () => changeCheckCombosFood(food, combosIndex, foodIndex)
                "
                v-for="(food, foodIndex) in combos.childs"
                :key="food.id"
              >
                <div
                  v-if="food.count > 0"
                  class="check-food-icon"
                  @click.stop="
                    () => closeCheckCombosFood(food, combosIndex, foodIndex)
                  "
                >
                  <i class="el-icon-circle-close-outline"></i>
                </div>
                <div class="check-food-name">
                  {{ gLang == 1 ? food.name_ug : food.name_zh }}
                </div>
                <div v-if="food.count > 0" class="check-food-count">
                  <span>{{ food.count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="confirmCombosModal">{{
            gL("confirm")
          }}</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import numberBox from "../components/numberBox.vue";
import kiloBox from "../components/kiloBox.vue";
import { formatDate } from "../utils/utils.js";
import {
  getTableDetailsAPI,
  getFoodCategoriesAPI,
  getFoodsAPI,
  getRemarksAPI,
  postOrderCancelAPI,
  postOrderDetailsAPI
} from "./../api/index.js";
var self, str;
export default {
  name: "FoodList",
  created() {
    const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
    str = "private-" + storeInfo.merchant_no + ":App\\Events\\TableStatus";
    // if (this.sockets) {
    //   this.sockets.subscribe(str, data => {
    //     if (data.table_id == this.table_id) {
    //       self.getList();
    //       self.getFoodList();
    //     }
    //   });
    // }
  },
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.cart_list = [];
    self.table_detail = "";
    if (self.$route.params.id) {
      self.table_id = self.$route.params.id;
    } else {
      self.$router.push("/table");
    }
    if (self.$route.query.id) {
      self.query_order_id = self.$route.query.id;
      self.tableDetail(self.query_order_id);
    } else {
      self.tableDetail();
    }
    //清空数据
    self.commitBt = false;
    self.price_count = { count: 0, price: 0 };
    (self.delete_text = false), //删除搜索框内容按钮
      (self.serachText = ""), //搜索内容
      (self.all_remark_input = ""),
      (self.foodList = []);
    //获取区域
    self.getList();
    self.getFoodList();
    self.getRemarkList();
    self.getOrderRemarkList();
  },

  data() {
    return {
      gLang: 1,
      tables: [],
      delete_text: false, //删除搜索框内容按钮
      serachText: "", //搜索内容
      active_menu: "", //选中的区或楼
      number_box: false, //新客模态框
      table_id: "", //餐桌ID
      activeName: "first",
      foodList: [],
      print_order: true, //打印后堂
      cart_list: [], //购物车列表
      price_count: { count: 0, price: 0 }, //价格和数量
      table_detail: [], //餐桌详情
      customer_detail: {}, //详情
      order_id: "", //订单ID
      query_order_id: "", //url订单ID
      active_order: 0,
      messageBox: false,
      mesaage_title: "",
      mesaage_content: "",
      mesaage_state: 0, //模态框1清空购物车2撤台
      remark_input: "", //备注input
      remark_title: this.gL("orderRemark"), //备注标题
      remark_box: false,
      remark_state: [],
      remark_list: [],
      remark_lists: [],
      order_remark_list: 1,
      remark_item: {}, //点击美食数据
      all_remark_input: "", //订单备注
      commitBt: false, //
      kilo_box: false, //公斤输入框
      now_food: "", //点击的美食
      defaultImg: require("../../static/images/defau.png"),
      showCombosModal: false, //套餐模态框
      combosInfo: {}, // 套餐信息
      checkFood: [],
      formatDate: formatDate
    };
  },
  methods: {
    backHandler() {
      this.$router.back();
    },

    fiexdNum(e) {
      var num = e.remaining_count - e.count;
      return num.toFixed(2);
    },
    //搜索输入框输入时候
    inputSearch() {
      setTimeout(() => {
        self.getFoodList();
      }, 300);
      if (self.serachText.length != 0) {
        self.delete_text = true;
      } else {
        self.delete_text = false;
      }
    },
    //删除搜索内容
    removeSerach() {
      self.serachText = "";
      self.delete_text = false;
      self.getFoodList();
    },
    //选择Top菜单
    clickMenu(tab, event) {
      self.active_menu = tab.$attrs.id;
      self.getFoodList();
    },
    /**
     * 显示新客模态框
     */
    showNumberBox() {
      self.number_box = true;
    },
    /**
     * 获取区域
     */
    getList() {
      getFoodCategoriesAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          console.log("-------------- data -------", response.data);
          self.tables = [
            {
              name_ug: self.gL("all"),
              name_zh: self.gL("all"),
              item: "first",
              id: 0
            },
            ...response.data.data
          ];
        }
      });
    },
    /**
     * 备注
     */
    getRemarkList() {
      getRemarksAPI({
        tag: "food"
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.remark_lists = response.data.data;
        }
      });
    },
    /**
     * 订单备注
     */
    getOrderRemarkList() {
      getRemarksAPI({
        tag: "order"
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.order_remark_list = response.data.data;
        }
      });
    },
    /**
     * 获取美食
     */
    getFoodList() {
      var data = {};
      if (self.active_menu != "") {
        data.food_category_id = self.active_menu;
      }
      if (self.serachText != "") {
        data.keyword = self.serachText;
      }

      getFoodsAPI(data).then(response => {
        if (response.status >= 200 && response.status < 300) {
          response.data.data.forEach(item => {
            self.$set(item, "active", false);
            self.$set(item, "count", 0);
            if (self.cart_list.length != 0) {
              self.cart_list.forEach(items => {
                if (items.id == item.id) {
                  item.active = true;
                  // item.remaining_count=item.remaining_count-items.count;
                  item.count = items.count;
                }
              });
            }
          });
          self.foodList = response.data.data;
        }
      });
    },

    clickFoodNow(e) {
      if (e.format_id == 2) {
        self.kilo_box = true;
        self.$refs.mychild.openBox(e);
        self.now_food = e;
        self.priceCount();
        return;
      } else {
        if (e.combos.length) {
          this.showCombosModal = true;
          this.combosInfo = e;
        } else {
          self.clickFood(e);
        }
      }
    },
    /**
     * 点击美食
     */
    clickFood(e, s) {
      let record = self.cart_list.find(n => n.id == e.id);
      let index = self.cart_list.findIndex(n => n.id == e.id);
      if (e.format_id == 2) {
        e.active = true;
        if (record) {
          if (s == 0) {
            self.cart_list.splice(index, 1);
            e.active = false;
          } else {
            record.count = s;
          }
        } else {
          if (s == 0) {
            e.active = false;
          } else {
            e.count = s;
            self.cart_list.push(e);
          }
        }
        self.priceCount();
        return;
      }
      if (e.cell_clear_state == true) {
        if (e.remaining_count != 0) {
          if (self.cart_list.length != 0) {
            if (record) {
              if (record.count < e.remaining_count) {
                e.active = true;
                record.count++;
                e.count = record.count;
              }
            } else {
              e.active = true;
              e.count = 1;
              self.cart_list.push(e);
            }
          } else {
            e.active = true;
            e.count = 1;
            self.cart_list.push(e);
          }
        }
      } else {
        e.active = true;
        if (self.cart_list.length != 0) {
          if (record) {
            record.count++;
          } else {
            e.count = 1;
            self.cart_list.push(e);
          }
        } else {
          e.count = 1;
          self.cart_list.push(e);
        }
      }
      self.priceCount();
    },
    /**
     * 减数量
     */
    minusFood(e) {
      let record = self.cart_list.find(n => n.id == e.id);
      let index = self.cart_list.findIndex(n => n.id == e.id);
      if (record) {
        if (record.count != 1) {
          record.count--;
        } else {
          record.count--;
          self.cart_list.splice(index, 1);
          e.active = false;
        }
        e.count = record.count;
      }
      for (let i = 0; i < this.foodList.length; i++) {
        if (record.id == this.foodList[i].id) {
          if (record.count == 0) {
            this.foodList[i].active = false;
          }
        }
      }
      self.priceCount();
    },
    /**
     * 清空购物车
     */
    removeCart(e) {
      self.cart_list = [];
      self.price_count = { count: 0, price: 0 };
      self.getFoodList();
    },
    /**
     * 清空模态框
     */
    removeMessage(e) {
      self.mesaage_state = 1;
      self.messageBox = true;
      self.mesaage_title = self.gL("detailedList");
      self.mesaage_content = self.gL("detailedListContent");
    },
    /**
     * 撤台模态框
     */
    cancelMessage(e) {
      self.mesaage_state = 2;
      self.messageBox = true;
      self.mesaage_title = self.gL("cancelOrder");
      self.mesaage_content = self.gL("cancelOrderContent");
    },
    /**
     * 撤台
     */
    cancelorder(e) {
      postOrderCancelAPI(self.order_id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.tableDetail();
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
        }
      });
    },
    /**
     * 清空模态框
     */
    messageClick() {
      if (self.mesaage_state == 1) {
        self.messageBox = false;
        self.removeCart();
      }
      if (self.mesaage_state == 2) {
        self.messageBox = false;
        self.cancelorder();
      }
    },
    /**
     * 关掉模态框
     */
    cancelMessageBox(e) {
      self.messageBox = false;
    },
    /**
     * 关掉模态框
     */
    cancel(e, s) {
      self.number_box = false;
      if (e == 3 || e == 4) {
        self.kilo_box = false;
      }
      if (e == 4) {
        self.clickFood(self.now_food, s);
      }
      if (e == 2) {
        self.cart_list = [];
        self.tableDetail(s);
        self.getFoodList();
      }
    },
    /**
     * 计算总价格和数量
     */
    priceCount() {
      self.price_count.price = 0;
      self.price_count.count = 0;
      self.price_count.count = self.cart_list.length;
      self.cart_list.forEach(item => {
        self.price_count.price += item.count * item.price;
      });
    },
    /**
     * 餐桌详情
     */
    tableDetail(s) {
      // getTableDetailItemAPI(self.table_id)
      getTableDetailsAPI(self.table_id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          console.log("---------- 获取订单详情成功 ----------", response.data);
          if (response.data.table.orders.length != 0) {
            self.table_detail = response.data.table;
            if (s) {
              self.table_detail.orders.forEach(function(item, index) {
                if (item.id == s) {
                  self.customer_detail = item;
                  self.order_id = item.id;
                  self.active_order = index;
                }
              });
            } else {
              self.customer_detail = response.data.table.orders[0];
              self.order_id = response.data.table.orders[0].id;
              self.active_order = 0;
              if (
                self.customer_detail.canceled_foods.length != 0 ||
                self.customer_detail.order_details.length != 0
              ) {
                self.$router.push({
                  name: "checkOut",
                  params: { id: self.table_id },
                  query: { id: self.customer_detail.id }
                });
              }
            }
          } else {
            self.$router.push({ path: "/table" });
          }
        }
      });
    },
    doHandleMonth(month) {
      var m = month;
      if (month.toString().length == 1) {
        m = "0" + month;
      }
      return m;
    },
    /**
     * 更换订单
     */
    changeTable(e, s) {
      if (e.canceled_foods.length != 0 || e.order_details.length != 0) {
        self.$router.push({
          name: "checkOut",
          params: { id: self.table_id },
          query: { id: e.id, active_order: s }
        });
      }
      self.customer_detail = e;
      self.order_id = e.id;
      self.active_order = s;
      self.cart_list = [];
      self.price_count = { count: 0, price: 0 };
      self.getFoodList();
    },
    /**
     * 备注
     */
    remarkItem(e, v) {
      let remarks = document.querySelectorAll(".remark-item");
      remarks.forEach(element => {
        element.classList.remove("changeColor");
      });
      let remark = document.querySelector(
        ".remark-item[identifier=r" + v + "]"
      );
      remark.classList.add("changeColor");

      if (self.gLang == 1) {
        if (self.remark_input != "") {
          self.remark_input += "،" + e;
        } else {
          self.remark_input += e;
        }
      } else {
        if (self.remark_input != "") {
          self.remark_input += "，" + e;
        } else {
          self.remark_input += e;
        }
      }
    },
    /**
     * 点击购物车美食
     */
    clickRemarkItem(e) {
      self.remark_box = true;
      self.remark_title = e.name;
      self.remark_item = e;
      self.remark_state = 1;
      self.remark_list = self.remark_lists;
      console.log(self.remark_list);
    },
    /**
     * 点击订单备注
     */
    remarkAll() {
      self.remark_box = true;
      self.remark_title = self.gL("orderRemark");
      self.remark_state = 2;
      self.remark_list = self.order_remark_list;
      console.log(self.remark_list);
    },
    /**
     * 备注关闭
     */
    cancelRemarkBox() {
      self.remark_box = false;
      self.remark_input = "";
    },
    /**
     * 备注完成
     */
    remarkClick() {
      if (self.remark_state == 1) {
        self.$set(self.remark_item, "remark", self.remark_input);
      } else {
        self.all_remark_input = self.remark_input;
      }
      self.cancelRemarkBox();
    },
    /**
     * 提交订单
     */
    orderCommit() {
      self.commitBt = true;
      if (self.cart_list.length == 0) {
        self.commitBt = false;
        self.$message({
          message: self.gL("chooiseFood"),
          type: "error",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      }
      let isNewAddFood = localStorage.getItem("isNewAddFood");
      if (isNewAddFood == "true") {
        localStorage.removeItem("isNewAddFood");
        var order_details = [];
        self.cart_list.forEach(item => {
          var items = { food_id: "", foods_count: "", remarks: "" };
          items.food_id = item.id;
          items.foods_count = parseFloat(item.count);
          if (item.remark) {
            items.remarks = item.remark;
          }
          if (item.combos_data && item.combos_data.length) {
            items.combo_info = item.combos_data;
          }
          order_details.push(items);
        });

        postOrderDetailsAPI(self.order_id, {
          order_details: order_details,
          order_remark: self.all_remark_input
        })
          .then(response => {
            if (response.status >= 200 && response.status < 300) {
              self.$message({
                message: self.gL("successfulOperation"),
                type: "success",
                customClass: self.$toastClass(),
                offset: 120
              });

              self.$router.push({
                name: "checkOut",
                params: { id: self.table_id },
                query: { id: self.order_id, active_order: this.active_order }
              });
            }
          })
          .finally(() => {
            self.commitBt = false;
          });
      } else {
        var order_details = [];
        self.cart_list.forEach(item => {
          var items = { food_id: "", foods_count: "", remarks: "" };
          items.food_id = item.id;
          items.foods_count = parseFloat(item.count);
          if (item.remark) {
            items.remarks = item.remark;
          }
          if (item.combos_data && item.combos_data.length) {
            items.combo_info = item.combos_data;
          }
          order_details.push(items);
        });

        postOrderDetailsAPI(self.order_id, {
          order_details: order_details,
          order_remark: self.all_remark_input
        })
          .then(response => {
            if (response.status >= 200 && response.status < 300) {
              self.$message({
                message: self.gL("successfulOperation"),
                type: "success",
                customClass: self.$toastClass(),
                offset: 120
              });

              self.$router.push({
                name: "checkOut",
                params: { id: self.table_id },
                query: { id: self.order_id }
              });
            }
          })
          .finally(() => {
            self.commitBt = false;
          });
      }
    },

    // 点击选择套餐弹出框的确定按钮
    confirmCombosModal(e) {
      for (let i = 0; i < this.combosInfo.combos.length; i++) {
        let count = 0;
        for (let j = 0; j < this.combosInfo.combos[i].childs.length; j++) {
          count += this.combosInfo.combos[i].childs[j].count;
        }
        if (this.combosInfo.combos[i].count > count) {
          this.$message({
            message: `<div style="${
              this.gLang == 1 ? "direction: rtl;" : "direction: ltr;"
            }">${this.gL("noSelectCombosFoodsTips")
              .replace(
                "%s",
                this.gLang == 1
                  ? this.combosInfo.combos[i].name_ug
                  : this.combosInfo.combos[i].name_zh
              )
              .replace("%n", this.combosInfo.combos[i].count)}</div>`,
            type: "warning",
            dangerouslyUseHTMLString: true,
            center: true,
            offset: 120
          });
          return;
        }
      }

      this.combosInfo.active = true;
      this.clickFood({ ...this.combosInfo, combos_data: this.checkFood });
      this.showCombosModal = false;
      this.$nextTick(() => {
        this.combosInfo = {};
        this.checkFood = [];
      });
    },

    // 选择套餐美食
    changeCheckCombosFood(value, combosIndex, foodIndex) {
      let count = 0;
      for (
        let i = 0;
        i < this.combosInfo.combos[combosIndex].childs.length;
        i++
      ) {
        count += this.combosInfo.combos[combosIndex].childs[i].count;
      }
      if (this.combosInfo.combos[combosIndex].count <= count) {
        this.$message({
          message: this.gL("selectCombosFoodsTips").replace(
            "%s",
            this.combosInfo.combos[combosIndex].count
          ),
          type: "warning",
          offset: 120
        });
      } else {
        const index = this.checkFood.findIndex(item => item.id == value.id);
        if (index == -1) {
          this.checkFood.push({
            id: value.id,
            food_id: value.food_id,
            count: 1
          });
          this.combosInfo.combos[combosIndex].childs[foodIndex].count = 1;
        } else {
          this.checkFood[index].count += 1;
          this.combosInfo.combos[combosIndex].childs[foodIndex].count += 1;
        }
      }
    },

    // 删除已选择的套餐
    closeCheckCombosFood(value, combosIndex, foodIndex) {
      const index = this.checkFood.findIndex(item => item.id == value.id);
      this.checkFood.splice(index, 1);
      this.combosInfo.combos[combosIndex].childs[foodIndex].count = 0;
    }
  },
  // sockets: {
  //   //不能改,j建立连接自动调用connect
  //   connect: function() {
  //     //与socket.io连接后回调
  //     console.log("socket connected");
  //   },
  //   disconnect() {
  //     this.isConnected = false;
  //   }
  // },
  components: {
    numberBox,
    kiloBox
  }
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.changeColor {
  border: 1px solid #139d59;
  color: #139d59;
}
.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
}
.food-list {
  width: 30%;
  height: 100%;
  .lists {
    width: 100%;
    height: 90%;
    background-color: #f2f2f2;
    position: relative;
    .list-wrap {
      padding: 0 10px;
      height: 100%;
      .tit {
        display: flex;
        justify-content: space-between;
        padding: 12px 0 20px 0;
        .table-customer {
          color: @textColor;
          .table-num {
            font-size: 30px;
            .num {
              font-weight: bold;
            }
          }
          .customer {
            padding: 0 20px;
            font-size: 26px;
          }
        }
        .icon {
          .iconfont {
            font-size: 30px;
            color: @grayColor;
            cursor: pointer;
          }
        }
      }
      .time-name {
        display: flex;
        justify-content: space-between;
        font-size: 22px;
        color: @grayColor;
        border-bottom: 1px solid @graphiteColor;
        padding-bottom: 5px;
        .time-day {
          .time {
            padding: 0 5px;
          }
        }
      }
      .food_lists {
        height: 56%;
        overflow-y: scroll;
        padding: 0 10px;
        .food-item {
          padding-top: 15px;
          border-bottom: 1px dashed @graphiteColor;
          padding-bottom: 10px;
          cursor: pointer;
          .food-name {
            font-size: 26px;
          }
          .kilo {
            font-size: 22px;
          }
          .icon-edit {
            .iconfont {
              color: @greenColor;
              font-size: 26px;
            }
          }
          .add-price {
            display: flex;
            justify-content: space-between;
            padding-top: 15px;
            align-items: center;
            .price-remark {
              display: flex;
              width: 70%;
              .price {
                font-size: 22px;
                color: @grayColor;
              }
              .remark {
                font-size: 22px;
                color: @greenColor;
                width: 150px;
                display: inline-block;
                overflow: hidden;
                height: 20px;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding: 2px 0;
                height: 100%;
                width: 90%;
              }
            }
            .add-minus {
              display: flex;
              align-items: center;
              width: 30%;
              .iconfont {
                font-size: 30px;
                color: @greenColor;
                cursor: pointer;
              }
              .num {
                font-size: 22px;
                display: inline-block;
                width: 40px;
                text-align: center;
              }
            }
          }
        }
      }
      .price-count {
        height: 10%;
        .del-price-count {
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          height: 100%;
          // padding-top: 50px;
          .iconfont {
            font-size: 30px;
            color: @grayColor;
            cursor: pointer;
          }
          .price {
            font-size: 30px;
            color: #ff9c00;
            font-weight: bold;
          }
          .count {
            color: @grayColor;
            font-size: 26px;
          }
        }
      }
      .remark-btn {
        background: #ffffff;
        color: @grayColor;
        height: 10%;
        margin-top: 15px;
        padding: 10px;
        font-size: 22px;
        cursor: pointer;
        overflow: hidden;
        textarea {
          outline: none;
          color: #666666;
          width: 100%;
          resize: none;
          font-family: "Alp Ekran";
          border: none;
          font-size: 22px;
          cursor: pointer;
        }
      }
      .cancel-btn {
        text-align: center;
        padding: 10px 0;
        background-color: @graphiteColor;
        font-size: 22px;
        margin-top: 10px;
        cursor: pointer;
      }
    }
    .wave {
      position: absolute;
      bottom: -20px;
      width: 100%;
      img {
        width: 100%;
      }
    }
  }
  .lists::after,
  .lists::before {
    content: "";
    display: block;
    width: 100%;
    position: absolute;
    border-top: 10px dotted #f2f2f2;
    transform: translateY(-50%);
    z-index: -1;
  }
  .lists::after {
    top: 0;
    transform: translateY(-50%);
    z-index: -1;
  }
  .lists::before {
    bottom: 0;
    transform: translateY(50%);
    z-index: -1;
  }
  .btns {
    font-size: 22px;
    display: flex;
    padding-top: 25px;
    justify-content: space-between;
    align-items: center;
    .new {
      padding: 20px 40px;
      background-color: #d15e4b;
      color: #ffffff;
      cursor: pointer;
    }
    .new-zh {
      padding: 20px 40px;
    }
    .orders {
      .order-item {
        border: 2px solid @grayColor;
        border-radius: 5px;
        padding: 0 10px;
        margin-right: 5px;
        height: 100%;
        cursor: pointer;
      }
      .active {
        background-color: @greenColor;
        border-color: @greenColor;
        color: #ffffff;
      }
    }
    .scrolls {
      width: 71%;
      overflow-x: scroll;
      overflow-y: hidden;
      height: 40px;
      display: flex;
      padding-bottom: 5px;
    }
  }
}
.wrap {
  height: 100%;
  width: 68%;
  background: #e5e5e5;
  //菜单和输入框
  .top {
    background-color: @bgColor;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 10px;
    .menu-wrap {
      width: 74%;
      display: flex;
      .menu {
        width: 100%;
        display: flex;
        color: #ffffff;
        height: 100%;
        .menu-item {
          height: 100%;
          width: 100%;
          // padding: 0 10px;
          line-height: 40px;
          font-size: 22px;
          cursor: pointer;
        }
        .active {
          background-color: @greenColor;
        }
      }
    }
    .serach {
      width: 23%;
      background: #ffffff;
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      .input {
        width: 100%;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #666666;
        width: 70%;
      }
      .search {
        color: #666666;
        font-size: 24px;
        padding-left: 10px;
        vertical-align: -2px;
      }
      .del {
        width: 40px;
        height: 100%;
        position: absolute;
        right: 0;
        background-color: #e6e6e6;
        text-align: center;
        line-height: 40px;
      }
    }
  }
  //餐桌
  .table {
    padding: 22px;
    padding-right: 0;
    background: #e5e5e5;
    height: 86.5%;
    width: 100%;
    position: relative;
    overflow-y: scroll;
    .box {
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      .food-item {
        width: 17%;
        min-width: 15%;
        margin-bottom: calc(12% / 4);
        position: relative;
        background-color: #ffffff;
        padding: 6px;
        cursor: pointer;
        .img {
          height: 112px;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name {
          font-size: 20px;
          margin: 5px 0;
          height: 54px;
          line-height: 1.3;
          overflow: hidden;
          text-overflow: ellipsis;
          padding: 0 5px;
        }
        .bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .price {
            color: #ff5151;
            font-size: 18px;
            padding: 0 5px;
            font-weight: bold;
          }
          .remain {
            .num {
              color: #ff5151;
            }
          }
        }
        .maskk {
          width: 100%;
          height: 58%;
          background-color: rgba(0, 0, 0, 0.7);
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 30px;
          visibility: hidden;
        }
      }
      .food-item:not(:nth-child(5n)) {
        margin-right: calc(13% / 4);
      }
      .active-foods {
        background-color: #d15e4b;
        color: #ffffff;
        .bottom {
          .price {
            color: #ffffff;
          }
          .remain {
            color: #ffffff;
            .num {
              color: #ffffff;
            }
          }
        }
      }
      .over {
        background-color: #333333;
        color: #ffffff;
        .bottom {
          .price {
            color: #ffffff;
          }
          .remain {
            display: none;
          }
        }
        .maskk {
          visibility: visible;
        }
      }
    }
    .checkout {
      font-size: 22px;
      color: #1a1a1a;
      display: flex;
      justify-content: center;
    }
  }
  .order-type {
    display: flex;
    background: #e5e5e5;
    align-items: center;
    font-size: 22px;
    color: #ffffff;
    padding-top: 10px;
    .order {
      // padding: 10px 25px;
      // background: @greenColor;
      margin-left: 20px;
      // cursor: pointer;
      font-size: 18px;
    }
    .order-zh {
      padding: 10px 50px;
    }
    .check {
      height: 42px;
      width: 170px;
      display: flex;
      cursor: pointer;
      background-color: #cccccc;
      .check-item {
        width: 70%;
        text-align: center;
        font-size: 18px;
        line-height: 42px;
        background-color: #666666;
      }
    }
    .active {
      flex-direction: row-reverse;
      .check-item {
        background-color: @greenColor;
      }
    }
  }
}
//数字键盘提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 600px;
    font-size: 30px;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content {
      text-align: center;
      padding: 50px 30px 50px 30px;
    }
    .content-remark {
      padding: 30px;
      padding-bottom: 0;
      .remark {
        padding-bottom: 30px;
        font-size: 26px;
      }
      .remark-list {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        font-size: 26px;
        .remark-item {
          width: 48%;
          background-color: #f5f5f5;
          padding: 15px 0;
          text-align: center;
          margin-bottom: 20px;
          overflow: hidden;
          cursor: pointer;
        }
      }
    }
    .input {
      border: 1px solid #e6e6e6;
      padding: 15px 20px 10px 20px;
      textarea {
        outline: none;
        color: #666666;
        width: 100%;
        resize: none;
        font-family: "Alp Ekran";
        border: none;
        font-size: 26px;
      }
    }
    .btn {
      margin: 20px 30px;
      background: @greenColor;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
  }
}
@media screen and (max-width: 1440px) {
  .food-list {
    .btns {
      .scroll {
        width: 65%;
      }
    }
  }
}
@media screen and (max-width: 1366px) {
  .wrap {
    height: 96.2%;
  }
  .food-list {
    .btns {
      .scroll {
        width: 58%;
      }
    }
  }
}

.icon {
  cursor: pointer;
  padding: 0px 10px;
}

.back-icon {
  font-size: 26px;
}

.combos-box-ug {
  direction: rtl;
}

.combos-box-zh {
  direction: ltr;
}

.combos-box {
  padding: 10px;
  .combos-item {
    margin-bottom: 20px;
  }
  .combos-title {
    font-size: 20px;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    .combos-title-count {
      padding: 0px 20px;
    }
  }
  .combos-check {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 20px;
    column-gap: 20px;
    row-gap: 20px;
    .check-item {
      height: 42px;
      padding: 0px 40px;
      font-size: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      cursor: pointer;
      position: relative;
      &.active {
        background-color: #139d59;
        color: #ffffff;
      }

      .check-food-count {
        position: absolute;
        left: 9px;
        top: 50%;
        font-size: 12px;
        transform: translateY(-50%);
        border-radius: 50%;
        background-color: #ff5151;
        width: 22px;
        height: 22px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .check-food-icon {
        position: absolute;
        right: 0px;
        top: 50%;
        font-size: 18px;
        transform: translateY(-50%);
        width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
