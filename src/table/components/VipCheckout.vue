<template>
  <CommonDialog :dialogShow.sync="show" width="960px" :title="gL('vipPayDialogTitle')">
    <div class="detail" :class="gLang == 1 ? 'ug-box' : 'zh-box'">
      <div class="pay-box">
        <div class="pay-input">￥{{ vipDetailData.vipPrice }}</div>
        <div class="pay-title">
          <span>{{ gL("realAmount") }}</span>
          <span>￥{{ vipDetailData.vipPrice }}</span>
        </div>
        <div class="pay-btn" @click="confrim">
          <el-image class="pay-icon" :src="payIcon" fit="contain"></el-image>
        </div>
      </div>
      <div class="line-box">
        <div class="line"></div>
      </div>
      <div class="info-box">
        <div class="info-title">{{ vipDetailData.level_name }}</div>
        <div class="info-line-box">
          <div class="info-line">
            <div>{{ vipDetailData.name }}</div>
            <div class="price-text">￥{{ vipDetailData.balance }}</div>
          </div>
          <div class="info-line">
            <div>{{ vipDetailData.mobile }}</div>
            <div>{{ gL("amout") }}</div>
          </div>
        </div>
        <div class="recharge-btn" @click="$emit('recharge')">
          {{ gL("recharge") }}
        </div>
      </div>
    </div>
  </CommonDialog>
</template>

<script>
import CommonDialog from "../../components/CommonDialog.vue";
import payIcon from "./../../assets/images/pay-icon.png";
export default {
  components: {
    CommonDialog
  },
  data() {
    return {
      show: false,
      gLang: 1,
      vipDetailData: {
        name: "",
        mobile: "",
        level_name: "",
        balance: 0
      },
      payIcon: payIcon
    };
  },
  methods: {
    confrim() {
      if (this.vipDetailData.balance < this.vipDetailData.vipPrice) {
        this.$message({
          message: this.gL("vipCheckoutTips"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      this.$emit("vipConfrim", this.vipDetailData);
      this.show = false;
    },
    // 显示弹出框
    showDialog(data) {
      this.gLang = localStorage.getItem("langId");
      this.vipDetailData = data;
      this.show = true;
    },
    updateDialogData(data) {
      this.vipDetailData = { ...this.vipDetailData, ...data };
    }
  }
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;

/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

.detail {
  display: flex;
  padding-bottom: 15px;
  &.ug-box {
    flex-direction: row-reverse;
    direction: rtl;
  }
  &.zh-box {
    flex-direction: row;
    direction: ltr;
  }
  .line-box {
    flex: 1;
    display: flex;
    justify-content: center;
  }
  .line {
    width: 2px;
    height: 100%;
    background-color: #ccc;
    padding: 0;
    border: none;
  }

  .pay-box {
    flex: 12;
  }

  .pay-input {
    width: 100%;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 26px;
    color: #333;
    background-color: #cccccc;
    border-radius: 4px;
  }

  .pay-title {
    padding: 20px 0px;
    text-align: start;
  }

  .pay-btn {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #07c160;
    border-radius: 4px;
    cursor: pointer;
    .pay-icon {
      width: 40px;
      height: 40px;
    }
  }

  .info-box {
    background-color: #2e3033;
    color: #fff;
    flex: 12;
    display: flex;
    flex-direction: column;
    .info-title {
      height: 50px;
      border-bottom: 1px solid #ccc;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .info-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .info-line-box {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      padding: 0px 10px;
    }
    .price-text {
      color: #07c160;
    }
    .recharge-btn {
      width: 100%;
      height: 50px;
      line-height: 40px;
      background-color: #333;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
}
</style>
