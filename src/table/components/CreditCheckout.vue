<template>
  <CommonDialog
    :dialogShow.sync="show"
    :title="gL('credit')"
    @close="closeDialog"
  >
    <div class="detail" :class="gLang == 1 ? 'ug-box' : 'zh-box'">
      <div class="pay-title">
        <span>{{ gL("realAmount") }} : </span>
        <span :style="{ direction: 'ltr', display: 'inline-block' }">{{
          formatNumber(orderPrice)
        }}</span>
      </div>
      <div class="phone-input-box">
        <el-autocomplete
          class="inline-input"
          v-model="phoneValue"
          type="number"
          :style="{ direction: 'ltr' }"
          :fetch-suggestions="phoneInputHandler"
          value-key="selectItem"
          :debounce="800"
          :placeholder="gL('pleasePhone')"
          :trigger-on-focus="false"
          @input="phoneInput"
          @select="phoneSelect"
        ></el-autocomplete>
      </div>
      <div class="credit-info" v-if="creditData.id">
        <div class="credit-line">
          <div class="line-item">
            <div class="line-key">{{ gL("phoneNumber") }} :</div>
            <div class="line-value line-number">{{ creditData.phone }}</div>
          </div>
          <div class="line-item">
            <div class="line-key">{{ gL("createdDate") }} :</div>
            <div class="line-value line-number">
              {{ formatDate(creditData.created_at, "YYYY-MM-DD") }}
            </div>
          </div>
        </div>
        <div class="credit-line">
          <div class="line-item">
            <div class="line-key">{{ gL("nameUg") }} :</div>
            <div class="line-value">{{ creditData.name_ug }}</div>
          </div>
          <div class="line-item">
            <div class="line-key">{{ gL("nameZh") }} :</div>
            <div class="line-value">{{ creditData.name_zh }}</div>
          </div>
        </div>
        <div class="credit-line">
          <div class="line-item">
            <div class="line-key">{{ gL("creditLimit") }} :</div>
            <div class="line-value line-number">
              {{ formatNumber(creditData.credit_limit) }}
            </div>
          </div>
          <div class="line-item">
            <div class="line-key">{{ gL("creditPrice") }} :</div>
            <div class="line-value line-number" :style="{ color: '#ff9c00' }">
              {{ formatNumber(creditData.balance) }}
            </div>
          </div>
        </div>
        <div class="credit-line">
          <div class="line-item">
            <div class="line-key">{{ gL("availableLimit") }} :</div>
            <div class="line-value line-number" :style="{ color: '#ff9c00' }">
              {{ formatNumber(creditData.credit_limit - creditData.balance) }}
            </div>
          </div>
        </div>
      </div>
      <div class="pay-btn" @click="confrim" v-if="creditData.id">
        <el-image class="pay-icon" :src="payIcon" fit="contain"></el-image>
      </div>
      <div class="no-content" v-else>{{ gL("noData") }}</div>
    </div>
  </CommonDialog>
</template>

<script>
import CommonDialog from "../../components/CommonDialog.vue";
import payIcon from "./../../assets/images/pay-icon.png";
import { getCreditAvailableAPI } from "./../../api/index.js";
import { formatDate, formatNumber } from "./../../utils/utils.js";
export default {
  components: {
    CommonDialog
  },
  data() {
    return {
      show: false,
      gLang: 1,
      phoneValue: "",
      creditData: {},
      payIcon: payIcon,
      orderPrice: 0,
      formatDate: formatDate,
      formatNumber: formatNumber
    };
  },
  methods: {
    // 获取手机号码列表
    phoneInputHandler(value, callback) {
      if (this.phoneValue.length >= 4) {
        getCreditAvailableAPI(this.phoneValue).then(response => {
          if (response.status >= 200 && response.status < 300) {
            const arr = [];
            response.data.data.forEach(item => {
              arr.push({
                selectItem: `${item.phone} - ${
                  this.gLang == 1 ? item.name_ug : item.name_zh
                }`,
                ...item
              });
            });
            callback(arr);
          }
        });
      } else {
        callback([]);
      }
    },

    // 选择手机号码
    phoneSelect(item) {
      this.creditData = item;
    },

    // 输入手机号码
    phoneInput() {
      if (this.phoneValue.length > 11) {
        this.phoneValue = this.phoneValue.slice(0, 11);
      }
    },

    // 关闭弹出框
    closeDialog() {
      this.show = false;
      this.phoneValue = "";
      this.creditData = {};
    },

    // 确认支付
    confrim() {
      if (
        this.creditData.credit_limit <
        this.orderPrice + this.creditData.balance
      ) {
        this.$message({
          message: this.gL("creditCheckoutTips"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      this.$emit("creditConfrim", {
        orderPrice: this.orderPrice,
        ...this.creditData
      });
      this.closeDialog();
    },

    // 显示弹出框
    showDialog(order_price) {
      this.gLang = localStorage.getItem("langId");
      this.orderPrice = order_price;
      this.show = true;
    }
  }
};
</script>

<style>
.phone-input-box .el-autocomplete {
  width: 100%;
}
.phone-input-box .el-autocomplete input {
  height: 50px;
}
</style>
<style lang="less" scoped>
.phone-input-box {
  margin-bottom: 15px;
}
.detail {
  display: flex;
  flex-direction: column;
  padding-bottom: 15px;
  &.ug-box {
    direction: rtl;
  }
  &.zh-box {
    direction: ltr;
  }
  .credit-info {
    height: 260px;
    padding: 20px 10px;
    display: flex;
    flex-direction: column;
    background-color: #f2f2f2;
    border-radius: 4px;
  }
  .credit-line {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 10px;
    box-sizing: border-box;
    font-size: 22px;
    &:nth-child(odd) {
      background-color: #fff;
    }
    &:nth-child(even) {
      background-color: #f5f5f5;
    }
    .line-item {
      width: 50%;
      display: flex;
      column-gap: 6px;
    }
    &.item-box {
      column-gap: 6px;
    }
  }

  .pay-btn {
    margin-top: 15px;
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #07c160;
    border-radius: 4px;
    cursor: pointer;
    .pay-icon {
      width: 40px;
      height: 40px;
    }
  }

  .pay-title {
    padding: 20px 10px;
  }

  .line-number {
    direction: ltr;
  }

  .no-content {
    width: 100%;
    height: 355px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
