<template>
  <el-dialog
    :title="gL('payText')"
    :visible="show"
    :center="true"
    @close="$emit('update:show', false)"
    width="620px"
  >
    <div class="pay-number-box">
      <div class="pay-input">
        <div class="input-text">
          <span>￥</span>
          <span :class="numberValue ? 'text-black' : 'text-grey'">{{
            numberValue ? numberValue : 0
          }}</span>
        </div>
        <div class="input-close" v-if="numberValue" @click="numberValue = ''">
          <i class="el-icon-circle-plus-outline"></i>
        </div>
      </div>
      <div class="total-price" :class="gLang == 1 ? 'ug-dialog' : 'zh-dialog'">
        <span>{{ gL("realAmount") }} : </span>
        <span class="price-num">￥{{ totalPrice }}</span>
      </div>
      <div class="number-box">
        <div class="number-left" @click="payConfirm">
          <el-image class="pay-icon" :src="payIcon" fit="contain"></el-image>
        </div>
        <div class="number-right">
          <div
            class="num-item"
            :class="(index + 1) % 4 == 0 ? 'fat-item' : ''"
            v-for="(num, index) in numArray"
            :key="index"
            @click="clickItem(num)"
          >
            <i v-if="num == 'icon'" class="iconfont icon-tuige"></i>
            <span v-else>{{ num }}</span>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import payIcon from "./../../assets/images/pay-icon.png";
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      numArray: [
        "7",
        "8",
        "9",
        "icon",
        "4",
        "5",
        "6",
        "20",
        "1",
        "2",
        "3",
        "50",
        "0",
        "00",
        ".",
        "100"
      ],
      payIcon: payIcon,
      numberValue: "",
      totalPrice: 0,
      isFirstClick: true
    };
  },
  methods: {
    showDialog(price) {
      this.numberValue = price.toString();
      this.totalPrice = price.toString();
      this.isFirstClick = true;
      this.$emit("update:show", true);
    },
    clickItem(num) {
      console.log("num -> ", num, "value  -> ", this.numberValue);
      if (num == "icon") {
        this.numberValue = this.numberValue.substr(
          0,
          this.numberValue.length - 1
        );
      } else {
        console.log("else ----");
        if (this.numberValue.length > 6) return;
        if (this.isFirstClick) {
          console.log("first click");
          if (
            (num == "." && this.numberValue.indexOf(".") != -1) ||
            (num == "." && this.numberValue == "")
          )
            return;
          if (num == ".") {
            this.numberValue = this.numberValue + num;
          } else {
            this.numberValue = num;
          }
        } else {
          if (num.length == 2 && this.numberValue.length >= 5) return;
          if (num.length == 3 && this.numberValue.length > 4) return;
          console.log(
            "------ num ------",
            this.numberValue,
            this.numberValue.indexOf(".")
          );
          if (
            (num == "." && this.numberValue.indexOf(".") != -1) ||
            (num == "." && this.numberValue == "")
          )
            return;
          this.numberValue = this.numberValue + num;
        }
      }
      this.isFirstClick = false;
    },
    payConfirm() {
      this.$emit("payConfirm", this.numberValue);
      this.$emit("update:show", false);
    }
  }
};
</script>

<style lang="less" scoped>
.pay-number-box {
  display: flex;
  flex-direction: column;
  &.ug-dialog {
    direction: rtl;
  }
  &.zh-dialog {
    direction: ltr;
  }
  .pay-input {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #2d7aff;
    border-radius: 4px;
    height: 60px;
    font-size: 24px;
    background-color: #eff1f6;
    .input-close {
      position: absolute;
      top: 50%;
      transform: translateY(-50%) rotateZ(45deg);
      right: 20px;
      cursor: pointer;
    }
  }
  .total-price {
    font-size: 24px;
    padding: 20px 0px;
  }
  .price-num {
    display: inline-block;
    direction: ltr;
  }
  .number-box {
    display: flex;
    justify-content: space-between;
    background-color: #eff1f6;
    border-radius: 4px;
    padding: 10px 10px 0px 10px;
    direction: rtl;
  }
  .number-right {
    width: 420px;
    display: flex;
    flex-direction: row-reverse;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .number-left {
    width: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
    background-color: #07c160;
    border-radius: 4px;
    cursor: pointer;
    .pay-icon {
      width: 40px;
      height: 40px;
    }
  }
  .num-item {
    width: 90px;
    height: 60px;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    margin-bottom: 10px;
    border-radius: 4px;
    cursor: pointer;
    &.fat-item {
      width: 120px;
      .icon-tuige {
        font-size: 24px;
      }
    }
  }

  .input-text {
    direction: ltr;
  }

  .text-black {
    color: #333;
  }

  .text-grey {
    color: #ccc;
  }
}
</style>
