import Vue from 'vue'
import App from './App.vue'

import VueRouter from 'vue-router'
import routerConfig from './router.config.js'
import { validatePositiveInteger, validateCurrency } from './directive/index.js'
// import PAZU from '../static/js/pazuclient.js'
// Vue.use(PAZU);
import '../static/css/reset.css' /*引入公共样式*/
Vue.use(VueRouter);
const router = new VueRouter(routerConfig);

// 指令
validatePositiveInteger(Vue);
validateCurrency(Vue);

Vue.prototype.gLang = 1; // 1：维吾尔语 2：汉语
var langId = localStorage.getItem("langId");
//第一次进来的时候默认语言设置成1，否则从localStorage 获取用户最终修改语言
if (langId == null || langId == '' || langId == 'undefined' || typeof langId == 'undefined') {
  localStorage.setItem("langId", Vue.prototype.gLang);
} else {
  localStorage.setItem("langId", langId);
  Vue.prototype.gLang = langId;
}

/*获取语言包*/
var language = require("./language.js");
Vue.prototype.gL = function (key) {
  return language.lang(localStorage.getItem("langId"), key)
}

import VueI18n from 'vue-i18n'
import Element from 'element-ui'
// import 'element-ui/lib/theme-chalk/index.css'
import '../theme/index.css'
import ugLocale from 'element-ui/lib/locale/lang/ug-CN'
import zhLocale from 'element-ui/lib/locale/lang/zh-CN'
import elementLocal from 'element-ui/lib/locale'

Vue.use(VueI18n)
const messages = {
  "ug": {
    ...ugLocale
  },
  "zh-cn": {
    ...zhLocale
  }
}
const i18n = new VueI18n({
  locale: localStorage.getItem("langId") == 1 ? 'ug' : 'zh-cn',
  messages,
})
elementLocal.i18n((key, value) => i18n.t(key, value))

Vue.use(Element)
// if(localStorage.getItem('langId') == 1){
//   Vue.config.lang = 'ug'
//   require('./assets/css/elementUg.css')
// }else{
//   Vue.config.lang = 'zh-cn'
//   require('./assets/css/elementZh.css')
// }
// Vue.locale('ug', ugLocale)
// Vue.locale('zh-cn', zhLocale)
// socket
import 'babel-polyfill'
import promise from 'es6-promise';
promise.polyfill();
/*封装axios ajax方法*/
import axios from 'axios'

import VueAwesomeSwiper from 'vue-awesome-swiper'
// require styles
import 'swiper/dist/css/swiper.css'
// Vue.use(VueAwesomeSwiper, /* { default global options } */);
Vue.use(VueAwesomeSwiper);
axios.defaults.timeout = 100000;

const baseUrl = localStorage.getItem("ip_adr");
const ipv4Regex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
if (ipv4Regex.test(baseUrl)) {
  axios.defaults.baseURL = 'http://' + baseUrl;
} else {
  axios.defaults.baseURL = 'https://' + baseUrl;
}

// if (localStorage.getItem("ip_adr")==localStorage.getItem("settings_id")) {
  // axios.defaults.baseURL = 'https://' + localStorage.getItem("ip_adr") + '/api/v1/';
  //axios.defaults.baseURL = 'https://ros-api.amas.biz/api/v1/';
  // axios.defaults.baseURL = 'http://************:81/api/v1/';
// } else {
//   axios.defaults.baseURL = 'http://' + localStorage.getItem("ip_adr") + ':8001/api/v1/';
// }

var lang = 'ug-CN';
if (localStorage.getItem("langId") == 1) {
  lang = 'ug-CN';
  Vue.config.lang = 'ug'
} else {
  lang = 'zh-CN';
  Vue.config.lang = 'zh-cn'
}
// import VueSocketIO from 'vue-socket.io'
// if(localStorage.getItem("ip_adr")){
//   Vue.use(new VueSocketIO({
//     debug: true,
//     // 服务器端地址
//     // connection: 'ws://**************:6001',
//     connection: 'ws://**************:6001',
//   }))
// }

export function switchLang(){
  lang = lang === 'ug-CN' ? 'zh-CN' : 'ug-CN';
}

Vue.prototype.localeName = function (item, key = 'name'){
  console.log(`store.state.lang`, store.state.lang);
  return store.state.lang  == 1 ? item[key+ '_ug'] : item[key + '_zh'];
}

/**
 * 封装get方法
 * @param url
 * @param data
 * @returns {Promise}
 */
export function fetch(url, params) {
  params = params || {}
  return new Promise(function (resolve, reject) {
    axios.get(url, {
        headers: {
          'Accept-Language': lang,
          'Authorization': 'Bearer ' + localStorage.getItem("token"),
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'MerchantNo': getCookie('merchant_no')
        },
        params: params
      })
      .then(function (response) {
        resolve(response);
      })
      .catch(function (err) {
        reject(err)
      })
  })
}

/**
 * 封装patch请求
 * @param url
 * @param data
 * @returns {Promise}
 */

export function patch(url, data = {},language = '') {
  return new Promise((resolve, reject) => {
    axios.patch(url, data, {
        headers: {
          'Accept-Language': language === '' ? lang : language,
          'Authorization': 'Bearer ' + localStorage.getItem("token"),
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'MerchantNo': getCookie('merchant_no')
        }
      })
      .then(response => {
        resolve(response);
      }, err => {
        reject(err)
      })
  })
}

/**
 * 封装post请求
 * @param url
 * @param data
 * @returns {Promise}
 */

export function post(url, data = {}) {
  return new Promise((resolve, reject) => {
    axios.post(url, data, {
        headers: {
          'Accept-Language': lang,
          'Authorization': 'Bearer ' + localStorage.getItem("token"),
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'MerchantNo': getCookie('merchant_no')
        }
      })
      .then(response => {
        resolve(response);
      }, err => {
        reject(err)
      })
  })
}

/**
 * 封装put请求
 * @param url
 * @param data
 * @returns {Promise}
 */

export function put(url, data = {}) {
  return new Promise((resolve, reject) => {
    axios.put(url, data, {
        headers: {
          'Accept-Language': lang,
          'Authorization': 'Bearer ' + localStorage.getItem("token"),
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'MerchantNo': getCookie('merchant_no')
        }
      })
      .then(response => {
        resolve(response);
      }, err => {
        reject(err)
      })
  })
}


/**
 * 封装delete方法
 * @param url
 * @param data
 * @returns {Promise}
 */
export function remove(url, params = {}) {
  return new Promise((resolve, reject) => {
    axios.delete(url, {
        headers: {
          'Accept-Language': lang,
          'Authorization': 'Bearer ' + localStorage.getItem("token"),
          'Content-Type': 'application/x-www-form-urlencoded',
          'MerchantNo': getCookie('merchant_no')
        },
        params: params
      })
      .then(response => {
        resolve(response);
      })
      .catch(err => {
        reject(err)
      })
  })
}

/**
 * 更改ip
 * @param url
 * @param data
 * @returns {Promise}
 */
// export function changeIp(url, params = {}) {
//   if (localStorage.getItem("ip_adr")) {
//     axios.defaults.baseURL = 'https://' + localStorage.getItem("ip_adr") + '/api/v1/';
//     //axios.defaults.baseURL = 'https://ros-api.almas.biz/api/v1/';
//
//   }
// }


export function getBaseStaticURL() {
  // return 'https://' + localStorage.getItem("ip_adr") + '/api/v1/';
  //return 'https://ros-api.almas.biz/api/v1/';
}
//模态框CLASS
export function toastClass() {
  if (localStorage.getItem("langId") == 1) {
    return "uy-toast"
  } else {
    return "zh-toast"
  }
}
// 验证IP
export function isValidIP(ip) {
  var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
  return reg.test(ip);
}
// 验证域名
export function isValidDomain(domain) {
  // 首先，检查输入是否为字符串以及是否为空
  if (typeof domain !== 'string' || domain.trim() === '') {
    return false;
  }

  // 使用正则表达式常量来避免每次调用时重新编译
  const reg = /^(?!:\/\/)(?:[a-zA-Z0-9-]{1,63}\.){1,}[a-zA-Z]{2,}(?:\.[a-zA-Z]{2,})?$/;

  // 使用更新后的正则表达式进行测试
  return reg.test(domain);
}

//将数字转换成金额显示
export function toMoney(val) {
  val = Number(val);
  if (val != 0) {
    val = val.toString().replace(/\$|\,/g, '');
    if (isNaN(val)) {
      val = "0";
    }
    let sign = (val == (val = Math.abs(val)));
    val = Math.floor(val * 100 + 0.50000000001);
    let cents = val % 100;
    val = Math.floor(val / 100).toString();
    if (cents < 10) {
      cents = "0" + cents
    }
    for (var i = 0; i < Math.floor((val.length - (1 + i)) / 3); i++) {
      val = val.substring(0, val.length - (4 * i + 3)) + ',' + val.substring(val.length - (4 * i + 3));
    }
    return (((sign) ? '' : '-') + val + '.' + cents);
    // return num;//返回的是字符串23,245.12保留2位小数
  } else {
    return val;
  }
}
//手机号加空格
// export function toPhone(val){
//   var phoneNum = val.trim();
//   //如果是删除按键，则什么都不做
//   if (e.keyCode === 8) {
//     this.value = phoneNum;
//     return;
//   }

//   var len = phoneNum.length;
//   if (len === 3 || len === 8) {
//     phoneNum += ' ';
//     this.value = phoneNum;
//   }

// }

export function in_array(search, array) {
  for (var i in array) {
    if (array[i] == search) {
      return true;
    }
  }
  return false;
}

Array.prototype.indexOf = function (val) {
  for (var i = 0; i < this.length; i++) {
    if (this[i] == val) return i;
  }
  return -1;
};

Array.prototype.remove = function(val) {
  var index = this.indexOf(val);
  if (index > -1) {
  this.splice(index, 1);
  }
};

Vue.prototype.$switchLang = switchLang;
Vue.prototype.$fetch = fetch;
Vue.prototype.$post = post;
Vue.prototype.$remove = remove;
Vue.prototype.$patch = patch;
Vue.prototype.$put = put;
Vue.prototype.$axios = axios;
// Vue.prototype.$changeIp = changeIp;
Vue.prototype.$getBaseStaticURL = getBaseStaticURL;
Vue.prototype.$toastClass = toastClass;
Vue.prototype.$isValidIP = isValidIP;
Vue.prototype.$isValidDomain = isValidDomain;
Vue.prototype.$toMoney = toMoney;
// Vue.prototype.$socket = socket;
Vue.prototype.$in_array = in_array;

import store from './store'
new Vue({
  el: '#app',
  render: h => h(App),
  router,
  store,
  i18n,
  beforeCreate() {
    Vue.prototype.$bus = this
  }
})

