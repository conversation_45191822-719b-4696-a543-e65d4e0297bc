<template>
    <div class="wraps food">
        <div class="top topp">
          <div class="menu" :class="gLang==2?'menu-zh':''">
          <el-tabs v-model="activeName"
                type="card"
                class="menu-item"
                @tab-click='clickMenu'>
            <el-tab-pane v-for="item in tables"
              :key="item.id"
              :label="(item.tables_count == undefined || Number.isNaN(item.tables_count)) ? item.name : item.name+' ('+item.tables_count+')'"
              :name="item.item"
              :id="item.id">
            </el-tab-pane>
          </el-tabs>
          </div>
           <div class="serach">
            <div class="input">
                <span class="iconfont icon-search search"></span>
                <input type="text"  oninput="if(value.length>20)value=value.slice(0,20)" :placeholder="gL('tableName')" v-model="serachText" v-on:input="inputSearch" @keyup.enter="inputSearch" >
            </div>
            <div class="del" v-if="delete_text" @click="removeSerach">
                <span class='iconfont icon-jia-copy'></span>
            </div>
          </div>
        </div>
        <div class="table">
        <el-table
        :data="tableData"
        header-cell-class-name="headers"
        :header-cell-style="{background:'#e6e6e6'}"
        :cell-style="cell"
        row-class-name="row"
        v-loading="tableLoading"
        style="width: 100%">
        <el-table-column
          :label="gL('serialNumber')"
          type="index"
          align="center"
          width="60">
        </el-table-column>
        <el-table-column
          prop="no"
          align="center"
          :label="gL('tableNumbers')">
        </el-table-column>
        <el-table-column
          prop="name"
          align="center"
          :label="gL('tableName')">
        </el-table-column>
        <el-table-column
          prop="area_name"
          align="center"
          :label="gL('area')">
        </el-table-column>
        <el-table-column
          prop="seating_capacity"
          align="center"
          :label="gL('seatNumber')">
        </el-table-column>
        <el-table-column
          prop="sort"
          align="center"
           width="80px"
          :label="gL('sort')">
        </el-table-column>
        <el-table-column
          align="center"
          :label="gL('state')">
          <template slot-scope="scope">
            <el-switch
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.state"
              @change="changeSwitch(scope.$index, scope.row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column :label="gL('operation')" prop="dosome" align="center" width="120px">
          <template slot-scope="scope">
            <el-button type="text"  icon="iconfont icon-shiliangzhinengduixiang" @click="editData(scope.row)" circle></el-button>
             <span style="padding-right:9px;padding-left:15px">
              <span class='line'></span>
            </span>
            <el-button  type="text" class="danger" @click="delData(scope.row)" icon="iconfont icon-qingkong"  circle></el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>
      <div class="mask" v-if="modal_box">
        <div class="box">
          <div class="title">
            <span></span>
            <span>{{modal_title}}</span>
            <span class="iconfont icon-jia-copy" @click="cancel(2)"></span>
          </div>
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
            <div class="content" style="flex-direction:row">
              <div class="items">
                <div class="row">
                  <el-form-item prop="no">
                    <el-input v-model="ruleForm.no" ref="input"  :placeholder="gL('tableNumber')" type="number" oninput="if(value.length>6)value=value.slice(0,6)"></el-input>
                  </el-form-item>
                </div>
                <el-form-item prop="sort">
                    <el-input :placeholder="gL('sort')" v-model="ruleForm.sort" type="number" oninput="if(value.length>3)value=value.slice(0,3)">
                      <template slot="append">{{gL('sort')}}</template>
                    </el-input>
                </el-form-item>
                <div class="row">
                  <el-form-item prop="name_ug">
                    <el-input v-model="ruleForm.name_ug"   :placeholder="gL('tableName')+gL('ug')" class="input-ug" :class="gLang==1?'uy-input':'zh-input'" maxlength="20"></el-input>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="name_zh">
                    <el-input v-model="ruleForm.name_zh"  :placeholder="gL('tableName')+gL('zh')" :class="gLang==1?'uy-input':'zh-input'" maxlength="20"></el-input>
                  </el-form-item>
                </div>
                <div class="row">
                </div>
              </div>
               <div class="items" :class="gLang==1?'uy-select':''">
                <div class="row">
                  <el-form-item prop="area_id">
                    <el-select v-model="ruleForm.area_id" :placeholder="gL('area')">
                      <el-option
                        v-for="(item,index) in cate_list"
                        :key="index"
                        :label="item.name"
                        :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <el-form-item prop="seating_capacity">
                    <el-input :placeholder="gL('seatNumber')" v-model="ruleForm.seating_capacity" type="number" oninput="if(value.length>2)value=value.slice(0,2)" @keyup.enter.native="confirm(2)">
                      <template slot="append">{{gL('seatNumber')}}</template>
                    </el-input>
                </el-form-item>
                <div class="row type">
                    <div class="check" :class="ruleForm.state==0?'active':''" @click="ruleForm.state=0">{{gL('off')}}</div>
                    <div class="check" :class="ruleForm.state==1?'active':''" @click="ruleForm.state=1">{{gL('open')}}</div>
                </div>
              </div>
            </div>
          </el-form>
         <div class="adds">
            <div class="btn add-btn" @click="confirm(1)"  v-if="addBox">
                {{gL('continueAdd')}}
            </div>
            <div class="btn" @click="confirm(2)" :class="!addBox?'edit':''">
                {{gL('confirm')}}
            </div>
          </div>
        </div>
      </div>
       <modal :number_box="confirm_box"
                 :modal_content="modal_content"
                 :modal_title="modals_title"
                 @cancel="cancels" @confirm="confirmBox"></modal>
    </div>
</template>

<script>
import modal from '../components/modal.vue'
import { getTableListAPI, getAreasListAPI, getTableStateAPI, postTableAddAPI, putTableUpdateAPI, deleteTableDeleteAPI } from './../api/index.js'
var self;
export default {
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.activeName='first';
    self.activeId='';
    self.cate_list=[];
    self.ruleForm.area_id = '';
    self.serachText='',//搜索内容
    self.delete_text='',//搜索框删除按钮
    self.getData();
    self.getList();
    self.url = self.$getBaseStaticURL()+'upload/image';
  },
  data() {
    return {
      gLang:1,
      tableData:[],
      serachText:'',//搜索内容
      delete_text:'',//搜索框删除按钮
      activeName:'',
      activeId:'',
      modal_box:false,
      tables:[],
      cate_list:[],
      modal_title:this.gL('addTable'),
      ruleForm:{
        state:1,
        area_id:'',
        no:'',
        name_zh:'',
        name_ug:'',
        seating_capacity:'',
        sort:'',
      },
      url:"",
      rules: {
          sort: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' },
          ],
          no: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' },
          ],
          area_id: [
            { required: true, message: this.gL('plaeseChooise'), trigger: 'blur' }
          ],
          name_zh: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          name_ug: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          cost_price: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          vip_price: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          seating_capacity: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          price: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          image: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
        },
      addBox:true,
      activeName:'first',
      modal_content:'',
      modals_title:'',
      confirm_box:false,
      tableLoading: false
    };
  },
  methods: {
      //添加
    addFood(){
        self.modal_box=true;
        self.addBox=true;
        if(self.tableData.length>0){
            self.ruleForm.sort = Math.max.apply(Math, self.tableData.map(function(o) {return o.sort}))+1;
          }
        setTimeout(() => {
          self.$refs['input'].focus();
        }, 100);
    },
    //搜索输入框输入时候
    inputSearch(){
      if(self.serachText.length!=0){
        self.delete_text = true;
      }else{
        self.delete_text = false;
      }
      self.getData();
    },
    //删除搜索内容
    removeSerach(){
      self.serachText = '';
      self.delete_text = false;
      self.getData();
    },
    //点击点菜
    clickMenu(e){
      self.activeId = e.$attrs.id;
      if(e.$attrs.id!=0){
        self.ruleForm.area_id = e.$attrs.id;
      }
      self.getData();
    },
    //获取数据
    getData(){
      var data = {}
      if(self.activeId!=''){
        data.area_id = self.activeId;
      }
      if(self.serachText!=''){
        data.keyword = self.serachText;
      }
      this.tableLoading = true;
      getTableListAPI(data).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.tableData = response.data.data;
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    //获取列表
    getList(){
      getAreasListAPI().then((response) => {
        if(response.status >= 200 && response.status < 300){
          var count = 0
          response.data.data.forEach(item => {
            count += item.tables_count;
          });
          var all = {name:self.gL('all'),item:'first',id:0,tables_count:count};
          response.data.data.unshift(all);
          self.tables = response.data.data;
          response.data.data.forEach(item => {
            if(item.id!=0){
              self.cate_list.push(item);
            }
          });
        }
      });
    },
     //开关
    changeSwitch(index,row){
      getTableStateAPI(row.id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.$message({
            message:self.gL('successfulOperation'),
            type: "success",
            customClass:self.$toastClass(),
            offset: 120
          });
        }
      }).catch(err => {
        this.getData();
      });
    },
    //编辑
    editData(row){
      self.id = row.id;
      self.addBox = false;
      self.modal_box = true;
      self.ruleForm.food_category_id = row.food_category_id;
      self.ruleForm.no = row.no;
      self.ruleForm.seating_capacity = row.seating_capacity;
      self.ruleForm.state = row.state;
      self.ruleForm.name_zh = row.name_zh;
      self.ruleForm.name_ug = row.name_ug;
      self.ruleForm.sort = row.sort;
      self.ruleForm.area_id = row.area_id;
      self.modal_title=self.gL('editTable');
    },
    cancel(e){
      if(e==2){
        self.modal_box = false;
      }else{
        self.ruleForm.sort = self.ruleForm.sort+1;
        self.$refs['input'].focus();
      }
      self.ruleForm.state = 1;
      self.ruleForm.food_category_id = '';
      self.ruleForm.no = '';
      self.ruleForm.seating_capacity = '';
      self.ruleForm.state = 1;
      self.ruleForm.name_zh = '';
      self.ruleForm.name_ug = '';
      // self.ruleForm.area_id = '';
      self.modal_title=self.gL('addTable');
    },
    confirm(e){
        if(self.addBox){
          self.$refs.ruleForm.validate((valid) => {
            if (valid) {
              postTableAddAPI(self.ruleForm).then((response) => {
                if(response.status >= 200 && response.status < 300){
                  self.$message({
                    message:self.gL('successfulOperation'),
                    type: "success",
                    customClass:self.$toastClass(),
                    offset: 120
                  });
                  self.getData();
                  self.getList();
                  self.cancel(e);
                }
              });
          } else {
            return false;
          }
        });
       }else{
        self.$refs.ruleForm.validate((valid) => {
        if (valid) {
          putTableUpdateAPI(self.id, self.ruleForm).then((response) => {
            if(response.status >= 200 && response.status < 300){
              self.$message({
                message:self.gL('successfulOperation'),
                type: "success",
                customClass:self.$toastClass(),
                offset: 120
              });
              self.getData();
              self.cancel(e);
            }
          });
        }else {
            return false;
          }
        });
      }
    },
    //删除
    delData(row){
      self.id = row.id;
      self.confirm_box = true;
      if(self.gLang==1){
        self.modal_content="《"+row.name+"》"+self.gL('confirs')+self.gL('confirmdelete');
      }else{
        self.modal_content=self.gL('confirmdelete')+"《"+row.name+"》"+self.gL('confirs');
      }
      self.modals_title=self.gL('tips');
    },
    cancels(){
      self.confirm_box = false;
    },
    confirmBox(){
      self.cancels();
      self.delRowData();
    },
    delRowData(){
      deleteTableDeleteAPI(self.id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.$message({
            message:self.gL('successfulOperation'),
            type: "success",
            customClass:self.$toastClass(),
            offset: 120
          });
          self.getData();
        }
      });
    },
    cell({row, column, rowIndex, columnIndex}) {
      if(columnIndex == 2){
        if(self.gLang==1){
          return 'direction: rtl'
        }
      }
      if(columnIndex == 3){
        if(self.gLang==1){
          return 'direction: rtl'
        }
      }
    },
  },
    components:{
    modal
  }
};
</script>

<style lang="less" scoped>
.wraps{
    width: 100%;
    height: 100%;
   .top{
      height: 50px;
      width: 100%;
      background-color: #2e3033;
      display: flex;
      justify-content: space-between;
     .menu{
        width: 82%;
        display: flex;
        color:#ffffff;
        height: 100%;
        // overflow-x: scroll;
       .menu-item{
          width: 100%;
          height: 100%;
          // padding: 0 25px;
          // line-height: 40px;
          font-size: 26px;
          cursor: pointer;
       }
       .active{
         background-color: #139d59;;
       }
     }
    .serach{
       width: 280px;
       background: #4d4d4d;
       margin:5px 15px 5px 0;
       display: flex;
       align-items: center;
       justify-content: space-between;
       .input{
        //  width: 70%;
        display: flex;
        align-items: center;
       }
       input{
         outline: none;
         font-size: 22px;
         color:#ffffff;
         width: 70%;
         background: #4d4d4d;
         &::placeholder{
             color:#cccccc;
         }
       }
       .search{
         color:#cccccc;
         font-size: 24px;
         padding: 0 10px;
       }
       .del{
         width: 20%;
         height: 100%;
        color: #cccccc;
         justify-content: center;
         display: flex;
         align-items: center;
         cursor: pointer;
         .iconfont{
             font-size: 22px;
         }
       }
     }
   }
    .table{
      height: 93%;
      overflow-y: scroll;
    }
      //s提示框
   .mask{
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      background-color: rgba(0,0,0,.5);
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 1366px;
      min-height: 768px;
      .box{
        background-color: #ffffff;
        width: 920px;
        font-size: 30px;
        .title{
          background-color: #e6e6e6;
          color:#1a1a1a;
          padding: 25px 20px;
          text-align: center;
          position: relative;
          .iconfont{
            position: absolute;
            right: 20px;
            font-size: 23px;
            color:#666666;
            cursor: pointer;
          }
        }
        .content{
          padding: 50px 75px 0 75px;
          display: flex;
          justify-content: space-between;
          .items{
            width: 48%;
            .row{
              margin-bottom: 20px;
            }
            .type{
              display: flex;
              justify-content: space-between;
                .check{
                  width: 48%;
                  border:1px solid #cccccc;
                  color:#666666;
                  font-size: 18px;
                  text-align: center;
                  cursor: pointer;
                  height: 40px;
                  line-height: 40px;
                  margin-bottom: 40px;
                  border-radius: 3px;
                }
                .active{
                  background-color: #139d59;
                  color:#fff;
                }
              }
          }
        }
        .image{
          flex-wrap: wrap;
          justify-content: left;
          .img-item{
            width: 170px;
            height: 120px;
            margin-right: 22px;
            margin-bottom: 20px;
            overflow: hidden;
            cursor: pointer;
            border: 1px solid #3333;
            img{
              width: 100%;
              height: 100%;
            }
          }
        }
        .adds{
          display: flex;
          margin: 20px 75px 20px 75px;
          justify-content: space-between;
          .add-btn{
            background-color: #ff9c00;
          }
          .btn{
            width: 48%;
            margin: 0;
          }
          .edit{
            width: 100%;
          }
        }
        .btn{
          background: #139d59;
          color:#ffffff;
          font-size: 26px;
          text-align: center;
          padding: 15px 0;
          cursor: pointer;
          margin: 20px 75px 20px 75px;
        }
      }
   }
  }
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 170px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}
.avatar {
  width: 170px !important;
  height: 120px !important;
  display: block;
}
</style>
