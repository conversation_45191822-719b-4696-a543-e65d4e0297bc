<template>
  <div class="wrap">
    <div class="menus">
      <div class="box">
        <div
          class="menus-item"
          v-for="(item, index) in menus_list"
          :class="activeIndex == index ? 'active' : ''"
          @click="clickMenu(index)"
          :key="index"
        >
          {{ item }}
        </div>
      </div>
      <div style="flex-grow: 1" />
      <div
        class="add poster"
        @click="generateQRCode"
        v-if="generateQRCodeButtonVisibility"
        :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
      >
        <span>{{ gL("generateQRCode") }}</span>
        <i class="el-icon-picture"></i>
      </div>

      <div
        class="add"
        @click="add"
        :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
      >
        <span>{{ addText }}</span>
        <span class="iconfont icon-jia-copy-copy"></span>
      </div>
    </div>
    <!-- 餐桌 -->
    <div class="table">
      <keep-alive>
        <food ref="food" v-if="activeIndex == 0"></food>
        <cate ref="cate" v-if="activeIndex == 1"></cate>
      </keep-alive>
    </div>

    <!-- 生成餐桌二维码 -->
    <div class="poster-modal-box">
      <el-dialog
        :title="gL('posterModalTitle')"
        :visible.sync="showPosterModal"
        width="1000px"
        center
      >
        <p class="modal-title">{{ gL("posterModalTitle2") }}</p>
        <div class="image-box">
          <div
            class="box-item"
            v-for="item in imageList"
            :key="item.id"
            @click="clickImageItem(item.id)"
          >
            <img :src="item.url" alt="" />
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import food from "../tableManage/table.vue";
import cate from "../tableManage/cate.vue";
import QRCode from "qrcode";
var self;
export default {
  components: {
    food,
    cate
  },
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.activeIndex = 0;
    self.addText = this.gL("addTable");
    this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
  },
  data() {
    return {
      gLang: 1,
      menus_list: [this.gL("table"), this.gL("area")],
      activeIndex: 0,
      addText: this.gL("addTable"),
      generateQRCodeButtonVisibility: window.electronAPI && window.electronAPI.isElectron,
      showPosterModal: false,
      imageList: [
        {
          id: 1,
          url: require("../../static/images/QRCode/original/original_1.jpeg")
        },
        {
          id: 2,
          url: require("../../static/images/QRCode/original/original_2.jpeg")
        },
        {
          id: 3,
          url: require("../../static/images/QRCode/original/original_3.jpeg")
        }
      ],
      canvasDom: null,
      storeInfo: null
    };
  },
  methods: {
    //删除搜索内容
    clickMenu(e) {
      self.activeIndex = e;
      if (e == 0) {
        self.addText = self.gL("addTable");
      } else {
        self.addText = self.gL("addArea");
      }
    },
    //添加
    add() {
      if (self.activeIndex == 0) {
        self.$refs.food.addFood();
      } else {
        self.$refs.cate.addCate();
      }
    },
    generateQRCode() {
      this.showPosterModal = true;
    },

    // 点击生成二维码
    async clickImageItem(id) {
      const loading = this.$loading({
        text: this.gL("generatePoster")
      });
      const desks = this.$refs.food.tableData;
      const posters = [];
      for (let i = 0; i < desks.length; i++) {
        const baseUrl = await this.createImage(id, desks[i]);
        if (baseUrl.length < 20) {
          i--;
          continue;
        }
        posters.push({
          no: desks[i].no,
          image: baseUrl
        });
      }
      const result = window.electronAPI && await window.electronAPI.saveImage(posters);
      if(result == 0) {
        this.$message({
          message: this.gL("generatePosterSuccess"),
          type: "success",
          customClass: this.$toastClass(),
          offset: 120
        });
      } else if (result == 999) {

      } else {
        this.$message({
          message: this.gL("generatePosterFail").replace("%s", result),
          type: "success",
          customClass: this.$toastClass(),
          offset: 120
        });
      }
      loading.close();
    },

    // 格式化文本
    formatText(str, ctx) {
      let strArr = str.split("");
      let temp = "";
      for (let i = 0; i < strArr.length; i++) {
        if (ctx.measureText(temp).width < 700) {
          temp += strArr[i];
        } else {
          return { temp, isFinite: true };
        }
      }
      return { temp, isFinite: false };
    },

    // 用canvas画图片
    createImage(id, item) {
      return new Promise(resolve => {
        this.canvasDom = document.createElement("canvas");
        const ctx = this.canvasDom.getContext("2d");

        const img = new Image();
        img.src = require(`../../static/images/QRCode/${id}.jpeg`);

        this.canvasDom.width = img.width * 2;
        this.canvasDom.height = img.height * 2;

        img.onload = () => {
          ctx.drawImage(img, 0, 0, img.width * 2, img.height * 2);

          ctx.textBaseline = "top";
          ctx.fillStyle = "#fff";
          ctx.textAlign = "center";

          let qrcodeSize = 524;

          switch (id) {
            case 1: {
              ctx.font = "160px 'Alp Ekran'";
              ctx.fillText(item.no, 474, 380);
              break;
            }
            case 2: {
              ctx.font = "60px 'Alp Ekran'";
              ctx.direction = "rtl";
              ctx.textAlign = "left";
              ctx.fillText(`${item.no}-ئۈستەل`, 508, 296);
              ctx.direction = "inherit";
              ctx.textAlign = "right";
              ctx.fillText(`${item.no}-餐厅`, 440, 296);

              ctx.textAlign = "center";
              ctx.fillStyle = "#209f5a";
              const storeNameUg = this.formatText(
                this.storeInfo.merchant_name_ug,
                ctx
              );
              if (storeNameUg.isFinite) {
                ctx.fillText("..." + storeNameUg.temp, 474, 60);
              } else {
                ctx.fillText(storeNameUg.temp, 474, 60);
              }

              ctx.font = "80px '黑体'";
              const storeNameZh = this.formatText(
                this.storeInfo.merchant_name_zh,
                ctx
              );
              if (storeNameZh.isFinite) {
                ctx.fillText(storeNameZh.temp + "...", 474, 150);
              } else {
                ctx.fillText(storeNameZh.temp, 474, 150);
              }
              qrcodeSize = 420;
              break;
            }
            case 3: {
              ctx.textBaseline = "bottom";
              ctx.font = "140px 'Alp Ekran'";
              ctx.fillText(item.no, 474, 480);
              ctx.font = "60px 'Alp Ekran'";
              ctx.fillText("ئۈستەل", 260, 470);
              ctx.fillText("桌号", 660, 470);

              ctx.textBaseline = "top";
              ctx.fillStyle = "#209f5a";
              const storeNameUg = this.formatText(
                this.storeInfo.merchant_name_ug,
                ctx
              );
              if (storeNameUg.isFinite) {
                ctx.fillText("..." + storeNameUg.temp, 474, 60);
              } else {
                ctx.fillText(storeNameUg.temp, 474, 60);
              }

              ctx.font = "80px '黑体'";
              const storeNameZh = this.formatText(
                this.storeInfo.merchant_name_zh,
                ctx
              );
              if (storeNameZh.isFinite) {
                ctx.fillText(storeNameZh.temp + "...", 474, 150);
              } else {
                ctx.fillText(storeNameZh.temp, 474, 150);
              }
              qrcodeSize = 420;
              break;
            }
          }
          let baseUrl = localStorage.getItem("ip_adr");
          if (baseUrl.indexOf("d.almas.biz") > -1) {
            baseUrl = "ros-scan-api.d.almas.biz"
          } else {
            baseUrl = "ros-scan-api.mulazim.com"
          }
          QRCode.toDataURL(
            `https://${baseUrl}/mini/${this.storeInfo.merchant_no}|${item.id}`,
            {
              errorCorrectionLevel: "L",
              width: qrcodeSize,
              height: qrcodeSize,
              margin: 1
            },
            (error, qrcode) => {
              const img2 = new Image();
              img2.src = qrcode;
              img2.onload = () => {
                switch (id) {
                  case 1:
                    ctx.drawImage(img2, 210, 604, 524, 524);
                    break;
                  case 2:
                    ctx.drawImage(img2, 262, 486, 420, 420);
                    break;
                  case 3:
                    ctx.drawImage(img2, 262, 518, 420, 420);
                    break;
                }

                const baseUrl = this.canvasDom.toDataURL("image/jpeg");
                resolve(baseUrl);
              };
            }
          );
        };
      });
    }
  }
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.empty {
  margin: 0 auto;
}
#tMask {
  display: none;
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;
}
.wrap {
  height: 100%;
  width: 100%;
  //菜单和输入框
  .menus {
    display: flex;
    background-color: @bgColor;
    padding: 5px;
    font-size: 24px;
    color: #ffffff;
    justify-content: flex-end;
    border-bottom: 1px solid @grayColor;
    .box {
      display: flex;
    }
    .menus-item {
      padding: 12px 40px;
      text-align: center;
      cursor: pointer;
    }
    .active {
      background-color: @greenColor;
    }
    .add {
      display: flex;
      align-items: center;
      padding: 0 20px;
      background: #ff9c00;
      width: 210px;
      justify-content: space-between;
      margin-right: 10px;
      cursor: pointer;
      .iconfont {
        font-size: 24px;
      }
    }
    .poster {
      width: 280px;
    }
  }
  .table {
    height: 93%;
    overflow: hidden;
  }
}

.modal-title {
  font-size: 24px;
  color: #333;
  text-align: center;
}
.image-box {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  column-gap: 25px;
  margin-top: 20px;
  .box-item {
    width: 300px;
    box-sizing: border-box;
    border: 6px solid #e1e1e1;
    border-radius: 4px;
    cursor: pointer;
    img {
      width: 100%;
    }
  }
}
</style>
