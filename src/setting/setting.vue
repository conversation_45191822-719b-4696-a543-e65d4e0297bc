<template>
	<div class="wrap">
    <div class="menus">
        <div class="menus-item"   v-for="(item,index) in menus_list" :class="activeIndex==index?'active':''" @click="clickMenu(index)" :key="index">{{item}}</div>
    </div>
    <!-- 餐桌 -->
    <div class="table">
        <staff v-if="activeIndex==0"></staff>
        <paymentMethod v-if="activeIndex==1"></paymentMethod>
        <causeNotes v-if="activeIndex==2"></causeNotes>
        <businessInfo v-if="activeIndex==3"></businessInfo>
        <shifts v-if="activeIndex==4"></shifts>
        <!-- <bindWithWechat v-if="activeIndex==5"></bindWithWechat> -->
        <service v-if="activeIndex==5"></service>
        <!-- <downloadApp v-if="activeIndex==7"></downloadApp> -->
        <!-- <merchantSettings v-if="activeIndex==8"></merchantSettings> -->
        <MessageSettings v-if="activeIndex==6"></MessageSettings>
    </div>
    <!-- 餐桌状态 -->
	</div>
</template>

<script>
import staff from '../settingComponents/staff.vue'
import paymentMethod from '../settingComponents/paymentMethod.vue'
import causeNotes from '../settingComponents/causeNotes.vue'
import businessInfo from '../settingComponents/businessInfo.vue'
import shifts from '../settingComponents/shifts.vue'
import bindWithWechat from '../settingComponents/bindWithWechat.vue'
import service from '../settingComponents/service.vue'
import downloadApp from '../settingComponents/downloadApp.vue'
import merchantSettings from '../settingComponents/merchantSettings.vue'
import MessageSettings from '../settingComponents/MessageSettings.vue'
var self;
export default {
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.activeIndex=0;
  },
  data() {
    return {
      gLang:1,
      menus_list:[
        this.gL('staffMnage'),
        this.gL('paymentMethod'),
        this.gL('causeNotes'),
        this.gL('businessInfo'),
        this.gL('shifts'),
        // this.gL('bindWithWechat'),
        this.gL('service'),
        // this.gL('downloadApp'),
        // this.gL('merchantSettings'),
        this.gL('messageSettings')
        ],
      activeIndex:0,
    };
  },
  methods: {
    //删除搜索内容
    clickMenu(e){
        self.activeIndex = e;
    },

  },
   components:{
    staff,
    paymentMethod,
    causeNotes,
    businessInfo,
    shifts,
    bindWithWechat,
    service,
    downloadApp,
    merchantSettings,
    MessageSettings
  }
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.empty{
  margin: 0 auto;
}
 .wrap{
    height: 100%;
    width: 100%;
   //菜单和输入框
   .menus{
       display: flex;
       background-color: @bgColor;
       flex-wrap: wrap;
       padding: 5px;
       font-size: 23px;
       color:#ffffff;
        border-bottom: 1px solid @grayColor;
        overflow-x: scroll;
        .menus-item{
            padding: 14px 20px;
            text-align: center;
            cursor: pointer;
            white-space: nowrap;
        }
        .active{
            background-color: @greenColor;
        }
   }
   .table{
     height: 93%;
     overflow: hidden;
     overflow-y: scroll;
   }
}
</style>
