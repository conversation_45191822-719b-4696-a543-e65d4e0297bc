<script>
import PrintText from "../utils/PrintTextClass";
import moment from "moment/moment";


export default {
  name: "FoodText",
  methods: {
    print(json) {
      let isZh = localStorage.getItem("langId") != 1;
      const print_80 = json.order.printers.printer_size === 0;
      console.log("----打印中----", json)
      let printText = new PrintText();
      printText.setPrinterSize(print_80 ? printText.model.MAXCHARINLINE_80 : printText.model.MAXCHARINLINE_56)
      printText.setLang(isZh ? 'zh' : 'ug')
      //标题
      let title = ''
      switch (json.action) {
        case "提交订单":
        case "加菜":
          title = isZh ? "下单" : "يېڭى تاماق"
          break;
        case "退菜":
        case "全单退菜":
          title = (print_80 ? "****" : "*") + (isZh? " 退菜 " : "تاماق قايتۇرۇش") + (print_80 ? "****" : "*")
          break;
        case "催单":
          title = (print_80 ? "****" : "*") + (isZh ? " 催单 " : "تاماق سۈيلەش") + (print_80 ? "****" : "*")
          break;
      }

      printText.setTextType(printText.model.TEXT_TYPE_TITLE)
        .setAlign(printText.model.ALIGN_CENTER)
        .text(title)

        //桌号
        .text(isZh ? json.order.table_name_zh : json.order.table_name_ug)
        //人数
        .setTextType(printText.model.TEXT_TYPE_BODY)
        .setAlign(isZh ? printText.model.ALIGN_LEFT : printText.model.ALIGN_RIGHT)
        .text(isZh ? "人数 : " : "ئادەم سانى : ", json.order.customers_count + "")
        //工作人员
        .text((
          (json.action === '全单退菜' || json.action === '退菜')
            ?
            (isZh ? "退菜员 :" : "قايتۇرغۇچى : ") : (isZh ? "点菜员 : " : "تىزىملىغۇچى : ")
        ), json.order.operator)
        //订单号
        .text(isZh ? "订单号 : " : "زاكاز نومۇرى : ", json.order.order_no)
        .text(isZh ? "时间 : " : "ۋاقتى : ", moment().format("YYYY-MM-DD HH:mm:ss"))
        .line()
        .setTextType(printText.model.TEXT_TYPE_BODY_BIG)
        .textAlign((isZh ? "美食" : "تاماق"), (isZh ? "数量" : "سانى"))
        .feed()
      if (json.action === '全单退菜' || json.action === '退菜') {
        json.order.order_details.forEach(item => {
          item.canceled_foods.forEach(canceled => {
            printText.text(item.food_name_ug)
            printText.textAlign(canceled.food_name_zh, parseFloat(canceled.count) + "")
              .textBody(isZh ? "备注 : " : "ئەسكەرتىش : " + canceled.remarks).line()
          })
        })
      } else {
        json.order.order_details.forEach(item => {
          printText.text(item.food_name_ug)
            .textAlign(item.food_name_zh, parseFloat(item.foods_count) + "")
            if (item.remarks) {
              printText.setTextType(printText.model.TEXT_TYPE_BODY_BIG)
              printText.textBody(isZh ? "备注 : " : "ئەسكەرتىش : " + item.remarks)
            }
            printText.line()
        })
      }
      if (json.order.remarks) {
        printText.setTextType(printText.model.TEXT_TYPE_BODY_BIG)
          .textBody(isZh ? "订单备注 : " : "زاكاز ئەسكەرتىش : " + json.order.remarks)
          .line()
      }
      printText
        .feed(5)
        .cut()

      window.electronAPI && window.electronAPI.printTask && window.electronAPI.printTask({
        text_byte: printText.buffer.flush(),
        json: json,
        try: 1,
        printers: json.order.printers
      })
    }
  }
}
</script>

<template>

</template>

