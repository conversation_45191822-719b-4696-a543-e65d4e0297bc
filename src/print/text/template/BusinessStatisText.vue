<script>
import PrintText from "../utils/PrintTextClass";
import moment from "moment";

export default {
  name: "MemberRechargeText",
  methods: {
    print(json) {
      let isUg = localStorage.getItem("langId") == 1;
      console.log("----营业统计 打印中----", json)
      let printText = new PrintText();
      printText.setPrinterSize(json.order.printers.printer_size === 0 ? printText.model.MAXCHARINLINE_80 : printText.model.MAXCHARINLINE_56)
      printText.setLang(isUg ? 'ug' : 'zh')
      //标题
      let title = isUg ? json.order.merchant.name_ug:json.order.merchant.name_zh

      printText.setTextType(printText.model.TEXT_TYPE_TITLE)
        .setAlign(printText.model.ALIGN_CENTER)
        .textBody(title)
        .text(isUg ? "تىجارەت ئەھۋالى" : "营业统计")
        .setTextType(printText.model.TEXT_TYPE_BODY)
        .line()

        .setAlign(isUg ? printText.model.ALIGN_RIGHT : printText.model.ALIGN_LEFT)

        .textAlign(isUg ? "باشلىنىش ۋاقتى" : "开始时间", json.order.begin_date)
        .textAlign(isUg ?  "ئاخىرلىشىش ۋاقتى" : "结束时间", json.order.end_date)
        .textAlign(isUg ? "بېسىلغان ۋاقتى" : "打印日期", moment().format("YYYY-MM-DD HH:mm:ss"))
        .line()

        .setAlign(printText.model.ALIGN_CENTER)
        .text(isUg ? "تىجارەت ئەھۋالى" : "营业情况");

      for (const report of json.order.headCashInfo) {
        printText.textAlign(report.name,this.formatPrice(report.value))
      }

      printText.line().text(isUg ? "ئالغان پۇل تەپسىلاتى" : "实收金额详情")

      for (const price of json.order.order_proportion) {
        printText.textAlign(price.payment_type_name,this.formatPrice(price.amount))
      }
      printText.line()
        .text(isUg ? "زاكاز ئەھۋالى" : "订单详情")
        .feed()
        .textAlign(isUg ? "ئومۇمىي خېرىدار سانى" : "客户总数" , json.order.orderStatisticsInfo.customers_count+"")
        .textAlign(isUg ?  "ئومۇمىي زاكاز سانى" : "订单总数", json.order.orderStatisticsInfo.order_count+"")
        .textAlign(isUg ?  "ئوتتۇرچە زاكاز سوممىسى" : "单均消费", this.formatPrice(json.order.orderStatisticsInfo.order_avg))
        .textAlign(isUg ?  "ئوتتۇرچە خېرىدار سوممىسى" : "人均消费", this.formatPrice(json.order.orderStatisticsInfo.customers_avg))
        .line()
        .text(isUg ? "ئەزا ئەھۋالى" : "会员详情")
        .textAlign(isUg ?  "يېڭى قوشۇلغان ئەزا سانى" : "新增会员数量", json.order.vip.vip_count+"")
        .textAlign(isUg ?  "ئومومىي قاچىلانغان پۇل" : "充值总金额", this.formatPrice(json.order.vip.vip_recharge_amount))
        .textAlign(isUg ?  "سوۋغات قىلىنغان پۇل" : "赠送金额", this.formatPrice(json.order.vip.vip_recharge_present_amount))
        .textAlign(isUg ?  "نەق پۇل" : "现金", this.formatPrice(json.order.vip.vip_total_Balance))
        // .text(isUg ? , json.order.mobile)
        .feed(5)
        .cut()
      window.electronAPI && window.electronAPI.printTask && window.electronAPI.printTask({text_byte:printText.buffer.flush(),json:json,try:1,printers:json.order.printers})

    },
    formatPrice(num) {
      const number = parseFloat(num);
      const formatter = new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      const formatted = formatter.format(number);
      if (formatted.includes(".")) {
        return formatted+"";
      } else {
        return formatted.replace(".00", "")+"";
      }
    }

  }
}
</script>

<template>

</template>

