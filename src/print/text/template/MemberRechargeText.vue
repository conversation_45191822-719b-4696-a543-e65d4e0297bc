<script>
import PrintText from "../utils/PrintTextClass";
import moment from "moment";

export default {
  name: "MemberRechargeText",
  methods: {
    print(json) {
      let isUg = localStorage.getItem("langId") == 1;
      console.log("----订单换台 打印中----", json)
      let printText = new PrintText();
      printText.setPrinterSize(json.order.printers.printer_size === 0 ? printText.model.MAXCHARINLINE_80 : printText.model.MAXCHARINLINE_56)
      printText.setLang(isUg ? 'ug' : 'zh')
      //标题
      let title = isUg ? 'ئەزا پۇل قاچىلاش تالونى' : '会员充值订单'

      printText.setTextType(printText.model.TEXT_TYPE_BODY_BIG)
        .setAlign(printText.model.ALIGN_CENTER)
        .text(title)

        .setTextType(printText.model.TEXT_TYPE_BODY)
        .setAlign(isUg ? printText.model.ALIGN_RIGHT : printText.model.ALIGN_LEFT)

        .text(isUg ? 'تېلىفون نومۇرى : ' : '电话号码 : ', json.order.mobile)
        .text(isUg ? 'ئەزا نامى : ' : '会员名称 : ', json.order.name)
        .text(isUg ? 'پۇل قاچىلانغان ۋاقت : ' : '充值日期 : ', moment().format(json.order.printers.printer_size == 1 ? "HH:mm:ss" : "YYYY-MM-DD HH:mm:ss"))
        .text(isUg ? 'پۇل تۆلەش شەكلى : ' : '支付方式 : ', json.order.payment_type)
        .line()
        .text(isUg ? 'قاچىلاش سوممىسى : ' : '充值金融 : ',  this.formatPrice(json.order.recharge_amuunt))
        .text(isUg ? 'سوۋغات سوممىسى : ' : '赠送金额 : ',  this.formatPrice(json.order.present_amount))
        .text(isUg ? 'قالدۇق سوممىسى : ' : '余额 : ',  this.formatPrice(json.order.balance))
        .feed(3)
        .text(isUg ? 'ئەزا ئىمزاسى : ' : '客户签字 : ', '_________________')

        .feed(5)
        .cut()
      window.electronAPI && window.electronAPI.printTask && window.electronAPI.printTask({text_byte:printText.buffer.flush(),json:json,try:1,printers:json.order.printers})
    },
    formatPrice(num) {
      const number = parseFloat(num);
      const formatter = new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      const formatted = formatter.format(number);
      if (formatted.includes(".")) {
        return formatted+"";
      } else {
        return formatted.replace(".00", "")+"";
      }
    }

  }
}
</script>

<template>

</template>

