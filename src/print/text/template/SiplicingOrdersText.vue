<script>
import moment from "moment/moment";
import PrintText from '../utils/PrintTextClass'

export default {
  name: "ExchangeOrderText",
  methods:{
    print(json) {
      let isZh = localStorage.getItem("langId") != 1;
      console.log("----订单换台 打印中----",json)
      let printText = new PrintText();
      printText.setPrinterSize(json.order.printers[0].printer_size===0?printText.model.MAXCHARINLINE_80:printText.model.MAXCHARINLINE_56)
      printText.setLang(isZh?'zh':'ug')
      //标题
      let  title = isZh ? '订单拼单' : 'زاكاز بىرلەشتۈرۈش'

      printText.setTextType(printText.model.TEXT_TYPE_BODY_BIG)
        .setAlign(printText.model.ALIGN_CENTER)
        .text(title)

        .setTextType(printText.model.TEXT_TYPE_BODY)
        .setAlign(isZh?printText.model.ALIGN_LEFT:printText.model.ALIGN_RIGHT)

        .text(isZh?'拼单人 : ':'زاكاز بىرلەشتۈرگۈچى : ', json.order.operator)
        .text(isZh?"原台 : ":"ئەسلى ئۈستەل : ",json.order[isZh?"origin_table_name_zh":"origin_table_name_ug"])
        .text(isZh?'订单号 : ':'زاكاز نومۇرى : ',json.order.origin_order_no)
        .text(isZh?"人数 : ":"ئادەم سانى : ",json.order.customers_count+"")
        .text(isZh?"时间 : ":"ۋاقتى : ",moment().format("HH:mm:ss"))

        .line()
        .text(isZh?'目标台 : ':'يۆتكەلگەن ئۈستەل : ',json.order[isZh?"target_table_name_zh":"target_table_name_ug"])
        .text(isZh?'目标订单 : ':'بىرلەشكەن زاكاز : ',json.order.target_order_no)
        .feed(5)
        .cut()
      window.electronAPI && window.electronAPI.printTask && window.electronAPI.printTask({text_byte:printText.buffer.flush(),json:json,try:1,printers:json.order.printers[0]})
    }
  }
}
</script>

<template>

</template>

