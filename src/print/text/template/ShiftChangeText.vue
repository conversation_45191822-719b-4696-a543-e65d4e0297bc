<script>
import PrintText from "../utils/PrintTextClass";
import moment from "moment";

export default {
  name: "FoodsStatisText",
  methods: {
    print(json) {
      let isUg = localStorage.getItem("langId") == 1;
      console.log("----换班 打印中----", json)
      let printText = new PrintText();
      printText.setPrinterSize(json.order.printers.printer_size === 0 ? printText.model.MAXCHARINLINE_80 : printText.model.MAXCHARINLINE_56)
      printText.setLang(isUg ? 'ug' : 'zh')
      //标题
      let title = isUg ? json.order.merchant.name_ug : json.order.merchant.name_zh

      printText.setTextType(printText.model.TEXT_TYPE_BODY_BIG)
        .setAlign(printText.model.ALIGN_CENTER)
        .textBody(title)
        .text(isUg ? "ئىسمېنا خاتىرىسى" : "班次统计")
        .setTextType(printText.model.TEXT_TYPE_BODY)
        .line()

        .setAlign(isUg ? printText.model.ALIGN_RIGHT : printText.model.ALIGN_LEFT)

        .textAlign(isUg ? "باشلىنىش ۋاقتى" : "开始时间", json.order.start_at)
        .textAlign(isUg ? "ئاخىرلىشىش ۋاقتى" : "结束时间", json.order.leave_at)
        .textAlign(isUg ? "بېسىلغان ۋاقتى" : "打印日期", moment().format("YYYY-MM-DD HH:mm:ss"))
        .line()

        .textAlign(isUg ? "زاپاس پۇل : " : "备用金 : ", this.formatPrice(json.order.alternate_amount))
        .textAlign(isUg ? "ئالدىنقى ئىسمېنا سوممىسى : " : "前班余额 : ", this.formatPrice(json.order.working_balance))
        .line()
        .textAlign(isUg ? "ئومومىي سودا : " : "营业总金额 : ", this.formatPrice(json.order.paid_amount))

      for (const item of json.order.paymentProportion) {
        printText.textAlign(item.payment_type, this.formatPrice(item.total))
      }

      printText.line()
        .textAlign(isUg ? "ئۆتكۈزگەن پۇل : " : "交班金额 : ", this.formatPrice(json.order.submitted_amount))
        .line()

        if(json.order.printers.printer_size === 0){
          printText
            .feed(4)
            .textAlign('_________________', '____________________')
            .textAlign((isUg ? "ئىسمېنا ئۆتكۈزگۈچى" : "交班人"), (isUg ? "ئىسمېنا ئۆتكۈزىۋالغۇچى" : "接班人"))
        }else{
          printText
            .feed(3)
            .setAlign(printText.model.ALIGN_CENTER)
            .text('____________________')
            .text(isUg ? "ئىسمېنا ئۆتكۈزگۈچى" : "交班人")
            .feed(3)
            .text('____________________')
            .text(isUg ? "ئىسمېنا ئۆتكۈزىۋالغۇچى" : "接班人")
        }
      printText.feed(5)
        .cut()
      window.electronAPI && window.electronAPI.printTask && window.electronAPI.printTask({text_byte:printText.buffer.flush(),json:json,try:1,printers:json.order.printers})
    },
    formatPrice(num) {
      const number = parseFloat(num);
      const formatter = new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      const formatted = formatter.format(number);
      if (formatted.includes(".")) {
        return formatted + "";
      } else {
        return formatted.replace(".00", "") + "";
      }
    }

  }
}
</script>

<template>

</template>

