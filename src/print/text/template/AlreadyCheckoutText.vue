<script>
import moment from "moment/moment";
import PrintText from '../utils/PrintTextClass'

export default {
  name: "ExchangeOrderText",
  methods: {
    print(json) {
      let isUg = localStorage.getItem("langId") == 1;
      console.log("----已结账 打印中----", isUg, json)
      let printText = new PrintText();
      printText.setPrinterSize(json.order.printers.printer_size === 0 ? printText.model.MAXCHARINLINE_80 : printText.model.MAXCHARINLINE_56)
      printText.setLang(isUg ? 'ug':'zh')
      //标题
      let title = json.order.merchant[isUg ? "name_ug" : "name_zh"]

      printText.setTextType(printText.model.TEXT_TYPE_TITLE)
        .setAlign(printText.model.ALIGN_CENTER)
        .textBody(title)
        .setTextType(printText.model.TEXT_TYPE_BODY)
        .text(isUg ? "ھېسابات تالونى" : "结账单")
        .line()
        .setAlign(isUg ? printText.model.ALIGN_RIGHT : printText.model.ALIGN_LEFT)
        .text(isUg ? "ئۈستەل : " : "餐台 : ", json.order[isUg ? "table_name_ug" : "table_name_zh"])
        .text(isUg ? "پۇل يىغقۇچى : " : "收银员 : ", json.order.cashier_name)
        .text(isUg ? 'زاكاز نومۇرى : ' : '订单号 : ', json.order.order_no)
        .text(isUg ? "ۋاقتى : " : "时间 : ", moment().format("HH:mm:ss"))
        .line()
        .textAlign(isUg ? "تاماق" : "美食"
          , isUg ? "باھاسى" : "单价"
          , isUg ? "سانى" : "数量"
          , isUg ? "جەمئىي" : "总计")
        .feed()
      json.order.order_details.forEach(item => {
        printText.text(item.food_name_ug)
          .textAlign(
            item.food_name_zh
            , this.formatPrice(item.price)
            , (parseFloat(item.foods_count) + "")
            , this.formatPrice(item.foods_count * item.price))
      })
      printText.line()
        .textAlign(isUg ? "ئالغان پۇل (已收现金) : " : "已收现金 : ", this.formatPrice(
          json.order.payment_type_id == 4
            ? json.order.order_vip_price
            : json.order.collected_amount))
        .textAlign(isUg ? "قايتۇرغان پۇل (找零) : " : "找零 : ", this.formatPrice(json.order.give_change))
        .textAlign(isUg ? "كېمەيتىلگەن سوممىسى : " : "抹零金额 : ", this.formatPrice(json.order.ignore_price))
        .textAlign(isUg ? "ئومۇمىي سوممىسى (现金) : " : "总价格 (现金) : ", this.formatPrice(json.order.order_price))
        .line()
        .textBody((isUg ? "ئادرېس : " : "地址 : ") + json.order.merchant[isUg ? "address_ug" : "address_zh"])
        .text((isUg ? "ئالاقىلىشىش نومۇرى : " : "联系号码 : "), json.order.merchant.phone)
        .line()
        .setAlign(printText.model.ALIGN_CENTER)
        .text(isUg ? "شىنجاڭ ئالماس يۇمشاق دېتال چەكلىك شىركىتى" : "新疆金钻软件有限公司")
        .text("************")
        .feed(5)
        .cut()
      window.electronAPI && window.electronAPI.printTask && window.electronAPI.printTask({text_byte:printText.buffer.flush(),json:json,try:1,printers:json.order.printers})

    },
    formatPrice(num) {
      const number = parseFloat(num);
      const formatter = new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      const formatted = formatter.format(number);
      if (formatted.includes(".")) {
        return formatted + "";
      } else {
        return formatted.replace(".00", "") + "";
      }
    },
  }
}
</script>

<template>

</template>

<style scoped lang="less">

</style>
