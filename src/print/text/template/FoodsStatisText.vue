<script>
import PrintText from "../utils/PrintTextClass";
import moment from "moment";

export default {
  name: "FoodsStatisText",
  methods: {
    print(json) {
      let isUg = localStorage.getItem("langId") == 1;
      console.log("----美食统计 打印中----", json);
      let printText = new PrintText();
      printText.setPrinterSize(
        json.order.printers.printer_size === 0
          ? printText.model.MAXCHARINLINE_80
          : printText.model.MAXCHARINLINE_56
      );
      printText.setLang(isUg ? "ug" : "zh");
      //标题
      let title = isUg
        ? json.order.merchant.name_ug
        : json.order.merchant.name_zh;

      printText
        .setTextType(printText.model.TEXT_TYPE_BODY_BIG)
        .setAlign(printText.model.ALIGN_CENTER)
        .textBody(title)
        .text(isUg ? "تاماق سېتىلىش ئەھۋالى " : "美食统计")
        .setTextType(printText.model.TEXT_TYPE_BODY)
        .line()

        .setAlign(
          isUg ? printText.model.ALIGN_RIGHT : printText.model.ALIGN_LEFT
        )

        .textAlign(
          isUg
            ? json.order.printers.printer_size === 0
              ? "باشلىنىش ۋاقتى"
              : "باشلىنىش"
            : "开始时间",
          json.order.begin_date
        )
        .textAlign(
          isUg
            ? json.order.printers.printer_size === 0
              ? "ئاخىرلىشىش ۋاقتى"
              : "ئاخىرلىشىش"
            : "结束时间",
          json.order.end_date
        )
        .textAlign(
          isUg
            ? json.order.printers.printer_size === 0
              ? "بېسىلغان ۋاقتى"
              : "بېسىلغان"
            : "打印日期",
          moment().format("YYYY-MM-DD HH:mm:ss")
        )
        .line()

        .setAlign(printText.model.ALIGN_CENTER)
        .text(isUg ? "تاماق تۈرى سېتىلىشى" : "美食分类营业情况");

      for (const category of json.order.food_category_proportion) {
        printText.textAlign(
          category.food_category_name,
          this.formatPrice(category.amount)
        );
      }

      printText
        .line()
        .text(isUg ? "تاماق سېتىلىشى" : "美食营业情况")
        .textAlign(
          isUg ? "تاماق" : "美食",
          isUg ? "جەمئىي" : "总计",
          isUg ? "باھاسى" : "单价",
          isUg ? "سانى" : "数量"
        );

      for (const item of json.order.list) {
        if (item.foods_list.length > 1) {
          for (let food of item.foods_list) {
            if (food.food_id != 0) {
              printText
                .setAlign(
                  isUg
                    ? printText.model.ALIGN_RIGHT
                    : printText.model.ALIGN_LEFT
                )
                .text(food.name)
                .textAlign(
                  " ",
                  this.formatPrice(food.real_amount),
                  this.formatPrice(food.price),
                  parseFloat(food.count) + ""
                );
            }
          }
        }
      }
      printText.feed(5).cut();
      window.electronAPI &&
        window.electronAPI.printTask &&
        window.electronAPI.printTask({
          text_byte: printText.buffer.flush(),
          json: json,
          try: 1,
          printers: json.order.printers
        });
    },
    formatPrice(num) {
      const number = parseFloat(num);
      const formatter = new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      const formatted = formatter.format(number);
      if (formatted.includes(".")) {
        return formatted + "";
      } else {
        return formatted.replace(".00", "") + "";
      }
    }
  }
};
</script>

<template> </template>
