export default {
  data:null,
  map(){
    if (!this.data){
      this.data = new Map();
      this.data.set('(',  122);
      this.data.set(')',  121);
      this.data.set('ﻼ',  133);
      this.data.set('ﺌ',  134);
      this.data.set('ﯥ',  135);
      this.data.set('ﯧ',  136);
      this.data.set('ﯦ',  137);
      this.data.set('ﻰ',  123);//138
      this.data.set('ﯩ',  139);
      this.data.set('ﯨ',  140);
      this.data.set('ﻲ',  124);//141
      this.data.set('ﻴ',  142);
      this.data.set('ﻳ',  143);
      this.data.set('ﺎ',  144);
      this.data.set('ﻪ',  145);
      this.data.set('ﺐ',  146);
      this.data.set('ﺒ',  147);
      this.data.set('ﺑ',  148);
      this.data.set('ﭗ',  149);
      this.data.set('ﭙ',  150);
      this.data.set('ﭘ',  151);
      this.data.set('ﺖ',  152);
      this.data.set('ﺘ',  153);
      this.data.set('ﺗ',  154);
      this.data.set('ﺞ',  155);
      this.data.set('ﺠ',  156);
      this.data.set('ﺟ',  157);
      this.data.set('ﭻ',  158);
      this.data.set('ﭽ',  159);
      this.data.set('ﭼ',  160);
      this.data.set('ﺦ',  161);
      this.data.set('ﺨ',  162);
      this.data.set('ﺧ',  163);
      this.data.set('ﺪ',  164);
      this.data.set('ﺮ',  165);
      this.data.set('ﺰ',  166);
      this.data.set('ﮋ',  167);
      this.data.set('ﺲ',  168);
      this.data.set('ﺴ',  169);
      this.data.set('ﺳ',  170);
      this.data.set('ﺶ',  171);
      this.data.set('ﺸ',  172);
      this.data.set('ﺷ',  173);
      this.data.set('ﻎ',  174);
      this.data.set('ﻐ',  175);
      this.data.set('ﻏ',  176);
      this.data.set('ﻒ',  177);
      this.data.set('ﻔ',  178);
      this.data.set('ﻓ',  179);
      this.data.set('ﻖ',  180);
      this.data.set('ﻘ',  181);
      this.data.set('ﻗ',  182);
      this.data.set('ﻚ',  183);
      this.data.set('ﻜ',  184);
      this.data.set('ﻛ',  185);
      this.data.set('ﮓ',  186);
      this.data.set('ﮕ',  187);
      this.data.set('ﮔ',  188);
      this.data.set('ﯔ',  189);
      this.data.set('ﯖ',  190);
      this.data.set('ﯕ',  191);
      this.data.set('ﻞ',  192);
      this.data.set('ﻠ',  193);
      this.data.set('ﻟ',  194);
      this.data.set('ﻢ',  195);
      this.data.set('ﻤ',  196);
      this.data.set('ﻣ',  197);
      this.data.set('ﻦ',  198);
      this.data.set('ﻨ',  199);
      this.data.set('ﻧ',  200);
      this.data.set('ﮭ',  201);
      this.data.set('ﻮ',  202);
      this.data.set('ﯘ',  203);
      this.data.set('ﯚ',  204);
      this.data.set('ﯜ',  205);
      this.data.set('ﯟ',  206);
      this.data.set('ﯤ',  207);
      this.data.set('ﺍ',  208);
      this.data.set('ﻩ',  209);
      this.data.set('ﺏ',  210);
      this.data.set('ﭖ',  211);
      this.data.set('ﺕ',  212);
      this.data.set('ﺝ',  213);
      this.data.set('ﭺ',  214);
      this.data.set('ﺥ',  215);
      this.data.set('ﺩ',  216);
      this.data.set('ﺭ',  217);
      this.data.set('ﺯ',  218);
      this.data.set('ﮊ',  219);
      this.data.set('ﺱ',  220);
      this.data.set('ﺵ',  221);
      this.data.set('ﻍ',  222);
      this.data.set('ﻑ',  223);
      this.data.set('ﻕ',  224);
      this.data.set('ﻙ',  225);
      this.data.set('ﮒ',  226);
      this.data.set('ﯓ',  227);
      this.data.set('ﻝ',  228);
      this.data.set('ﻡ',  229);
      this.data.set('ﻥ',  126); //230
      this.data.set('ﮬ',  231);
      this.data.set('ﻭ',  232);
      this.data.set('ﯗ',  233);
      this.data.set('ﯙ',  234);
      this.data.set('ﯛ',  235);
      this.data.set('ﯞ',  125); //236
      this.data.set('ﯤ',  237);
      this.data.set('ﻯ',  238);
      this.data.set('ﻱ',  239);
      this.data.set('ﻻ',  240);
      this.data.set('؟',  241);
      this.data.set('ﺋ',  242);
      this.data.set('،',  127);
      this.data.set(',',  127);
      this.data.set('，',  127);

      this.data.set('ا',  208);
      this.data.set('ى',  238);
      this.data.set('ە',  209);
      this.data.set('ۇ',  233);
      this.data.set('ن',  126);
      this.data.set('ۈ',  235);
      this.data.set('ر',  217);
      this.data.set('ت',  152);
      this.data.set('خ',  215);


    }
    return this.data;
  }
}
