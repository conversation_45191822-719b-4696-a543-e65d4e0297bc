import ugChar from "./UyghurCharUtils";
import iconv from "iconv-lite";
import {MutableBuffer} from 'mutable-buffer'
import UgCharMap from './UgCharMap'

/**
 * 文本打印指令封装
 */
class PrintText {
  constructor() {
    //参数
    this.model = {
      TYPE_LINE: 0,
      TYPE_TEXT: 1,
      TYPE_CUT: 2,

      ALIGN_LEFT: 48,
      ALIGN_CENTER: 49,
      ALIGN_RIGHT: 50,

      TEXT_TYPE_TITLE: 0,
      TEXT_TYPE_BODY: 1,
      TEXT_TYPE_BODY_BIG: 2,

      MAXCHARINLINE_80: 46,
      MAXCHARINLINE_56: 32,
    }

    this.max_charin_line = this.model.MAXCHARINLINE_80
    this.align = this.model.ALIGN_RIGHT
    this.textType = this.model.TEXT_TYPE_BODY

    this.isZh = true

    this.buffer = new MutableBuffer()
    this.write([27, 64])
    // this.write([27, 100, 3])
    this.write([27, 116, 30])
    this.beautify()
  }

  /**
   * 设置语言
   * @param lang
   * @returns {PrintText}
   */
  setLang(lang) {
    this.isZh = lang === 'zh'
    return this
  }

  /**
   * 设置打印机类型
   * @param size
   * @returns {PrintText}
   */
  setPrinterSize(size) {
    this.max_charin_line = size
    return this
  }

  /**
   * 设置对齐方式
   * @param align
   * @returns {PrintText}
   */
  setAlign(align) {
    this.align = align
    return this
  }

  /**
   * 设置文本类型 标题|正文
   * @param textType
   * @returns {PrintText}
   */
  setTextType(textType) {
    this.textType = textType
    return this
  }


  /**
   * 画横线
   * @returns {PrintText}
   */
  line() {
    const tempTextType = this.textType
    this.setTextType(this.model.TEXT_TYPE_BODY)
    this.printTextType()
    let str = ''
    for (let i = 0; i < this.max_charin_line; i++) {
      str += '-'
    }
    this.write(str + '\n')
    this.setTextType(tempTextType)
    return this
  }


  /**
   * 内部方法 获取空格长度
   * @param str
   * @returns {number}
   */
  getSpaceLength(str) {
    let count = 0;
    for (let i = 0; i < str.length; i++) {
      let c = str[i];
      let isChinese = c >= '\u4e00' && c <= '\u9fa5'
      if (c==='￥'){
        isChinese = true
      }
      if (isChinese) {
        count += 2
      } else {
        count += 1

      }
    }
    return count
  }

  /**
   * 获取空格
   * @param count
   * @returns {string}
   */
  getSpace(count) {
    let space = ''
    for (let i = 0; i < count; i++) {
      space += ' '
    }
    return space
  }

  /**
   * 切纸
   * @returns {PrintText}
   */
  cut() {
    this.write([29, 86, 1]);
    return this
  }

  /**
   * 换行
   * @param count
   * @returns {PrintText}
   */
  feed(count) {
    if (count) {
      for (let i = 0; i < count; i++) {
        this.write(10);
      }
    } else {
      this.write(0);
    }
    return this
  }

  /**
   * 设置文本类型，以及对齐方式
   */
  printTextType() {
    //判断是否标题
    if (this.textType === this.model.TEXT_TYPE_TITLE) {
      this.write([27, 33, 48]);
    } else if(this.textType === this.model.TEXT_TYPE_BODY) {
      this.write([27, 33, 0]);
    } else {
      this.write([27, 33, 16]);
    }

    if (this.align && this.align !== this.model.ALGIN_DEFAULT) {
      this.write([27, 97, this.align])
    }
  }

  /**
   * 文本特殊处理
   * @param text
   * @returns {*|string}
   */
  specialFormat(text){

    //处理‘-’和维语一起是一些特殊情况 比如 《3-ئۈستەل》
    if (text && text.split('-').length===2){
      const before = text.split('-')[0]
      const after = text.split('-')[1]

      if (before || after || before.length!==0 || after.length!==0) {
        if (!this.isUyghur(before[before.length-1]) && this.isUyghur(after[0])){
          text = '-'+before+after
        }
      }

    }
    if (text){
      text = text.replace('¥','￥')
    }
    return text
  }

  /**
   * 打印一行连续字符串
   * @returns {PrintText}
   */
  text() {
    let argu = []

    if (!this.isZh) {
      for (let i = arguments.length - 1; i >= 0; i--) {
        argu.push(arguments[i])
      }
    }else {
      argu = arguments
    }

    let text = ''

    const utils = new ugChar();
    for (let i = 0; i < argu.length; i++) {
      console.log("调用参数" + i + "是:" + argu[i] + "\n");
      let content = argu[i]
      content = content && utils.Basic2Extend(content)
      content = content && this.reversal(content)
      text += content
    }
    this.textWrite(text)
    return this
  }

  /**
   * 打印一行连续字符串，并且左右中对齐
   * @param content 左对齐文本
   * @param content2 右或中对齐文本
   * @param content3  右对齐文本
   * @returns {PrintText}
   */
  textAlign(content, content2, content3,content4) {

    //维语时反转
    if (!this.isZh){
      if (content4){
        const temp = content
        const temp2 = content2
        content = content4
        content2 = content3
        content4 = temp
        content3 = temp2
      }else if (content3){
        const temp = content
        content = content3
        content3 = temp
      }else if (content2){
        const temp = content
        content = content2
        content2 = temp
      }
    }


    const utils = new ugChar();
    content = content && utils.Basic2Extend(content);
    content2 = content2 && utils.Basic2Extend(content2);
    content3 = content3 && utils.Basic2Extend(content3);
    content4 = content3 && utils.Basic2Extend(content4);

    content = content && this.reversal(content)
    content2 = content2 && this.reversal(content2)
    content3 = content3 && this.reversal(content3)
    content4 = content3 && this.reversal(content4)


    let text = ''

    if (content2) {
      if (content4){
        let rightSpace
        let centerSpace
        let leftSpace

        if (this.isZh) {
          leftSpace = this.getSpace(this.max_charin_line / 10 * 5 - this.getSpaceLength(content)-2);
          centerSpace = this.getSpace(this.max_charin_line / 10 * 2 - this.getSpaceLength(content2));
          rightSpace = this.getSpace(this.max_charin_line / 10 * 3 - this.getSpaceLength(content3) - this.getSpaceLength(content4));
        } else {
          rightSpace = this.getSpace(this.max_charin_line / 10 * 5 - this.getSpaceLength(content4)-2);
          centerSpace = this.getSpace(this.max_charin_line / 10 * 2 - this.getSpaceLength(content3));
          leftSpace = this.getSpace(this.max_charin_line / 10 * 3 - this.getSpaceLength(content2) - this.getSpaceLength(content));
        }

        text = content + leftSpace + content2 + centerSpace + content3 + rightSpace + content4

      }else if (content3) { //左中右对齐
        let rightSpace
        let leftSpace

        if (this.isZh) {
          leftSpace = this.getSpace(this.max_charin_line / 10 * 4 - this.getSpaceLength(content));
          rightSpace = this.getSpace(this.max_charin_line / 10 * 6 - this.getSpaceLength(content2) - this.getSpaceLength(content3));
        } else {
          rightSpace = this.getSpace(this.max_charin_line / 10 * 6 - this.getSpaceLength(content3));
          leftSpace = this.getSpace(this.max_charin_line / 10 * 4 - this.getSpaceLength(content) - this.getSpaceLength(content2));
        }

        text = content + leftSpace + content2 + rightSpace + content3

      } else {  //左右对齐
        text = content + this.getSpace(this.max_charin_line - this.getSpaceLength(content) - this.getSpaceLength(content2)) + content2
      }
    }
    this.textWrite(text)

    return this
  }

  /**
   * 打印正文
   * @param str
   * @returns {PrintText}
   */
  textBody(str) {

    let utils = new ugChar()
    str = utils.Basic2Extend(str);

    let index = 0;
    let startIndex = 0;

    let text = ''
    for (let i = 0; i < str.length; i++) {
      let nowIndex = startIndex + (this.textType === this.model.TEXT_TYPE_TITLE ? this.max_charin_line / 2 : this.max_charin_line)
      if (nowIndex > str.length) {
        text += this.reversal(str.substring(startIndex, str.length)) + '\n'
        break;
      }
      for (let j = nowIndex; j >= 0; j--) {
        index = j
        if (str[j] === ' ' || !this.isUyghur(str[j])) {
          text += this.reversal(str.substring(startIndex, j)) + '\n'
          break;
        }
      }
      if (nowIndex === str.length) {
        break
      }
      console.log("startIndex:" + startIndex)
      startIndex = index;
    }
    console.log(str.substring(0, 6))
    console.log(str)
    console.log("text", text)

    this.textWrite(text)

    return this
  }

  /**
   * 格式化好的文本转byte
   * @param text
   */
  textWrite(text) {
    let addCancelKanjiMode = [28, 46]
    let addSelectKanjiMode = [28, 38]

    this.useFont()
    this.write(addCancelKanjiMode)
    this.addSetCharcterSize(true)
    this.printTextType()
    let isKanjiMode = false

    for (let k = 0; k < text.length; k++) {
      let c = text[k];

      if (this.isUyghur(c)) { //维语处理
        if (isKanjiMode) {
          this.useFont()
          this.write(addCancelKanjiMode)
          isKanjiMode = false
          this.addSetCharcterSize(true)
          this.printTextType()
        }
        this.write(UgCharMap.map().get(c))
      } else {
        if (!isKanjiMode) { //中文处理
          this.cancelFont()
          this.write(addSelectKanjiMode)
          isKanjiMode = true
          this.addSetCharcterSize(false)
          this.printTextType()
        }
        let encode = iconv.encode(c, "GB18030");
        for (let encodeElement of encode) {
          this.write(encodeElement)
        }
      }
    }
    // 换行
    this.write(10)
  }

  /**
   * 判断是否为维语字符
   * @param c 判断的字符
   * @param before 前一个字符
   * @param after 后一个字符
   * @returns {false|*}
   */
  isUyghur(c, before, after) {
    //判断标点符号
    let isPunctuation = c === '，'
      || c === ','
      || c === '('
      || c === ')'
      || c === '{'
      || c === '}'

    // 判断前后字符是否都是维语字符
    if (isPunctuation && before && after) {
      let beforeIsUyghur = before || UgCharMap.map().has(c)
      let afterIsUyghur = before || UgCharMap.map().has(c)
      return beforeIsUyghur && afterIsUyghur
    }
    return !isPunctuation && UgCharMap.map().has(c)
  }

  /**
   * 倍率设置
   * @param isUg
   */
  addSetCharcterSize(isUg) {
    if (this.textType === this.model.TEXT_TYPE_TITLE && !isUg) {
      this.write([29, 33, 17])
    } else {
      this.write([29, 33, 0])
    }
  }

  /**
   * 反转字符串
   * @param str
   * @returns {string}
   */
  reversal(str) {
    str = this.specialFormat(str)

    let cache = '';
    let rtl = ''
    for (let i = str.length - 1; i >= 0; i--) {
      if (this.isUyghur(str[i])) {
        if (rtl !== '') {
          cache += rtl
          rtl = ''
        }
        cache += str[i];
      } else {
        rtl = str[i] + rtl
      }
    }
    if (rtl !== '') {
      cache += rtl
    }
    // for (let i = 0; i <= MAX_LENGTH - cache.length; i++) {
    //   cache = cache + ' '
    //   console.log("cache", cache.length)
    // }
    return cache;
  }

  /**
   * 写入buffer
   * @param data
   */
  write(data) {
    if (typeof data === "string") {
      this.buffer.write(data)
    } else if (typeof data === "number") {
      this.buffer.writeUInt8(data)
    } else if (typeof data === "object") {
      for (let datum of data) {
        this.buffer.writeUInt8(datum)
      }
    }
  }

  /**
   * 自定义字符设置
   */
  beautify() {
    this.write([27, 37, 1])
    this.write([27, 38, 3, 121, 127,
      12,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x07, 0xFC, 0x00, 0x0E, 0x0E, 0x00, 0x18,
      0x03, 0x00, 0x30, 0x01, 0x80, 0x60, 0x00, 0xC0, 0x40, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00,
      12,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x60, 0x00, 0xC0, 0x30, 0x01, 0x80, 0x18,
      0x03, 0x00, 0x0E, 0x0E, 0x00, 0x07, 0xFC, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00,
      12,
      /* ALKATIP 字号[16点] 上移[-2]*/
      /* UCS2:0xFBD8  12x24 */
      0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00,
      0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x01, 0xD8, 0x00, 0x03, 0xD0, 0x00, 0x03, 0x70, 0x00, 0x03,
      0x00, 0x00, 0x03, 0x00,
      12,
      0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x1B, 0x00, 0x00, 0x1B, 0x00,
      0x00, 0x18, 0x00, 0x00, 0x1B, 0x00, 0x01, 0xDB, 0x00, 0x03, 0xD0, 0x00, 0x03, 0x70, 0x00, 0x03,
      0x00, 0x00, 0x03, 0x00,
      12,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x01, 0x00, 0x04, 0x03,
      0x8E, 0x0C, 0x05, 0x1F, 0x1C, 0x0E, 0x13, 0x38, 0x05, 0x1B, 0x70, 0x03, 0x8F, 0xC0, 0x01, 0x07,
      0x80, 0x00, 0x00, 0x00,
      12,
      0x00, 0x07, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x1D, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0xC0, 0xC0, 0x01,
      0xC0, 0xC0, 0x00, 0xC0, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x19, 0x80, 0x00, 0x1F, 0x80, 0x00, 0x0E,
      0x00, 0x00, 0x00, 0x00,
      12,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00,
      0x1F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00])
    this.write([27, 37, 1])
  }

  /**
   * 使用自定义字符
   */
  useFont() {
    this.write([27, 37, 1])
  }

  /**
   * 取消自定义字符
   */
  cancelFont() {
    this.write([27, 37, 0])
  }

}


module.exports = PrintText;




