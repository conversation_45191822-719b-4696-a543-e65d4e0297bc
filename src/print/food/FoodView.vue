<template>
  <div
    v-if="
      data &&
        (data.action == '提交订单' ||
          data.action == '加菜' ||
          data.action == '退菜' ||
          data.action == '全单退菜' ||
          data.action == '催单')
    "
    ref="container"
    class="container"
    :style="{
      width: data.order.printers.printer_size == 0 ? '560px' : '384px',
      direction: langId == 1 ? 'rtl' : 'ltr',
      fontSize: data.order.printers.printer_size == 0 ? '28px' : '26px'
    }"
  >
    <div
      v-if="data.action == '提交订单' || data.action == '加菜'"
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'
      }"
    >
      {{ langId == 1 ? "يېڭى تاماق" : "下单" }}
    </div>
    <div
      v-if="data.action == '全单退菜' || data.action == '退菜'"
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'
      }"
    >
      {{ langId == 1 ? "**** تاماق قايتۇرۇش ****" : "**** 退菜 ****" }}
    </div>
    <div
      v-if="data.action == '催单'"
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'
      }"
    >
      {{ langId == 1 ? "**** تاماق سۈيلەش ****" : "**** 催单 ****" }}
    </div>
    <div
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '44px' : '38px',
        padding: '6px 0px'
      }"
    >
      {{ langId == 1 ? addSpacesAroundNumbers(data.order.table_name_ug) : addSpacesAroundNumbers(data.order.table_name_zh) }}
    </div>
    <div
      class="item-line"
      :style="{
        flexDirection: data.order.printers.printer_size == 0 ? 'row' : 'column'
      }"
    >
      <div
        class="line-text half-line"
        :style="{
          width: data.order.printers.printer_size == 0 ? '50%' : '100%'
        }"
      >
        <span>{{ langId == 1 ? "ئادەم سانى : " : "人数 : " }}</span>
        <span class="ltr">{{ data.order.customers_count }}</span>
      </div>
      <div
        class="line-text half-line"
        :style="{
          width: data.order.printers.printer_size == 0 ? '50%' : '100%'
        }"
      >
        <span v-if="data.action == '全单退菜' || data.action == '退菜'">{{
          langId == 1 ? "قايتۇرغۇچى : " : "退菜员 : "
        }}</span>
        <span v-else>{{ langId == 1 ? "تىزىملىغۇچى : " : "点菜员 : " }}</span>
        <span>{{ data.order.operator }}</span>
      </div>
    </div>
    <div class="item-line">
      <div class="line-text">
        {{ langId == 1 ? "زاكاز نومۇرى : " : "订单号 : " }}
      </div>
      <div class="line-text">{{ data.order.order_no }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? "ۋاقتى : " : "时间 : " }}</div>
      <div class="line-text time">{{ time }}</div>
    </div>
    <div class="line"></div>
    <div class="item-line between">
      <div>{{ langId == 1 ? "تاماق" : "美食" }}</div>
      <div>{{ langId == 1 ? "سانى" : "数量" }}</div>
    </div>
    <div
      class="food-list"
      v-if="data.action == '全单退菜' || data.action == '退菜'"
    >
      <div
        class="food-item"
        v-for="item in data.order.order_details"
        :key="item.id"
      >
        <div
          v-for="canceled in item.canceled_foods"
          :key="canceled.id"
          class="canceled-item"
        >
          <div class="item-line between vertical-center">
            <div class="food-name-box">
              <div
                class="food-name food-name-ug rtl"
                :style="{ textAlign: langId == 1 ? 'right' : 'left' }"
              >
                {{ addSpacesAroundNumbers(canceled.food_name_ug) }}
              </div>
              <div
                class="food-name ltr"
                :style="{ textAlign: langId == 1 ? 'right' : 'left' }"
              >
                {{ addSpacesAroundNumbers(canceled.food_name_zh) }}
              </div>
            </div>
            <div class="food-count">{{ parseFloat(canceled.count) }}</div>
          </div>
          <div class="remarks" v-if="canceled.remarks">
            <span>{{ langId == 1 ? "ئەسكەرتىش : " : "备注 : " }}</span>
            <span>{{ addSpacesAroundNumbers(canceled.remarks) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="food-list" v-else>
      <div
        class="food-item"
        v-for="item in data.order.order_details"
        :key="item.id"
      >
        <div class="item-line between vertical-center">
          <div class="food-name-box">
            <div
              class="food-name food-name-ug rtl"
              :style="{ textAlign: langId == 1 ? 'right' : 'left' }"
            >
              {{ addSpacesAroundNumbers(item.food_name_ug) }}
            </div>
            <div
              class="food-name ltr"
              :style="{ textAlign: langId == 1 ? 'right' : 'left' }"
            >
              {{ addSpacesAroundNumbers(item.food_name_zh) }}
            </div>
          </div>
          <div class="food-count">{{ parseFloat(item.foods_count) }}</div>
        </div>
        <div class="remarks" v-if="item.remarks">
          <span>{{ langId == 1 ? "ئەسكەرتىش : " : "备注 : " }}</span>
          <span>{{ addSpacesAroundNumbers(item.remarks) }}</span>
        </div>
      </div>
    </div>
    <div class="line"></div>
    <div class="remarks" v-if="data.order.remarks">
      <span>{{ langId == 1 ? "زاكاز ئەسكەرتىش : " : "订单备注 : " }}</span>
      <span>{{ addSpacesAroundNumbers(data.order.remarks) }}</span>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import print from "./../print.vue";
export default {
  data() {
    return {
      data: null,
      time: "",
      langId: 1
    };
  },
  methods: {
    print(json) {
      return new Promise(resolve => {
        this.data = json;
        this.time = moment().format("YYYY-MM-DD HH:mm:ss");
        this.langId = localStorage.getItem("langId");
        console.log("打印开始 --- 1", this.data);
        this.$nextTick(() => {
          this.data = null;
          print.saveImage(this.$refs.container, json).then(()=>{
            resolve();
          })
        });
      });
    },
    addSpacesAroundNumbers(str) {
      // 定义正则表达式，用于匹配字符串中的数字
      const regex = /(\w)/g;

      // 使用replace方法，并通过回调函数在匹配到的数字前后添加空格
      // \u200B是Unicode中的空格字符，用于在数字前后添加不占位置的空格
      const result = str.replace(regex, '\u200B$1\u200B');

      // 返回处理后的字符串
      return result;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 12px;
  padding-bottom: 30px;
  padding-top: 20px;
  .report-title {
    font-weight: bold;
    text-align: center;
  }
  .item-line {
    display: flex;
    .half-line {
      width: 50%;
    }
    &.between {
      justify-content: space-between;
    }
    &.vertical-center {
      align-items: center;
    }
  }
  .food-list {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    .food-name-box {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      .food-name-ug {
        font-size: 42px;
      }
    }
  }
  .remarks {
    font-size: 48px;
    line-height: 1.1;
    padding: 10px 0px;
  }
  .line {
    width: 100%;
    height: 2px;
    background-color: #000;
  }
  .time {
    direction: ltr;
  }
  .rtl {
    direction: rtl;
  }
  .ltr {
    direction: ltr;
  }
  .food-count {
    font-size: 48px;
    direction: ltr;
  }
}
</style>
