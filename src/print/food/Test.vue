<script>
import print from "../print.vue";

export default {
  name: "Test",
  methods: {
    print(json) {
      json.order.printers.beautiful_print = 1
      const data = {
        image:null,
        json:json,
        try:0,
        test:1,
        printers:json.order.printers
      }
      window
      && window.electronAPI
      && window.electronAPI.printTask
      && window.electronAPI.printTask(data)
    }
  }
}
</script>

<template>

</template>

<style scoped lang="less">

</style>
