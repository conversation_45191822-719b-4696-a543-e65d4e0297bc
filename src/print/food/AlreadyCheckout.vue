<template>
  <div
    v-if="data && data.action == '已结账'"
    ref="container"
    class="container"
    :style="{
      width: data.order.printers.printer_size == 0 ? '560px' : '384px',
      direction: langId == 1 ? 'rtl' : 'ltr',
      fontSize: data.order.printers.printer_size == 0 ? '28px' : '26px'
    }"
  >
    <div
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'
      }"
    >
      {{
        langId == 1 ? addSpacesAroundNumbers(data.order.merchant.name_ug) : addSpacesAroundNumbers(data.order.merchant.name_zh)
      }}
    </div>
    <div
      class="report-title"
      :style="{
        fontWeight: 'normal',
        fontSize: data.order.printers.printer_size == 0 ? '34px' : '30px'
      }"
    >
      {{ langId == 1 ? "ھېسابات تالونى" : "结账单" }}
    </div>
    <div class="line"></div>
    <div
      class="item-line"
      :style="{
        flexDirection: data.order.printers.printer_size == 0 ? 'row' : 'column'
      }"
    >
      <div
        class="line-text half-line"
        :style="{
          width: data.order.printers.printer_size == 0 ? '50%' : '100%'
        }"
      >
        <span>{{ langId == 1 ? "ئۈستەل : " : "餐台 : " }}</span>
        <span>{{
          langId == 1 ? addSpacesAroundNumbers(data.order.table_name_ug) : addSpacesAroundNumbers(data.order.table_name_zh)
        }}</span>
      </div>
      <div
        class="line-text half-line"
        :style="{
          width: data.order.printers.printer_size == 0 ? '50%' : '100%'
        }"
      >
        <span>{{ langId == 1 ? "پۇل يىغقۇچى : " : "收银员 : " }}</span>
        <span>{{ addSpacesAroundNumbers(data.order.cashier_name) }}</span>
      </div>
    </div>
    <div class="item-line">
      <div class="line-text">
        {{ langId == 1 ? "زاكاز نومۇرى : " : "订单号 : " }}
      </div>
      <div class="line-text">{{ data.order.order_no }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? "ۋاقتى : " : "时间 : " }}</div>
      <div class="line-text time">{{ time }}</div>
    </div>
    <div class="line"></div>
    <div class="item-line">
      <div class="flex">{{ langId == 1 ? "تاماق" : "美食" }}</div>
      <div
        class="fixed"
        :class="langId == 1 ? 'text-left' : 'text-right'"
        :style="{
          width: data.order.printers.printer_size == 0 ? '90px' : '70px'
        }"
      >
        {{ langId == 1 ? "باھاسى" : "单价" }}
      </div>
      <div
        class="fixed"
        :class="langId == 1 ? 'text-left' : 'text-right'"
        :style="{
          width: data.order.printers.printer_size == 0 ? '80px' : '60px'
        }"
      >
        {{ langId == 1 ? "سانى" : "数量" }}
      </div>
      <div
        class="fixed"
        :class="langId == 1 ? 'text-left' : 'text-right'"
        :style="{
          width: data.order.printers.printer_size == 0 ? '90px' : '70px'
        }"
      >
        {{ langId == 1 ? "جەمئىي" : "总计" }}
      </div>
    </div>
    <div class="food-list">
      <div
        class="food-item"
        v-for="item in data.order.order_details"
        :key="item.id"
      >
        <div class="item-line vertical-center">
          <div class="food-name-box flex">
            <div
              class="food-name food-name-ug rtl"
              :style="{ textAlign: langId == 1 ? 'right' : 'left' }"
            >
              {{ addSpacesAroundNumbers(item.food_name_ug) }}
            </div>
            <div
              class="food-name ltr"
              :style="{ textAlign: langId == 1 ? 'right' : 'left' }"
            >
              {{ addSpacesAroundNumbers(item.food_name_zh) }}
            </div>
          </div>
          <div
            class="fixed ltr"
            :class="langId == 1 ? 'text-left' : 'text-right'"
            :style="{
              width: data.order.printers.printer_size == 0 ? '90px' : '70px'
            }"
          >
            {{ formatPrice(item.price) }}
          </div>
          <div
            class="fixed ltr"
            :class="langId == 1 ? 'text-left' : 'text-right'"
            :style="{
              width: data.order.printers.printer_size == 0 ? '80px' : '60px'
            }"
          >
            {{ parseFloat(item.foods_count) }}
          </div>
          <div
            class="fixed ltr"
            :class="langId == 1 ? 'text-left' : 'text-right'"
            :style="{
              width: data.order.printers.printer_size == 0 ? '90px' : '70px'
            }"
          >
            {{ formatPrice(item.foods_count * item.price) }}
          </div>
        </div>
      </div>
    </div>
    <div class="line"></div>
    <div class="item-line between">
      <div>{{ langId == 1 ? "ئالغان پۇل (已收现金) : " : "已收现金 : " }}</div>
      <div class="ltr">
        {{
          formatPrice(
            data.order.payment_type_id == 4
              ? data.order.order_vip_price
              : data.order.collected_amount
          )
        }}
      </div>
    </div>
    <div class="item-line between">
      <div>{{ langId == 1 ? "قايتۇرغان پۇل (找零) : " : "找零 : " }}</div>
      <div class="ltr">{{ formatPrice(data.order.give_change) }}</div>
    </div>
    <div class="item-line between">
      <div>{{ langId == 1 ? "كېمەيتىلگەن سوممىسى : " : "抹零金额 : " }}</div>
      <div class="ltr">{{ formatPrice(data.order.ignore_price) }}</div>
    </div>
    <div class="item-line between">
      <div>
        {{ langId == 1 ? "ئومۇمىي سوممىسى (现金) : " : "总价格 (现金) : " }}
      </div>
      <div class="ltr">{{ formatPrice(data.order.order_price) }}</div>
    </div>
    <div class="line"></div>
    <div>
      <span class="line-text fixed">
        {{ langId == 1 ? "ئادرېس : " : "地址 : " }}
      </span>
      <span class="line-text flex line-height">
        {{
          langId == 1
            ? addSpacesAroundNumbers(data.order.merchant.address_ug)
            : addSpacesAroundNumbers(data.order.merchant.address_zh)
        }}
      </span>
    </div>
    <div class="item-line">
      <div class="line-text">
        {{ langId == 1 ? "ئالاقىلىشىش نومۇرى : " : "联系号码 : " }}
      </div>
      <div class="line-text ltr">{{ data.order.merchant.phone }}</div>
    </div>
    <div class="line"></div>
    <div class="company-name">
      {{
        langId == 1
          ? "شىنجاڭ ئالماس يۇمشاق دېتال چەكلىك شىركىتى"
          : "新疆金钻软件有限公司"
      }}
    </div>
    <div class="company-name">************</div>
  </div>
</template>

<script>
import moment from "moment";
import print from "./../print.vue";
export default {
  data() {
    return {
      data: null,
      time: "",
      langId: 1
    };
  },
  methods: {
    print(json) {
      this.data = json;
      this.time = moment().format("YYYY-MM-DD HH:mm:ss");
      this.langId = localStorage.getItem("langId");
      console.log("打印开始 --- 1", this.data);
      this.$nextTick(() => {
        print.saveImage(this.$refs.container, json);
        this.data = null;
      });
    },
    formatPrice(num) {
      const number = parseFloat(num);
      const formatter = new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      const formatted = formatter.format(number);
      if (formatted.includes(".")) {
        return formatted;
      } else {
        return formatted.replace(".00", "");
      }
    },
    addSpacesAroundNumbers(str) {
      // 定义正则表达式，用于匹配字符串中的数字
      const regex = /(\w)/g;

      // 使用replace方法，并通过回调函数在匹配到的数字前后添加空格
      // \u200B是Unicode中的空格字符，用于在数字前后添加不占位置的空格
      const result = str.replace(regex, '\u200B$1\u200B');

      // 返回处理后的字符串
      return result;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 12px;
  padding-bottom: 30px;
  padding-top: 20px;
  .report-title {
    font-weight: bold;
    text-align: center;
    line-height: 1.2;
  }
  .company-name {
    text-align: center;
    font-size: 32px;
  }
  .fixed {
    flex-shrink: 0;
    flex-grow: 0;
  }
  .flex {
    flex-grow: 1;
    flex-shrink: 1;
  }
  .item-line {
    display: flex;
    row-gap: 12px;
    &.between {
      justify-content: space-between;
    }
    &.vertical-center {
      align-items: center;
    }
  }
  .food-list {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    .food-name-box {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      .food-name-ug {
        font-size: 42px;
      }
    }
  }
  .line {
    width: 100%;
    height: 2px;
    background-color: #000;
  }
  .time {
    direction: ltr;
  }
  .rtl {
    direction: rtl;
  }
  .ltr {
    direction: ltr;
  }
  .line-height {
    line-height: 1.2;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
}
</style>
