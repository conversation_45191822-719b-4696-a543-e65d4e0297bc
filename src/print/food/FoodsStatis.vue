<template>
  <div
    v-if="data && data.action == '美食统计'"
    ref="container"
    class="container"
    :style="{
      width: data.order.printers.printer_size == 0 ? '560px' : '384px',
      direction: langId == 1 ? 'rtl' : 'ltr',
      fontSize: data.order.printers.printer_size == 0 ? '28px' : '26px'
    }"
  >
    <div
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'
      }"
    >
      {{
        langId == 1 ? addSpacesAroundNumbers(data.order.merchant.name_ug) : addSpacesAroundNumbers(data.order.merchant.name_zh)
      }}
    </div>
    <div
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'
      }"
    >
      {{ langId == 1 ? "تاماق سېتىلىش ئەھۋالى" : "美食统计" }}
    </div>
    <div class="line"></div>
    <div
      class="item-line between"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '28px' : '22px'
      }"
    >
      <div class="line-text">
        {{ langId == 1 ? "باشلىنىش ۋاقتى" : "开始时间" }}
      </div>
      <div class="line-text time">{{ data.order.begin_date }}</div>
    </div>
    <div
      class="item-line between"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '28px' : '22px'
      }"
    >
      <div class="line-text">
        {{ langId == 1 ? "ئاخىرلىشىش ۋاقتى" : "结束时间" }}
      </div>
      <div class="line-text time">{{ data.order.end_date }}</div>
    </div>
    <div
      class="item-line between"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '28px' : '22px'
      }"
    >
      <div class="line-text">
        {{ langId == 1 ? "بېسىلغان ۋاقتى" : "打印日期" }}
      </div>
      <div class="line-text time">{{ time }}</div>
    </div>
    <div class="line"></div>
    <div class="region-title text-center">
      {{ langId == 1 ? "تاماق تۈرى سېتىلىشى" : "美食分类营业情况" }}
    </div>
    <div
      class="item-line between"
      v-for="category in data.order.food_category_proportion"
      :key="category.food_category_id"
    >
      <div class="line-text">
        {{ addSpacesAroundNumbers(category.food_category_name) }}
      </div>
      <div class="line-text ltr">
        {{ formatPrice(category.amount) }}
      </div>
    </div>
    <div class="line"></div>
    <div class="region-title text-center">
      {{ langId == 1 ? "تاماق سېتىلىشى" : "美食营业情况" }}
    </div>
    <div class="item-line">
      <div class="flex">{{ langId == 1 ? "تاماق" : "美食" }}</div>
      <div
        class="fixed"
        :class="langId == 1 ? 'text-left' : 'text-right'"
        :style="{
          width: data.order.printers.printer_size == 0 ? '136px' : '110px'
        }"
      >
        {{ langId == 1 ? "جەمئىي" : "总计" }}
      </div>
      <div
        class="fixed"
        :class="langId == 1 ? 'text-left' : 'text-right'"
        :style="{
          width: data.order.printers.printer_size == 0 ? '120px' : '100px'
        }"
      >
        {{ langId == 1 ? "باھاسى" : "单价" }}
      </div>
      <div
        class="fixed"
        :class="langId == 1 ? 'text-left' : 'text-right'"
        :style="{
          width: data.order.printers.printer_size == 0 ? '100px' : '80px'
        }"
      >
        {{ langId == 1 ? "سانى" : "数量" }}
      </div>
    </div>
    <div class="food-list">
      <div
        class="food-item"
        v-for="item in data.order.list"
        :key="item.category_id"
        v-if="item.foods_list.length > 1"
      >
        <div
          v-for="food in item.foods_list"
          :key="food.food_id"
          v-if="food.food_id != 0"
        >
          <div class="food-name flex">
            {{ food.name }}
          </div>
          <div class="food-info">
            <div
              class="fixed ltr"
              :class="langId == 1 ? 'text-left' : 'text-right'"
              :style="{
                width: data.order.printers.printer_size == 0 ? '136px' : '110px'
              }"
            >
              {{ formatPrice(food.real_amount) }}
            </div>
            <div
              class="fixed ltr"
              :class="langId == 1 ? 'text-left' : 'text-right'"
              :style="{
                width: data.order.printers.printer_size == 0 ? '120px' : '100px'
              }"
            >
              {{ formatPrice(food.price) }}
            </div>
            <div
              class="fixed ltr"
              :class="langId == 1 ? 'text-left' : 'text-right'"
              :style="{
                width: data.order.printers.printer_size == 0 ? '100px' : '80px'
              }"
            >
              {{ parseFloat(food.count) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import print from "./../print.vue";
export default {
  data() {
    return {
      data: null,
      time: "",
      langId: 1
    };
  },
  methods: {
    print(json) {
      this.data = json;
      this.langId = localStorage.getItem("langId");
      this.time = moment().format("YYYY-MM-DD HH:mm:ss");
      console.log("打印开始 --- 1", this.data);
      this.$nextTick(() => {
        print.saveImage(this.$refs.container, json);
        this.data = null;
      });
    },
    formatPrice(num) {
      const number = parseFloat(num);
      const formatter = new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      const formatted = formatter.format(number);
      if (formatted.includes(".")) {
        return formatted;
      } else {
        return formatted.replace(".00", "");
      }
    },
    addSpacesAroundNumbers(str) {
      // 定义正则表达式，用于匹配字符串中的数字
      const regex = /(\w)/g;

      // 使用replace方法，并通过回调函数在匹配到的数字前后添加空格
      // \u200B是Unicode中的空格字符，用于在数字前后添加不占位置的空格
      const result = str.replace(regex, '\u200B$1\u200B');

      // 返回处理后的字符串
      return result;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  padding-bottom: 30px;
  padding-top: 20px;
  line-height: 1.2;
  .report-title {
    font-weight: bold;
    text-align: center;
  }
  .item-line {
    display: flex;
    .half-line {
      width: 50%;
    }
    &.between {
      justify-content: space-between;
    }
    &.vertical-center {
      align-items: center;
    }
  }
  .food-list {
    display: flex;
    flex-direction: column;
    .food-name-box {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      .food-name-ug {
        font-size: 42px;
      }
    }
    .food-info {
      display: flex;
      justify-content: flex-end;
    }
  }
  .fixed {
    flex-shrink: 0;
    flex-grow: 0;
  }
  .flex {
    flex-grow: 1;
    flex-shrink: 1;
  }
  .time {
    direction: ltr;
  }
  .rtl {
    direction: rtl;
  }
  .ltr {
    direction: ltr;
  }
  .line {
    width: 100%;
    height: 2px;
    background-color: #000;
  }
  .line-2 {
    height: 60px;
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
}
</style>
