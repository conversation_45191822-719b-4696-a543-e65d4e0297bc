<template>
  <div
    v-if="data && data.action == '营业统计'"
    ref="container"
    class="container"
    :style="{
      width: data.order.printers.printer_size == 0 ? '560px' : '384px',
      direction: langId == 1 ? 'rtl' : 'ltr',
      fontSize: data.order.printers.printer_size == 0 ? '28px' : '26px'
    }"
  >
    <div
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'
      }"
    >
      {{
        langId == 1 ? addSpacesAroundNumbers(data.order.merchant.name_ug) : addSpacesAroundNumbers(data.order.merchant.name_zh)
      }}
    </div>
    <div
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'
      }"
    >
      {{ langId == 1 ? "تىجارەت ئەھۋالى" : "营业统计" }}
    </div>
    <div class="line"></div>
    <div class="item-line between" :style="{ fontSize: data.order.printers.printer_size == 0 ? '28px' : '22px' }">
      <div class="line-text">
        {{ langId == 1 ? "باشلىنىش ۋاقتى" : "开始时间" }}
      </div>
      <div class="line-text time">{{ data.order.begin_date }}</div>
    </div>
    <div class="item-line between" :style="{ fontSize: data.order.printers.printer_size == 0 ? '28px' : '22px' }">
      <div class="line-text">
        {{ langId == 1 ? "ئاخىرلىشىش ۋاقتى" : "结束时间" }}
      </div>
      <div class="line-text time">{{ data.order.end_date }}</div>
    </div>
    <div class="item-line between" :style="{ fontSize: data.order.printers.printer_size == 0 ? '28px' : '22px' }">
      <div class="line-text">
        {{ langId == 1 ? "بېسىلغان ۋاقتى" : "打印日期" }}
      </div>
      <div class="line-text time">{{ time }}</div>
    </div>
    <div class="line"></div>
    <div class="region-title text-center">{{ langId == 1 ? "تىجارەت ئەھۋالى" : "营业情况" }}</div>
    <div class="item-line between" v-for="report in data.order.headCashInfo" :key="report.id">
      <div class="line-text">
        {{ addSpacesAroundNumbers(report.name) }}
      </div>
      <div class="line-text ltr">
        {{ formatPrice(report.value) }}
      </div>
    </div>
    <div class="line"></div>
    <div class="region-title text-center">{{ langId == 1 ? "ئالغان پۇل تەپسىلاتى" : "实收金额详情" }}</div>
    <div class="item-line between" v-for="price in data.order.order_proportion">
      <div class="line-text">
        {{ addSpacesAroundNumbers(price.payment_type_name) }}
      </div>
      <div class="line-text ltr">{{ formatPrice(price.amount) }}</div>
    </div>
    <div class="line"></div>
    <div class="region-title text-center">{{ langId == 1 ? "زاكاز ئەھۋالى" : "订单详情" }}</div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "ئومۇمىي خېرىدار سانى" : "客户总数" }}
      </div>
      <div class="line-text ltr">{{ data.order.orderStatisticsInfo.customers_count }}</div>
    </div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "ئومۇمىي زاكاز سانى" : "订单总数" }}
      </div>
      <div class="line-text ltr">{{ data.order.orderStatisticsInfo.order_count }}</div>
    </div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "ئوتتۇرچە زاكاز سوممىسى" : "单均消费" }}
      </div>
      <div class="line-text ltr">{{ formatPrice(data.order.orderStatisticsInfo.order_avg) }}</div>
    </div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "ئوتتۇرچە خېرىدار سوممىسى" : "人均消费" }}
      </div>
      <div class="line-text ltr">{{ formatPrice(data.order.orderStatisticsInfo.customers_avg) }}</div>
    </div>
    <div class="line"></div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "يېڭى قوشۇلغان ئەزا سانى" : "新增会员数量" }}
      </div>
      <div class="line-text ltr">{{ data.order.vip.vip_count }}</div>
    </div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "ئومومىي قاچىلانغان پۇل" : "充值总金额" }}
      </div>
      <div class="line-text ltr">{{ formatPrice(data.order.vip.vip_recharge_amount) }}</div>
    </div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "سوۋغات قىلىنغان پۇل" : "赠送金额" }}
      </div>
      <div class="line-text ltr">{{ formatPrice(data.order.vip.vip_recharge_present_amount) }}</div>
    </div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "نەق پۇل" : "现金" }}
      </div>
      <div class="line-text ltr">{{ formatPrice(data.order.vip.vip_total_Balance) }}</div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import print from "./../print.vue";
export default {
  data() {
    return {
      data: null,
      time: "",
      langId: 1
    };
  },
  methods: {
    print(json) {
      this.data = json;
      this.langId = localStorage.getItem("langId");
      this.time = moment().format("YYYY-MM-DD HH:mm:ss");
      console.log("打印开始 --- 1", this.data);
      this.$nextTick(() => {
        print.saveImage(this.$refs.container, json);
        this.data = null;
      });
    },
    formatPrice(num) {
      const number = parseFloat(num);
      const formatter = new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      const formatted = formatter.format(number);
      if (formatted.includes(".")) {
        return formatted;
      } else {
        return formatted.replace(".00", "");
      }
    },
    addSpacesAroundNumbers(str) {
      // 定义正则表达式，用于匹配字符串中的数字
      const regex = /(\w)/g;

      // 使用replace方法，并通过回调函数在匹配到的数字前后添加空格
      // \u200B是Unicode中的空格字符，用于在数字前后添加不占位置的空格
      const result = str.replace(regex, '\u200B$1\u200B');

      // 返回处理后的字符串
      return result;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  padding-bottom: 30px;
  padding-top: 20px;
  line-height: 1.2;
  .report-title {
    font-weight: bold;
    text-align: center;
  }
  .item-line {
    display: flex;
    .half-line {
      width: 50%;
    }
    &.between {
      justify-content: space-between;
    }
  }
  .time {
    direction: ltr;
  }
  .ltr {
    direction: ltr;
  }
  .line {
    width: 100%;
    height: 2px;
    background-color: #000;
  }
  .line-2 {
    height: 60px;
  }
  .text-center {
    text-align: center;
  }
}
</style>
