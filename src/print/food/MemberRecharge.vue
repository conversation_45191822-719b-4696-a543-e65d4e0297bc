<template>
  <div v-if="data && data.action == '会员充值'" ref="container" class="container" :style="{width: data.order.printers.printer_size == 0 ? '560px' : '384px', direction: langId == 1 ? 'rtl' : 'ltr', fontSize: data.order.printers.printer_size == 0 ? '28px' : '26px'}">
    <div class="report-title" :style="{fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'}">{{ langId == 1 ? 'ئەزا پۇل قاچىلاش تالونى' : '会员充值订单' }}</div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'تېلىفون نومۇرى : ' : '电话号码 : ' }}</div>
      <div class="line-text">{{ data.order.mobile }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'ئەزا نامى : ' : '会员名称 : ' }}</div>
      <div class="line-text">{{ addSpacesAroundNumbers(data.order.name) }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'پۇل قاچىلانغان ۋاقت : ' : '充值日期 : ' }}</div>
      <div class="line-text time">{{ time }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'پۇل تۆلەش شەكلى : ' : '支付方式 : ' }}</div>
      <div class="line-text">{{ addSpacesAroundNumbers(data.order.payment_type) }}</div>
    </div>
    <div class="line"></div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'قاچىلاش سوممىسى : ' : '充值金融 : ' }}</div>
      <div class="line-text ltr">{{ formatPrice(data.order.recharge_amuunt) }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'سوۋغات سوممىسى : ' : '赠送金额 : ' }}</div>
      <div class="line-text ltr">{{ formatPrice(data.order.present_amount) }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'قالدۇق سوممىسى : ' : '余额 : ' }}</div>
      <div class="line-text ltr">{{ formatPrice(data.order.balance) }}</div>
    </div>
    <div class="line-2"></div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'ئەزا ئىمزاسى : ' : '客户签字 : ' }}</div>
      <div class="line-text">_________________</div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import print from "./../print.vue"
export default {
  data() {
    return {
      data: null,
      time: "",
      langId: 1
    };
  },
  methods: {
    print(json) {
      this.data = json;
      this.langId = localStorage.getItem("langId");
      const timeFormat = this.data.order.printers.printer_size == 1 ? "HH:mm:ss" : "YYYY-MM-DD HH:mm:ss";
      this.time = moment().format(timeFormat);
      console.log("打印开始 --- 1", this.data);
      this.$nextTick(() => {
        print.saveImage(this.$refs.container, json);
        this.data = null;
      })
    },
    formatPrice(num) {
      const number = parseFloat(num);
      const formatter = new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      const formatted = formatter.format(number);
      if (formatted.includes(".")) {
        return formatted;
      } else {
        return formatted.replace(".00", "");
      }
    },
    addSpacesAroundNumbers(str) {
      // 定义正则表达式，用于匹配字符串中的数字
      const regex = /(\w)/g;

      // 使用replace方法，并通过回调函数在匹配到的数字前后添加空格
      // \u200B是Unicode中的空格字符，用于在数字前后添加不占位置的空格
      const result = str.replace(regex, '\u200B$1\u200B');

      // 返回处理后的字符串
      return result;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  padding-bottom: 30px;
  padding-top: 20px;
  .report-title {
    font-weight: bold;
    text-align: center;
  }
  .item-line {
    display: flex;
    .half-line {
      width: 50%;
    }
  }
  .time {
    direction: ltr;
  }
  .ltr {
    direction: ltr;
  }
  .line {
    width: 100%;
    height: 2px;
    background-color: #000;
  }
  .line-2 {
    height: 60px;
  }
}
</style>
