<template>
  <div
    v-if="data && data.action == '换班'"
    ref="container"
    class="container"
    :style="{
      width: data.order.printers.printer_size == 0 ? '560px' : '384px',
      direction: langId == 1 ? 'rtl' : 'ltr',
      fontSize: data.order.printers.printer_size == 0 ? '28px' : '26px'
    }"
  >
    <div
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'
      }"
    >
      {{
        langId == 1 ? addSpacesAroundNumbers(data.order.merchant.name_ug) : addSpacesAroundNumbers(data.order.merchant.name_zh)
      }}
    </div>
    <div
      class="report-title"
      :style="{
        fontSize: data.order.printers.printer_size == 0 ? '48px' : '42px'
      }"
    >
      {{ langId == 1 ? "ئىسمېنا خاتىرىسى" : "班次统计" }}
    </div>
    <div class="line"></div>
    <div class="item-line">
      <div class="line-text">
        {{ langId == 1 ? "باشلىنىش ۋاقتى : " : "开始时间 : " }}
      </div>
      <div class="line-text time">{{ data.order.start_at }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">
        {{ langId == 1 ? "ئاخىرلىشىش ۋاقتى : " : "结束时间 : " }}
      </div>
      <div class="line-text time">{{ data.order.leave_at }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">
        {{ langId == 1 ? "بېسىلغان ۋاقتى : " : "打印日期 : " }}
      </div>
      <div class="line-text time">{{ time }}</div>
    </div>
    <div class="line"></div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "زاپاس پۇل : " : "备用金 : " }}
      </div>
      <div class="line-text ltr">
        {{ formatPrice(data.order.alternate_amount) }}
      </div>
    </div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "ئالدىنقى ئىسمېنا سوممىسى : " : "前班余额 : " }}
      </div>
      <div class="line-text ltr">{{ formatPrice(data.order.working_balance) }}</div>
    </div>
    <div class="line"></div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "ئومومىي سودا : " : "营业总金额 : " }}
      </div>
      <div class="line-text ltr">{{ formatPrice(data.order.paid_amount) }}</div>
    </div>
    <div
      class="item-line between"
      v-for="item in data.order.paymentProportion"
      :key="item.id"
    >
      <div class="line-text">{{ addSpacesAroundNumbers(item.payment_type) }} : </div>
      <div class="line-text ltr">{{ formatPrice(item.total) }}</div>
    </div>
    <div class="line"></div>
    <div class="item-line between">
      <div class="line-text">
        {{ langId == 1 ? "ئۆتكۈزگەن پۇل : " : "交班金额 : " }}
      </div>
      <div class="line-text ltr">{{ formatPrice(data.order.submitted_amount) }}</div>
    </div>
    <div class="line-2"></div>
    <div class="item-line" :style="{ fontSize: data.order.printers.printer_size == 0 ? '28px' : '20px' }">
      <div class="line-text half-line text-center">
        <div>_______________</div>
        <div class="line-text">
          {{ langId == 1 ? "ئىسمېنا ئۆتكۈزگۈچى" : "交班人" }}
        </div>
      </div>
      <div class="line-text half-line text-center">
        <div>_______________</div>
        <div class="line-text">
          {{ langId == 1 ? "ئىسمېنا ئۆتكۈزىۋالغۇچى" : "接班人" }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import print from "./../print.vue";
export default {
  data() {
    return {
      data: null,
      time: "",
      langId: 1
    };
  },
  methods: {
    print(json) {
      this.data = json;
      this.langId = localStorage.getItem("langId");
      const timeFormat = this.data.order.printers.printer_size == 1 ? "MM-DD HH:mm:ss" : "YYYY-MM-DD HH:mm:ss";
      this.time = moment().format(timeFormat);
      console.log("打印开始 --- 1", this.data);
      this.$nextTick(() => {
        print.saveImage(this.$refs.container, json);
        this.data = null;
      });
    },
    formatPrice(num) {
      const number = parseFloat(num);
      const formatter = new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      const formatted = formatter.format(number);
      if (formatted.includes(".")) {
        return formatted;
      } else {
        return formatted.replace(".00", "");
      }
    },
    addSpacesAroundNumbers(str) {
      // 定义正则表达式，用于匹配字符串中的数字
      const regex = /(\w)/g;

      // 使用replace方法，并通过回调函数在匹配到的数字前后添加空格
      // \u200B是Unicode中的空格字符，用于在数字前后添加不占位置的空格
      const result = str.replace(regex, '\u200B$1\u200B');

      // 返回处理后的字符串
      return result;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  padding-bottom: 30px;
  padding-top: 20px;
  line-height: 1.2;
  .report-title {
    font-weight: bold;
    text-align: center;
  }
  .item-line {
    display: flex;
    .half-line {
      width: 50%;
    }
    &.between {
      justify-content: space-between;
    }
  }
  .time {
    direction: ltr;
  }
  .ltr {
    direction: ltr;
  }
  .line {
    width: 100%;
    height: 2px;
    background-color: #000;
  }
  .line-2 {
    height: 60px;
  }
  .text-center {
    text-align: center;
  }
}
</style>
