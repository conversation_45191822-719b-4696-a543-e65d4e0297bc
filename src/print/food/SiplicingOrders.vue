<template>
  <div v-if="data && data.action == '拼单'" ref="container" class="container" :style="{width: data.order.printers[0].printer_size == 0 ? '560px' : '384px', direction: langId == 1 ? 'rtl' : 'ltr', fontSize: data.order.printers[0].printer_size == 0 ? '28px' : '26px'}">
    <div class="report-title" :style="{fontSize: data.order.printers[0].printer_size == 0 ? '48px' : '42px'}">{{ langId == 1 ? 'زاكاز بىرلەشتۈرۈش' : '订单拼单' }}</div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'زاكاز بىرلەشتۈرگۈچى : ' : '拼单人 : ' }}</div>
      <div class="line-text">{{ data.order.operator }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'ئەسلى ئۈستەل : ' : '原台 : ' }}</div>
      <div class="line-text">{{ langId == 1 ? addSpacesAroundNumbers(data.order.origin_table_name_ug) : addSpacesAroundNumbers(data.order.origin_table_name_zh) }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'زاكاز نومۇرى : ' : '订单号 : ' }}</div>
      <div class="line-text">{{ data.order.origin_order_no }}</div>
    </div>
    <div class="item-line">
      <div class="line-text half-line">
        <span>{{ langId == 1 ? 'ئادەم سانى : ' : '人数 : ' }}</span>
        <span class="ltr">{{ data.order.customers_count }}</span>
      </div>
      <div class="line-text half-line">
        <span>{{ langId == 1 ? 'ۋاقتى : ' : '时间 : ' }}</span>
        <span class="ltr">{{ time }}</span>
      </div>
    </div>
    <div class="line"></div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'يۆتكەلگەن ئۈستەل : ' : '目标台 : ' }}</div>
      <div class="line-text">{{ langId == 1 ? addSpacesAroundNumbers(data.order.target_table_name_ug) : addSpacesAroundNumbers(data.order.target_table_name_zh) }}</div>
    </div>
    <div class="item-line">
      <div class="line-text">{{ langId == 1 ? 'بىرلەشكەن زاكاز : ' : '目标订单 : ' }}</div>
      <div class="line-text">{{ data.order.target_order_no }}</div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import print from "./../print.vue"
export default {
  data() {
    return {
      data: null,
      time: "",
      langId: 1
    };
  },
  methods: {
    print(json) {
      this.data = json;
      this.time = moment().format("HH:mm:ss");
      this.langId = localStorage.getItem("langId");
      console.log("打印开始 --- 1", this.data);
      this.$nextTick(() => {
        print.saveImage(this.$refs.container, json);
        this.data = null;
      })
    },
    addSpacesAroundNumbers(str) {
      // 定义正则表达式，用于匹配字符串中的数字
      const regex = /(\w)/g;

      // 使用replace方法，并通过回调函数在匹配到的数字前后添加空格
      // \u200B是Unicode中的空格字符，用于在数字前后添加不占位置的空格
      const result = str.replace(regex, '\u200B$1\u200B');

      // 返回处理后的字符串
      return result;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  padding-bottom: 30px;
  padding-top: 20px;
  .report-title {
    font-weight: bold;
    text-align: center;
  }
  .item-line {
    display: flex;
    .half-line {
      width: 50%;
    }
  }
  .ltr {
    direction: ltr;
  }
  .line {
    width: 100%;
    height: 2px;
    background-color: #000;
  }
}
</style>
