<script>
import html2canvas from "html2canvas";
import getPixels from "get-pixels"
import slsLog from "./SlsLog.vue";


export default {
  //图片格式转换方法
  dataURLToBlob(dataurl) {
    let arr = dataurl.split(',');
    let mime = arr[0].match(/:(.*?);/)[1];
    let bstr = atob(arr[1]);
    let n = bstr.length;
    let u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], {type: mime});
  },
  //点击方法
  saveImage(divText, json) {

    return new Promise((resolve, reject)=>{
      if (json.order.printers.beautiful_print === 0) {
        console.log("文字打印",json)
        const langId = localStorage.getItem("langId") || 1;
        if (Array.isArray(json.order.printers)) {
          json.order.printers[0].lang = langId;
        } else {
          if (!json.order.printers.custom) {
            json.order.printers.lang = langId;
          }
        }
        window.electronAPI.printTask({image:null,json:json,try:1,printers:json.order.printers})
        resolve()
        return
      }
      // let canvasID = this.$refs[divText];
      let canvasID = divText;
      let that = this;
      // let a = document.createElement('a');
      html2canvas(canvasID,{scale:1}).then(canvas => {
        let dom = document.body.appendChild(canvas);
        dom.style.display = 'none';
        document.body.removeChild(dom);
        that.imagePixel(dom.toDataURL('image/png'),json).then(()=>{
          resolve()
        })
        return
        // a.style.display = 'none';
        // document.body.removeChild(dom);
        // let blob = that.dataURLToBlob(dom.toDataURL('image/png'));
        // let imgData=canvas.getContext("2d").getImageData(0,0,dom.width,dom.height);
        // a.setAttribute('href', URL.createObjectURL(blob));
        // //这块是保存图片操作  可以设置保存的图片的信息
        // a.setAttribute('download', imgText + '.png');
        // document.body.appendChild(a);
        // a.click();
        // that.downloadFileToFolder(blob,'test','png')
        // URL.revokeObjectURL(blob);
        // document.body.removeChild(a);

      });
    })
  },
  imagePixel(base64,json){
    return new Promise((resolve,reject)=>{
      getPixels(base64, 'image/png', function(err, pixels) {
        if (err) {
          slsLog.send(err,"生成图片失败","warn")
          console.error('Error getting pixels:', err);
        }else {
          // pixels 现在是 get-pixels 库的对象，可以进行进一步处理
          console.log('Got pixels:', pixels);
          resolve()
          window.electronAPI && window.electronAPI.printTask && window.electronAPI.printTask({image:pixels,json:json,try:1,printers:Array.isArray(json.order.printers) ? json.order.printers[0] : json.order.printers })
        }
      });
    })
  },
};
</script>
