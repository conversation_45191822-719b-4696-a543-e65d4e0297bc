<template>
  <div
    class="print-control-view"
    style="position: absolute;z-index: 1000000;background: white;margin-left: 10000px;display: flex;flex-direction: row"
  >
    <FoodView ref="foodView" />
    <SiplicingOrders ref="siplicingOrders" />
    <ExchangeOrder ref="exchhangeOrder" />
    <PreCheckout ref="preCheckout" />
    <AlreadyCheckout ref="alreadyCheckout" />
    <MemberRecharge ref="memberRecharge" />
    <ShiftChange ref="shiftChange" />
    <BusinessStatis ref="businessStatis" />
    <FoodsStatis ref="foodsStatis" />
    <Test ref="test" />

    <FoodText ref="foodText"></FoodText>
    <ExchangeOrderText ref="exchangeOrderText" />
    <SiplicingOrdersText ref="siplicingOrdersText" />
    <AlreadyCheckoutText ref="alreadyCheckoutText" />
    <PreCheckoutText ref="preCheckoutText" />
    <MemberRechargeText ref="memberRechargeText" />
    <BusinessStatisText ref="businessStatisText" />
    <FoodsStatisText ref="foodsStatisText" />
    <ShiftChangeText ref="shiftChangeText" />


  </div>
</template>

<script>
import FoodView from "./food/FoodView.vue";
import SiplicingOrders from "./food/SiplicingOrders.vue";
import ExchangeOrder from "./food/ExchangeOrder.vue";
import PreCheckout from "./food/PreCheckout.vue";
import AlreadyCheckout from "./food/AlreadyCheckout.vue";
import MemberRecharge from "./food/MemberRecharge.vue";
import ShiftChange from "./food/ShiftChange.vue";
import BusinessStatis from "./food/BusinessStatis.vue";
import FoodsStatis from "./food/FoodsStatis.vue";
import queqeIndexedDB from "./QueqeIndexedDB.vue";
import Test from "./food/Test.vue";
import FoodText from "./text/template/FoodText.vue";
import ExchangeOrderText from "./text/template/ExchangeOrderText.vue";
import SiplicingOrdersText from "./text/template/SiplicingOrdersText.vue";
import AlreadyCheckoutText from "./text/template/AlreadyCheckoutText.vue";
import PreCheckoutText from "./text/template/PreCheckoutText.vue";
import MemberRechargeText from "./text/template/MemberRechargeText.vue";
import BusinessStatisText from "./text/template/BusinessStatisText.vue";
import FoodsStatisText from "./text/template/FoodsStatisText.vue";
import ShiftChangeText from "./text/template/ShiftChangeText.vue";

export default {
  components: {
    ShiftChangeText,
    FoodsStatisText,
    BusinessStatisText,
    MemberRechargeText,
    PreCheckoutText,
    AlreadyCheckoutText,
    SiplicingOrdersText,
    ExchangeOrderText,
    FoodText,
    Test,
    FoodView,
    SiplicingOrders,
    ExchangeOrder,
    PreCheckout,
    AlreadyCheckout,
    MemberRecharge,
    ShiftChange,
    BusinessStatis,
    FoodsStatis
  },
  data() {
    return {
      loopTime: 30000,
      taskTimer: null
    };
  },
  mounted() {
    this.$bus.$on("customPrint", this.print);
  },
  async created() {
    window.electronAPI &&
      window.electronAPI.printFailed &&
      window.electronAPI.printFailed((ecent, type, res) => {
        console.log("打印失败", type, res);
        queqeIndexedDB.addPrintTask(type);
      });

    const appsData =
      window.electronAPI &&
      window.electronAPI.getApps &&
      (await window.electronAPI.getApps());
    if (appsData) {
      this.taskTimer = setInterval(this.filedTaskTry, this.loopTime);
    }
  },
  destroyed() {
    clearInterval(this.taskTimer);
    this.taskTimer = null;
    console.log("销毁了");
  },
  methods: {
    filedTaskTry() {
      queqeIndexedDB.getAllPrintTask().then(async res => {
        console.log("扫描并获取打印任务", res);
        for (const item of res) {
          await this.print(item);
        }
      });
    },
    async print(json) {
      const disablePrint = localStorage.getItem("disablePrint");
      console.log("disablePrint: ", disablePrint);
      if (disablePrint === "true") return;
      console.log("打印开始", json);
      if (json) {
        switch (json.action) {
          case "提交订单":
          case "加菜":
          case "退菜":
          case "全单退菜":
          case "催单":
            if (json.order.order_details && json.order.order_details.length) {
              const printArray = [];    // 保存要打印的全部订单（美食打印）
              const printObj = {};      // 保存要打印的全部订单（订单打印）
              for (let i = 0; i < json.order.order_details.length; i++) {
                const food = json.order.order_details[i];   // 获取当前订单
                if (!food.many_printers || !food.many_printers.length) continue;

                for (let j = 0; j < food.many_printers.length; j++) {
                  const printer = food.many_printers[j];    // 获取当前打印机
                  // 云打印不打印
                  if (printer.internet_type == 1) continue;

                  if (printer.split_print == 1) {
                    const data = {
                      ...json,
                      order: {
                        ...json.order,
                        printers: { ...printer },
                        order_details: [{...food, many_printers: [printer]}],
                      },
                    };
                    if (data.action == "全单退菜") {
                      if (food.canceled_foods.length) {
                        printArray.push(data);
                      }
                    } else {
                      printArray.push(data);
                    }
                  } else {
                    if (printObj[printer.printer_ip]) {
                      printObj[printer.printer_ip].order.order_details.push(food);
                    } else {
                      printObj[printer.printer_ip] = {
                        ...json,
                        order: {
                          ...json.order,
                          printers: { ...printer },
                          order_details: [{...food, many_printers: [printer]}],
                        },
                      };
                    }
                  }
                }
              }

              // 打印美食打印
              for (let i = 0; i < printArray.length; i++) {
                // await this.$refs.foodView.print(printArray[i]);
                await this.printFuc("foodView","foodText",printArray[i])
              }

              // 打印订单打印
              for (const item in printObj) {
                // await this.$refs.foodView.print(printObj[item]);
                await this.printFuc("foodView","foodText",printObj[item])
              }
            }
            break;
          case "订单换台":
            // this.$refs.exchhangeOrder.print(json);
            this.printFuc("exchhangeOrder","exchangeOrderText",json)
            break;
          case "拼单":
            // this.$refs.siplicingOrders.print(json);
            this.printFuc("siplicingOrders","siplicingOrdersText",json)
            break;
          case "已结账":
            // this.$refs.alreadyCheckout.print(json);
            this.printFuc("alreadyCheckout","alreadyCheckoutText",json)
            break;
          case "预结账":
            // this.$refs.preCheckout.print(json);
            this.printFuc("preCheckout","preCheckoutText",json)
            break;
          case "会员充值":
            // this.$refs.memberRecharge.print(json);
            this.printFuc("memberRecharge","memberRechargeText",json)
            break;
          case "营业统计":
            // this.$refs.businessStatis.print(json);
            this.printFuc("businessStatis","businessStatisText",json)
            break;
          case "美食统计":
            // this.$refs.foodsStatis.print(json);
            this.printFuc("foodsStatis","foodsStatisText",json)
            break;
          case "换班":
            // this.$refs.shiftChange.print(json);
            this.printFuc("shiftChange","shiftChangeText",json)
            break;
          case "测试":
            this.$refs.test.print(json);
            break;
        }
        console.log("打印结束", json);
      }
    },
    printFuc(imageFuc,textFuc,json){
      const jsTextPrint = true
      const csPrint = false

      let beautiful_print = Array.isArray(json.order.printers)
        ? json.order.printers[0].beautiful_print
        : json.order.printers.beautiful_print

      if (beautiful_print === 0 && jsTextPrint) {
        this.$refs[textFuc].print(json);
        csPrint && this.$refs[imageFuc].print(json);
      }else {
        this.$refs[imageFuc].print(json);
      }
    }
  }
};
</script>

<style scoped lang="less"></style>
