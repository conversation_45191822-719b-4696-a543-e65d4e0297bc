<script>

import th from "element-ui/src/locale/lang/th";

export default {
  openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('print_queqe', 1);

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        // 创建一个对象存储空间
        db.createObjectStore('json', { keyPath: 'id', autoIncrement: true });
      };

      request.onsuccess = (event) => {
        resolve(event.target.result);
      };

      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  },
  async addJson(str) {
    const db = await this.openDatabase();
    const transaction = db.transaction('json', 'readwrite');
    const store = transaction.objectStore('json');

    return new Promise((resolve, reject) => {
      const request = store.add({ value: str });
      request.onsuccess = () => {
        resolve();
      };
      request.onerror = () => {
        reject(request.error);
      };
    });
  },
  async clearAll() {
    const db = await this.openDatabase();
    const transaction = db.transaction('json', 'readwrite');
    const store = transaction.objectStore('json');

    return new Promise((resolve, reject) => {
      const request = store.clear();
      request.onsuccess = () => {
        resolve(); // 成功完成
      };
      request.onerror = () => {
        reject(request.error);
      };
    });
  },
  async count() {
    const db = await this.openDatabase();
    const transaction = db.transaction('json', 'readwrite');
    const store = transaction.objectStore('json');

    return new Promise((resolve, reject) => {
      const request = store.count();
      request.onsuccess = () => {
        console.log(`对象存储中的记录数量: ${request.result}`);
        resolve(request.result); // 返回记录数量
      };
      request.onerror = () => {
        reject(request.error); // 处理错误
      };
    });
  },
  async deleteKey() {
    const db = await this.openDatabase();
    const transaction = db.transaction('json', 'readwrite');
    const store = transaction.objectStore('json');

    return new Promise((resolve, reject) => {
      const request = store.clear();
      request.onsuccess = () => {
        resolve(); // 成功完成
      };
      request.onerror = () => {
        reject(request.error);
      };
    });
  },
  addPrintTask(json){
    this.addJson(json)
  },
  // 扫描并删除存储的字符串
  async  getAllPrintTask() {
    const db = await this.openDatabase();
    const transaction = db.transaction('json', 'readwrite');
    const store = transaction.objectStore('json');

    return new Promise((resolve, reject) => {
      const cursorRequest = store.openCursor();
      const result = [];

      cursorRequest.onsuccess = (event) => {
        const cursor = event.target.result;

        if (cursor) {
          result.push(cursor.value.value);
          store.delete(cursor.primaryKey); // 删除记录
          cursor.continue(); // 继续遍历
        } else {
          resolve(result);
        }
      };
      cursorRequest.onerror = () => {
        reject(cursorRequest.error);
      };
    });
  },
  test(){
    this.addPrintTask({ table_id: 'Hello1'})
    this.addPrintTask({ table_id: 'Hello2'})
    this.addPrintTask({ table_id: 'Hello3'})

    let that = this
    setTimeout(function(){
      that.addPrintTask({ table_id: 'Hello4'})
      that.getAllPrintTask().then(result=>{
        console.log("getAllPrintTask", result)
      })
      that.addPrintTask({ table_id: 'Hello5'})
    }, 2000);
  }
}
</script>
