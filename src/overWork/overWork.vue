<template>
	<div style="height:100%">
    <div class="wrap">
      <div class="top">
        <div class="one">
          <div class="time" :style="{direction:gLang==2?'ltr':'rtl'}">
            <span>{{gL('openDuty')}}:</span>
            <span>{{datas.overview.user_name}}</span>
          </div>
          <div class="name" :style="{direction:gLang==2?'ltr':'rtl'}">
            <span>{{gL('dutyTime')}}:</span>
            <span class="num">{{datas.overview.start_at}} <span style="font-family:arial">~</span> {{datas.overview.leave_at}}</span>
          </div>
        </div>
        <div class="two">
          <div class="two-item" :style="{direction:gLang==2?'ltr':'rtl'}">
            <span>{{gL('workingbalance')}}:</span>
            <input class='input' readonly type="text" v-model="datas.overview.working_balance">
          </div>
          <div class="two-item" :style="{direction:gLang==2?'ltr':'rtl'}">
            <span>{{gL('reserveFund')}}:</span>
            <input class='input' readonly type="text" v-model="datas.overview.alternate_amount">
          </div>
          <div class="two-item" :style="{direction:gLang==2?'ltr':'rtl'}">
            <span>{{gL('amountReceived')}}:</span>
            <span class="num">￥{{$toMoney(datas.overview.paid_amount)}}</span>
          </div>
          <div class="two-item" :style="{direction:gLang==2?'ltr':'rtl'}">
            <span>{{gL('businessSales')}}:</span>
            <span class="num">￥{{$toMoney(datas.overview.receivable_amount)}}</span>
          </div>
        </div>
      </div>
      <div class="bod">
        <div class="pay-box">
          <div class="left"><span :style="{width:gLang==2?'90px':''}">{{gL('receivables')}}</span></div>
          <div class="right">
            <div class="up">
              <div class='tit'>
                <div class="tit-item">{{gL('payType')}}</div>
                <div class="tit-item">{{gL('orderCount')}}</div>
                <div class="tit-item">{{gL('overWorkRefundPirce')}}</div>
                <div class="tit-item">{{gL('amountCollected')}}</div>
                <div class="tit-item">{{gL('VIPrecharge')}}</div>
                <div class="tit-item">{{gL('overWorkCreditRecharge')}}</div>
                <div class="tit-item brr">{{gL('totalPrice')}}</div>
              </div>
              <div class='detail'>
                <div class='tit' v-for="(item,index) in datas.cashInfo" :key="index" v-if="item.id!=-1">
                  <div class="tit-item brr">{{$toMoney(item.total)}}</div>
                  <div class="tit-item">{{ item.debt_repayment_amount }}</div>
                  <div class="tit-item">
                    <span v-if="item.vip_recharge_amount!=0">{{item.vip_recharge_amount}}</span>
                    <span v-if="item.vip_recharge_amount==0">0</span>
                  </div>
                  <div class="tit-item">{{$toMoney(item.order_amount)}}</div>
                  <div class="tit-item">{{item.refund_amount}}</div>
                  <div class="tit-item">{{item.order_count}}</div>
                  <div class="tit-item">{{item.payment_type}}</div>
                </div>
              </div>
              <div class='down'>
                <div class='tit' v-for="(item,index) in datas.cashInfo" :key="index" v-if="item.id==-1">
                  <div class="tit-item brr">{{$toMoney(item.total)}}</div>
                  <div class="tit-item">
                    <span v-if="item.vip_recharge_amount!=0">{{item.vip_recharge_amount}}</span>
                    <span v-if="item.vip_recharge_amount==0">0</span>
                  </div>
                  <div class="tit-item">{{$toMoney(item.order_amount)}}</div>
                  <div class="tit-item">{{item.order_count}}</div>
                  <div class="tit-item">{{item.payment_type}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class='business'>
          <div class="left"><span :style="{width:gLang==2?'90px':''}">{{gL('businessSituation')}}</span></div>
          <div class="right">
            <div class="row">
              <div class="colmun" :style="{flexDirection:gLang==2?'':'row-reverse'}">
                <span v-if="gLang==2">{{gL("totalCount")}}:</span>
                <span v-if="gLang==1">:{{gL("totalCount")}}</span>
                <span>{{datas.business_situation.order_count}}</span>
              </div>
              <div class="colmun" :style="{flexDirection:gLang==2?'':'row-reverse'}">
                <span v-if="gLang==2">{{gL("average")}}:</span>
                <span v-if="gLang==1">:{{gL("average")}}</span>
                <span>￥{{$toMoney(datas.business_situation.order_avg)}}</span>
              </div>
              <div class="colmun brr" :style="{flexDirection:gLang==2?'':'row-reverse'}">
                <span v-if="gLang==1">:{{gL("discountTotal")}}</span>
                <span  v-if="gLang==2">{{gL("discountTotal")}}:</span>
                <span>￥{{$toMoney(datas.business_situation.total_discount)}}</span>
              </div>
            </div>
            <div class="row brb">
              <div class="colmun" :style="{flexDirection:gLang==2?'':'row-reverse'}">
                <span v-if="gLang==1">:{{gL("totalPepoleCount")}}</span>
                <span v-if="gLang==2">{{gL("totalPepoleCount")}}:</span>
                <span>{{datas.business_situation.customer_count}}</span>
              </div>
              <div class="colmun" :style="{flexDirection:gLang==2?'':'row-reverse'}">
                <span v-if="gLang==1">:{{gL("pepoleAverage")}}</span>
                <span v-if="gLang==2">{{gL("pepoleAverage")}}:</span>
                <span>￥{{$toMoney(datas.business_situation.customer_avg)}}</span>
              </div>
              <div class="colmun brr" :style="{flexDirection:gLang==2?'':'row-reverse'}">
                <span v-if="gLang==1">:{{gL("amountCollected")}}</span>
                <span v-if="gLang==2">{{gL("amountCollected")}}:</span>
                <span>￥{{$toMoney(datas.business_situation.real_paid_amount)}}</span>
              </div>
            </div>
          </div>
        </div>
        <div class='business vip'>
          <div class="left"><span :style="{width:gLang==2?'90px':''}">{{gL('businessVIPSituation')}}</span></div>
          <div class="right">
            <div class="row">
              <div class="colmun" :style="{flexDirection:gLang==2?'':'row-reverse'}">
                <span  v-if="gLang==1">:{{gL("VipNewCount")}}</span>
                <span  v-if="gLang==2">{{gL("VipNewCount")}}:</span>
                <span>{{datas.vip.vip_count}}</span>
              </div>
              <div class="colmun" :style="{flexDirection:gLang==2?'':'row-reverse'}">
                <span v-if="gLang==1">:{{gL("VipNewPrice")}}</span>
                <span v-if="gLang==2">{{gL("VipNewPrice")}}:</span>
                <span>￥{{$toMoney(datas.vip.recharge_amount)}}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 挂账 -->
         <div class='business vip'>
          <div class="left"><span :style="{width:gLang==2?'90px':''}">{{gL('businessCreditStatis')}}</span></div>
          <div class="right">
            <div class="row">
              <div class="colmun" :style="{direction: gLang == 1 ? 'rtl' : 'ltr'}">
                <span>{{ gL("creditHolderCount") }} : </span>
                <span>{{ datas.debt ? datas.debt.holder_count : 0 }}</span>
              </div>
              <div class="colmun" :style="{direction: gLang == 1 ? 'rtl' : 'ltr'}">
                <span>{{ gL("overWorkCreditRecharge") }} : </span>
                <span>￥{{ datas.debt ? $toMoney(datas.debt.repayment_amount) : 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="btn" @click="openModal">{{gL("overWorkAndQuit")}}</div>
      </div>
    </div>
    <modal :number_box="modal_box"
      :modal_content="modal_content"
      :modal_title="modal_title"
      @cancel="cancel" @confirm="confirm"></modal>

    <div class="mask" v-if="number_box&&order_count!=0">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{gL("tips")}}</span>
          <span class="iconfont icon-jia-copy" @click="cancelBox"></span>
        </div>
        <div class="content" :style="{direction:gLang==2?'ltr':'rtl'}">
          <span>{{gL('youHave')}}</span>
          <span class="number">{{order_count}}</span>
          <span>{{gL('checks')}}</span>
        </div>
        <div class="btn" @click="confirmBox">
            {{gL('go')+gL('checkOut')}}
        </div>
      </div>
    </div>
    <startJob :key="startJobKey" :number_box="job_box"  @startBox="startBox"></startJob>
	</div>
</template>

<script>
var self;
import modal from '../components/modal.vue'
import startJob from '../components/startJob.vue'
import { getHandoverAPI, postHandoverAPI } from './../api/index.js'
export default {
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.order_count = localStorage.getItem("order_count");

    if(this.order_count != 0) {
      self.number_box = true;
    }
    this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
    if(this.storeInfo.isHandOver){
      self.job_box = true;
      self.modal_box = false;
      this.startJobKey = +new Date();
    }else{
      self.getList();
    }
  },
  data() {
    return {
      gLang:1,
      msg:'over',
      datas:{
        business_situation:{
          customer_avg: 0,
          customer_count: 0,
          order_avg: 0,
          order_count: 0,
          real_paid_amount: 0,
          total_discount: 0
        },
        cashInfo:[],
        overview:{
          alternate_amount: 0,
          leave_at: "",
          paid_amount: 0,
          receivable_amount: 0,
          start_at: "",
          user_id: 0,
          user_name: "",
          working_balance: 0
        },
        vip:{
          recharge_amount: 0,
          vip_count: 0
        }
      },//页面数据
      loading:false,
      modal_box:false,
      modal_content:'',
      modal_title:'',
      number_box:false,
      order_count:0,
      job_box:false,
      startJobKey: +new Date(),
      storeInfo: {},
    };
  },
  methods: {
    getList(){
      self.loading = false;
      getHandoverAPI().then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.datas = response.data.data;
          console.log("data ----- ", this.datas.overview)
          self.loading = true;
        }
      });
    },
    cancel(){
      self.modal_box= false;
    },
    //交班
    confirm(){
      postHandoverAPI({
        user_id: this.storeInfo.id,
        working_balance:self.datas.overview.working_balance,
        alternate_amount:self.datas.overview.alternate_amount
      }).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.$message({
            message:self.gL('successfulOperation'),
            type: "success",
            customClass:self.$toastClass(),
            offset: 120
          });
          self.reset();
          self.logout();
        }
      });
      self.cancel();
    },
    openModal(){
      if(this.storeInfo.isHandOver){
        self.job_box = true;
        this.startJobKey = +new Date();
      }else{
        self.modal_box= true;
        self.modal_title = self.gL('tips');
        self.modal_content = self.gL('ensureOverWork');
      }
    },
    cancelBox(){
      self.number_box= false;
    },
    //您有还没结账的订单
    confirmBox(){
      self.$router.push('table')
    },
    //关闭开班和开班成功
    startBox(e){
      this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      if(e){
        self.job_box=false;
      }else{
        self.job_box=false;
        self.getList();
      }
    },
    // 交班后把数据清零cookide改变false
    reset(){
      self.datas={
        business_situation:{
          customer_avg: 0,
          customer_count: 0,
          order_avg: 0,
          order_count: 0,
          real_paid_amount: 0,
          total_discount: 0
        },
        cashInfo:[],
        overview:{
          alternate_amount: 0,
          leave_at: "",
          paid_amount: 0,
          receivable_amount: 0,
          start_at: "",
          user_id: 0,
          user_name: "",
          working_balance: 0
        },
        vip:{
          recharge_amount: 0,
          vip_count: 0
        }
      }
      // self.$setCookie('isHandOver',true,7);
      const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      storeInfo.isHandOver = true;
      localStorage.setItem("merchant_info", JSON.stringify(storeInfo));
    },
    //退出
    logout(){
      // self.$setCookie('user',null,-1); //删除帐号到cookie
      // self.$setCookie('iSremember',null,-1); //删除记住到cookie，有效期7天
      // self.$setCookie('pswd',null,-1); //删除密码到cookie
      localStorage.removeItem('token')
      localStorage.removeItem('merchant_info')
      localStorage.setItem("shouldRefresh", true);
      self.$router.push({ path: "/login" });
    }
  },
  components:{
    modal,
    startJob
  }

};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
 .wrap{
   height: 100%;
   display: flex;
   flex-direction: column;
   justify-content: space-between;
   width: 98%;
   .top{
     height: 11%;
     background-color: @bgColor;
     color:#ffffff;
     font-size: 22px;
     padding:10px  20px;
     display: flex;
     flex-direction: column;
     justify-content: space-between;
     .one{
       display: flex;
       justify-content: space-between;
       .name{
         .num{
           direction: ltr;
           display: inline-block;
         }
       }
     }
     .two{
       display: flex;
       align-items: flex-end;
       flex-direction: row-reverse;
       justify-content: space-between;
       .two-item{
         .input{
           background-color: @textColor;
           width:110px;
           outline: none;
           border:1px solid @grayColor;
           color:#ff9c00;
           direction: ltr;
           padding: 0 10px;
         }
         .num{
           color:#ff9c00;
           direction: ltr;
           display: inline-block;
         }
       }
     }
   }
   .bod{
     font-size: 22px;
     height: 75%;
     display: flex;
     flex-direction: column;
     justify-content: space-between;
     .pay-box{
       height: 60%;
       width: 100%;
       display: flex;
       overflow:hidden;
       .left{
         width: 12%;
         height: 100%;
         background-color: #666666;
         font-size: 22px;
         color:#ffffff;
         display: flex;
         align-items: center;
         justify-content: center;
         text-align: center;
         line-height: 2;
       }
       .right{
         width: 88%;
         height: 100%;
        border-left: 1px solid #ffffff;
         .up{
           height: 100%;
           width: 100%;
           .tit{
             display: flex;
             height: 17%;
             border-bottom: 1px solid #ffffff;
             background-color: @graphiteColor;
             .tit-item{
               width: 20%;
               text-align: center;
               color:@textColor;
               height: 100%;
               display: flex;
              align-items: center;
              justify-content: center;
              border-right: 1px solid #ffffff;
             }
             .brr{
               border-right: none;
             }
           }
           .detail{
             height: 66%;
             overflow-y: scroll;
             .tit{
               height: 25%;
               flex-direction: row-reverse;
               background: #e6e6e6;
               width: 100%;
             }
           }
         }
         .down{
           height: 17%;
           width: 100%;
           .tit{
             height: 100%;
             flex-direction: row-reverse;
           }
         }
       }
     }
     .business{
        // height: 22%;
        width: 100%;
        display: flex;
        .left{
          width: 12%;
          padding: 10px;
          height: 100%;
          background-color: #666666;
          font-size: 22px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          line-height: 2;
        }
        .right{
          width: 88%;
          height: 100%;
          border-left: 1px solid #ffffff;
          .row{
            padding: 10px 0px;
            display: flex;
            height: 17%;
            border-bottom: 1px solid #ffffff;
            background-color: #e6e6e6;
            height: 50%;
            .colmun{
              width: 33.3%;
              text-align: center;
              color: #1a1a1a;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              border-right: 1px solid #ffffff;
            }
            .brr{
              border-right:none;
            }
          }
          .brb{
            border-bottom: none;
          }
        }
     }
     .vip{
      //  height: 12%;
       .left{
         line-height: 1.5;
       }
       .right{
         .row{
           height: 100%;
           .colmun{
             width: 50%;
           }
         }
       }
     }
   }
   .bottom{
     height: 10%;
     display: flex;
     justify-content: center;
     align-items: flex-start;
     .btn{
       background-color: #ff9c00;
       color:#ffffff;
       font-size: 30px;
       height: 80%;
       width: 60%;
       display: flex;
       justify-content: center;
      align-items: center;
      cursor: pointer;
      &:hover{
        opacity: 0.8;
      }
     }
   }
 }
//s提示框
.mask{
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0,0,0,.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box{
    background-color: #ffffff;
    width: 600px;
    font-size: 30px;
    .title{
      background-color: #e6e6e6;
      color:#1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont{
        position: absolute;
        right: 20px;
        font-size: 23px;
        color:@grayColor;
        cursor: pointer;
      }
    }
    .content{
      text-align: center;
      padding: 50px 30px 30px 30px;
      line-height: 1.5;
      .number{
        color:#ff5151;
      }
    }
    .btn{
      margin: 20px 75px 20px 75px;
      background: @greenColor;
      color:#ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
  }
}
</style>
