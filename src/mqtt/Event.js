
export default {
  config:{
    eventObject:{},
    callbackId: 0
  },
  publish(eventName, ...args) {
    // 取出当前事件所有的回调函数
    const callbackObject = this.config.eventObject[eventName];

    if (!callbackObject) return console.warn(eventName + " not found!");

    // 执行每一个回调函数
    for (let id in callbackObject) {
      // 执行时传入参数
      callbackObject[id](...args);
    }
  },
  subscribe(eventName, callback) {
    // 初始化这个事件
    if (!this.config.eventObject[eventName]) {
      // 使用对象存储，注销回调函数的时候提高删除的效率
      this.config.eventObject[eventName] = {};
    }

    const id = this.config.callbackId++;

    // 存储订阅者的回调函数
    // callbackId使用后需要自增，供下一个回调函数使用
    this.config.eventObject[eventName][id] = callback;

    // 每一次订阅事件，都生成唯一一个取消订阅的函数
    const unSubscribe = () => {
      // 清除这个订阅者的回调函数
      delete this.config.eventObject[eventName][id];

      // 如果这个事件没有订阅者了，也把整个事件对象清除
      if (Object.keys(this.config.eventObject[eventName]).length === 0) {
        delete this.config.eventObject[eventName];
      }
    };

    return { unSubscribe };
  },
  // 清除事件
  clear(eventName) {
    // 未提供事件名称，默认清除所有事件
    if (!eventName) {
      this.config.eventObject = {};
      return;
    }

    // 清除指定事件
    delete this.config.eventObject[eventName];
  }
}
