import mqtt from 'mqtt/dist/mqtt.js'
import CryptoJS from "crypto-js";
import {
	v4
} from 'uuid';
import event from "./Event";
import slsLog from "../print/SlsLog.vue";

export default {
	config: {
		instanceId: 'post-cn-i7m2gijq00t',
		host: 'post-cn-i7m2gijq00t.mqtt.aliyuncs.com',
		port: 443,
		topic: 'print_topic',
		useTLS: true,
		accessKey: 'LTAI5tFURyzvge7PATf9WjJU',
		secretKey: '******************************',
		groupId: 'GID_0001@@@',
		reconnectTimeout: 2000,
		client: null,
		mqttConcected: false,
	},
  status(){
    return this.config.client
  },

	connect(value) {
    const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
    const token = localStorage.getItem("token");
    if (token==null || storeInfo == null){
      return;
    }

    let config = this.config

		if (config.client) {
			return
		}

		let mqtt_uuid = JSON.parse(localStorage.getItem("mqtt_uuid"))
		if (!mqtt_uuid) {
      return
		}
		console.log("mqtt_uuid", mqtt_uuid);


		let instanceId = config.instanceId;
		let host = config.host;
		let port = config.port;
		let topic = config.topic;
		let useTLS = config.useTLS;
		let accessKey = config.accessKey;
		let secretKey = config.secretKey;
		let groupId = config.groupId;
		let clientId = groupId + mqtt_uuid.uuid;
		let reconnectTimeout = config.reconnectTimeout;
		let username = 'Signature|' + accessKey + '|' + instanceId;
		var password = CryptoJS.HmacSHA1(clientId, secretKey).toString(CryptoJS.enc.Base64);

		let options = {
			clientId: clientId,
			username: username,
			password: password,
			connectTimeout: 600000,
			clean: true
		}


		// 此处需要改成你的主机ip，并保证测试手机跟你的电脑处于同一个局域网
    console.log(config)
    let that = this
    config.client = mqtt.connect('wss://' + host + ':' + port, options)
		config.client.on('connect', function(res) {
      console.log("连接成功", res,mqtt_uuid.uuid,mqtt_uuid.merchant_no);
      that.broadcast("connect",true)
			config.client.publish(topic + "/p2p/" + groupId + "9998",
				'{"merchantNo":"' + mqtt_uuid.merchant_no + '","uuid":"' + mqtt_uuid.uuid + '","action":"register"}'
			);

		}).on('message', function(topic, message) {
			let json = JSON.parse(message.toString())
      slsLog.send(json,"接受数据")
			console.log("mqtt接受消息", topic, json)
      event.publish('mqtt-message',json)
		}).on('reconnect', function(topic, message) {
			console.log("mqtt重连", topic, message)
		}).on("close",function () {
      console.log("mqtt已断开连接")
      that.broadcast("close",false)
    }).on("disconnect",function (packet) {
      console.log("mqtt 从broker接收到断开连接的报文："+packet);
      that.broadcast("disconnect",false)
    }).on("offline",function () {
      console.log("mqtt 您已断开连接，请检查网络")
      that.broadcast("offline",false)
    }).on("error",(error) =>{
      console.log("mqtt 客户端出现错误：", error);
      that.broadcast("error",false)
    })
	},
  broadcast(action,value){
    console.log("mqtt状态更改", value)
    event.publish('mqtt-status',{action:action,value:value})
  },
	close() {
		if (this.config.client) {
			this.config.client.end(true, {}, () => {
				console.log("mqtt关闭 回调");
			})
			this.config.client = null
		}
	}

}
