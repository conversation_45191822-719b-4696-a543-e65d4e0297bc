import home from './home.vue'
import login from './login.vue'
import tableHome from './table/tableHome.vue'
import table from './table/table.vue'
import foodList from './table/foodList.vue'
import checkOut from './table/checkOut.vue'
import limit from './limit/limit.vue'
import overWork from './overWork/overWork.vue'
import member from './member/member.vue'
import printer from './printer/printer.vue'
import setting from './setting/setting.vue'
import bill from './bill/bill.vue'
import foodManage from './foodManage/foodManage.vue'
import tableManage from './tableManage/tableManage.vue'
import statis from './statis/statis.vue'
import marketing from './marketing/marketing.vue'
import secondaryMain from './views/secondaryMain/index.vue'
import stores from './views/storesPage/index.vue'
import credit from './views/credit/index.vue'


import { LunchBox, Charge, Spec, Method, MethodList, MethodAdd, SpecPage, ChargeAdd, ChargeList  } from './foodManage/views/'
import { food, cate, foodAll,foodsList, foodsForm } from './foodManage'


export default {
	routes: [
		{
			path: "/secondaryMain",
			component: secondaryMain
		},
		{
			path: '/home',
			component: home,
			children: [
				{
					path: '/tableHome',
					component: tableHome,
					children: [

					], redirect: '/tableHome/table'
				},
				{
					path: '/table',
					component: table
				},
				{
					path: '/foodList/:id',
					component: foodList,
					name: "foodList"
				},
				{
					path: '/checkOut/:id',
					component: checkOut,
					name: "checkOut"
				},
				{
					path: '/limit',
					component: limit
				},
				{
					path: '/overWork',
					component: overWork
				},
				{
					path: '/member',
					component: member
				},
				{
					path: '/credit',
					component: credit
				},
				{
					path: '/printer',
					component: printer
				},
				{
					path: '/setting',
					component: setting
				},
				{
					path: '/bill',
					component: bill
				},
				{
					path: '/foodManage',
					component: foodManage,
					redirect: '/foodManage/food/list',
					children: [
						{
							path: 'food',
							component: food,
              redirect: '/foodManage/food/list',
              children: [
                {
                  path: 'list',
                  component: foodsList,
                  name: "foodList"
                },
                {
                  path: 'add',
                  component: foodsForm,
                  name: "foodAdd"
                },
                {
                  path: 'edit/:id',
                  component: food,
                  name: "foodEdit"
                }
              ]
						},
						{
							path: 'category',
							component: cate
						},
						{
							path: 'all',
							component: foodAll
						},
						{
							path: 'spec',
							component: SpecPage,
							redirect: '/foodManage/spec/spec',
							children: [
								{
									path: 'spec',
									component: Spec
								},
								{
									path: 'method',
									component: Method,
									redirect: '/foodManage/spec/method/list',
									children: [
										{
											path: 'list',
											component: MethodList
										},
										{
											path: 'add',
											component: MethodAdd
										},
										{
											path: 'edit/:id',
											component: MethodAdd
										}
									]
								},
								{
									path: 'charge',
									component: Charge,
									redirect: '/foodManage/spec/charge/list',
									children: [
										{
											path: 'list',
											component: ChargeList
										},
										{
											path: 'add',
											component: ChargeAdd
										},
										{
											path: 'edit/:id',
											component: ChargeAdd
										}
									]
								},
								{
									path: 'lunch-box',
									component: LunchBox
								}
							]
						}
					]
				},

				{
					path: '/tableManage',
					component: tableManage
				},
				{
					path: '/statis',
					component: statis
				},
				{
					path: '/marketing',
					component: marketing
				},
				{
					path: '/stores',
					component: stores
				}
			]
		},
		{
			path: '/login',
			component: login
		},
		{
			path: '*',
			redirect: '/login'
		}
	],
	linkActiveClass: 'active-link',
	mode: 'hash' //取消 #
}
