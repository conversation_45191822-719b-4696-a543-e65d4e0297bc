<template>
  <div style="height: 100%">
    <div class="wrap">
      <div class="top" v-if="!detail">
        <div class="serach">
          <div class="input">
            <span class="iconfont icon-search search"></span>
            <input
              type="number"
              oninput="if(value.length>11)value=value.slice(0,11)"
              :placeholder="gL('phoneNumber')"
              v-model="serachText"
              v-on:input="inputSearch"
            />
          </div>
          <div class="del" v-if="delete_text" @click="removeSerach">
            <span class="iconfont icon-jia-copy"></span>
          </div>
        </div>
        <div class="all" v-if="tableData.length != 0">
          <div>
            <span>{{ gL("vipCount") }}:</span>
            <span class="price">{{ tableData[0].customers_count }}</span>
          </div>
          <div :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">
            <span>{{ gL("vipTotalPrice") }}:</span>
            <span class="price" style="display: inline-block"
              >￥{{ $toMoney(tableData[0].customers_balance) }}</span
            >
          </div>
        </div>
        <div
          class="btn"
          @click="addVipBoxs(0)"
          :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
        >
          <span class="iconfont icon-jia-copy-copy"></span>
          <span>{{ gL("addVip") }}</span>
        </div>
      </div>
      <div class="top" v-if="detail">
        <div class="detail-tit">
          <div
            class="menus"
            :class="record ? 'active' : ''"
            @click="recordClick(0)"
          >
            {{ gL("vipDetail") }}
          </div>
          <div
            class="menus"
            :class="!record ? 'active' : ''"
            @click="recordClick(1)"
          >
            {{ gL("record") }}
          </div>
        </div>
        <div class="btns">
          <div class="btn-item rechar" @click="openRechargeBox(1)">
            {{ gL("recharge") }}
          </div>
          <div class="btn-item edit" @click="addVipBoxs(2)">
            {{ gL("edit") }}
          </div>
          <div class="btn-item back" @click="backDetail">{{ gL("back") }}</div>
        </div>
      </div>
      <div class="table" style="height: 86%; overflow-y: scroll" v-if="!detail">
        <el-table
          :data="tableData"
          header-cell-class-name="header"
          :header-cell-style="{ background: '#2e3033' }"
          :cell-style="{ background: '#f2f2f2', borderColor: '#cccccc' }"
          @row-click="vipDetail"
          row-class-name="row"
          style="width: 100%"
          v-loading="tableLoading"
          height="100%"
        >
          <el-table-column
            :label="gL('serialNumber')"
            type="index"
            align="center"
            width="60"
          >
          </el-table-column>
          <el-table-column
            prop="mobile"
            align="center"
            :label="gL('phoneNumber')"
          >
          </el-table-column>
          <el-table-column prop="card_number" align="center" :label="gL('vipCardNumber')">
          </el-table-column>
          <el-table-column prop="name" align="center" :label="gL('name')">
          </el-table-column>
          <el-table-column
            prop="level_name"
            align="center"
            :label="gL('vipType')"
          >
          </el-table-column>
          <el-table-column align="center" :label="gL('balance')">
            <template slot-scope="scope">
              <span>{{ $toMoney(scope.row.balance) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="integration"
            align="center"
            :label="gL('integral')"
          >
          </el-table-column>
          <el-table-column
            prop="consumption_times"
            align="center"
            :label="gL('frequencys')"
          >
          </el-table-column>
          <el-table-column
            :label="gL('operation')"
            prop="dosome"
            align="center"
          >
            <template slot-scope="scope">
              <!-- <div class="recharge" @click.stop="openRechargeBox(0,scope.row)">
              {{gL('recharge')}}
            </div> -->
              <el-button
                type="text"
                class="rmb"
                icon="iconfont icon-RMB"
                circle
                @click.stop="openRechargeBox(0, scope.row)"
              ></el-button>
              <span style="padding: 0 15px">
                <span class="line"></span>
              </span>
              <el-button
                type="text"
                class="edits"
                icon="iconfont icon-shiliangzhinengduixiang"
                @click.stop="addVipBoxs(1, scope.row)"
                circle
              ></el-button>
              <!-- <el-button type="success">{{gL('recharge')}}</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <div class="paging">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
            layout="prev, pager, next"
            :total="totalCount"
          >
          </el-pagination>
        </div>
      </div>
      <div v-if="detail" style="height: 100%">
        <div class="no-record" v-if="!record" style="height: 100%">
          <div class="top">
            <div class="select">
              <el-select
                v-model="recordType"
                :placeholder="gL('plaeseChooise')"
                @change="getRecordList(recordType)"
              >
                <el-option
                  v-for="(item, index) in recordTypeList"
                  :key="index"
                  :label="item"
                  :value="index"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="table" style="height: 86%; overflow-y: scroll">
            <el-table
              :data="recordList"
              header-cell-class-name="header"
              :header-cell-style="{ background: '#2e3033' }"
              :cell-style="{ background: '#f2f2f2', borderColor: '#cccccc' }"
              row-class-name="row"
              v-loading="tableLoading"
              style="width: 100%"
            >
              <el-table-column
                prop="order_id"
                align="center"
                width="200"
                :label="gL('ordeNum')"
              >
              </el-table-column>
              <el-table-column
                prop="cashier"
                align="center"
                :label="gL('cashier')"
              >
              </el-table-column>
              <el-table-column
                prop="payment_type"
                align="center"
                :label="gL('paymentMethod')"
              >
              </el-table-column>
              <el-table-column align="center" :label="gL('recordPrice')">
                <template slot-scope="scope">
                  <span v-if="scope.row.type == 2" style="color: #139d59"
                    >+{{ $toMoney(scope.row.amount) }}</span
                  >
                  <span v-if="scope.row.type == 1"
                    >-{{ $toMoney(scope.row.amount) }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column align="center" :label="gL('balance')">
                <template slot-scope="scope">
                  <span>{{ $toMoney(scope.row.balance) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="updated_at"
                align="center"
                :label="gL('recordTime')"
              >
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="record" v-if="record">
          <div
            class="detail"
            :style="{ textAlign: gLang == 2 ? 'left' : 'right' }"
          >
            <div
              class="row"
              :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            >
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item">{{ rowData.mobile }}</div>
                <div class="colmun-item label">{{ gL("phoneNumber") }}</div>
              </div>
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item integral">
                  {{ rowData.integration }}
                </div>
                <div class="colmun-item label">{{ gL("integral") }}</div>
              </div>
            </div>
            <div
              class="row"
              :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            >
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item">{{ rowData.name }}</div>
                <div class="colmun-item label">{{ gL("name") }}</div>
              </div>
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item">{{ rowData.consumption_times }}</div>
                <div class="colmun-item label">{{ gL("frequencys") }}</div>
              </div>
            </div>
            <div
              class="row"
              :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            >
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item" v-if="rowData.sex == 0">
                  {{ gL("man") }}
                </div>
                <div class="colmun-item" v-if="rowData.sex == 1">
                  {{ gL("woman") }}
                </div>
                <div class="colmun-item label">{{ gL("sex") }}</div>
              </div>
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item">{{ rowData.created_at }}</div>
                <div class="colmun-item label">{{ gL("vipTime") }}</div>
              </div>
            </div>
            <div
              class="row"
              :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            >
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item">{{ rowData.birth_day }}</div>
                <div class="colmun-item label">{{ gL("birthDay") }}</div>
              </div>
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item">{{ rowData.merchant_name }}</div>
                <div class="colmun-item label">{{ gL("vipShop") }}</div>
              </div>
            </div>
            <div
              class="row"
              :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            >
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item">{{ rowData.level_name }}</div>
                <div class="colmun-item label">{{ gL("vipType") }}</div>
              </div>
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item price">
                  ￥{{ $toMoney(rowData.balance) }}
                </div>
                <div class="colmun-item label">{{ gL("balance") }}</div>
              </div>
            </div>
            <div
              class="row"
              :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            >
              <div
                class="colmun"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div class="colmun-item" v-if="rowData.state == 1">
                  {{ gL("open") }}
                </div>
                <div class="colmun-item" v-if="rowData.state == 0">
                  {{ gL("off") }}
                </div>
                <div class="colmun-item label">{{ gL("state") }}</div>
              </div>
              <!-- <div class="colmun" :style="{flexDirection:gLang==1?'row':'row-reverse'}">
              <div class="colmun-item" v-if="rowData.remark!=null">{{rowData.remark}}</div>
              <div class="colmun-item" v-if="rowData.remark==null">--</div>
              <div class="colmun-item label">{{gL('remark')}}</div>
            </div> -->
              <!-- <div class="colmun" :style="{flexDirection:gLang==1?'row':'row-reverse'}">
              <div class="colmun-item">{{rowData.updated_at}}</div>
              <div class="colmun-item label">{{gL('recentConsumption')}}</div>
            </div> -->
            </div>
          </div>
        </div>
      </div>
      <!-- 模态框 -->
      <div class="mask" v-if="addVipBox">
        <div class="box">
          <div class="title">
            <span></span>
            <span>{{ add_title }}</span>
            <span class="iconfont icon-jia-copy" @click="cancelAdd"></span>
          </div>
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
            <div class="content">
              <div class="content-input">
                <div class="item">
                  <el-form-item prop="mobile">
                    <el-input
                      :placeholder="gL('phoneNumber')"
                      :disabled="!addBox"
                      type="number"
                      oninput="if(value.length>11)value=value.slice(0,11)"
                      v-model="ruleForm.mobile"
                    ></el-input>
                  </el-form-item>
                </div>
                <div class="item sex">
                  <div
                    class="check"
                    :class="ruleForm.sex == 1 ? 'active' : ''"
                    @click="clickSex(1)"
                  >
                    {{ gL("woman") }}
                  </div>
                  <div
                    class="check"
                    :class="ruleForm.sex == 0 ? 'active' : ''"
                    @click="clickSex(0)"
                  >
                    {{ gL("man") }}
                  </div>
                </div>
                <div class="item">
                  <el-form-item prop="name">
                    <el-input
                      :placeholder="gL('name')"
                      :class="gLang == 1 ? 'input-ug' : ''"
                      maxlength="16"
                      v-model="ruleForm.name"
                    ></el-input>
                  </el-form-item>
                </div>
                <div class="item">
                  <el-form-item prop="birth_day">
                    <el-col :span="24">
                      <el-date-picker
                        v-model="ruleForm.birth_day"
                        value-format="yyyy-MM-dd"
                        type="date"
                        :placeholder="gL('birthDay')"
                      >
                      </el-date-picker>
                    </el-col>
                  </el-form-item>
                </div>
              </div>
              <div class="content-input">
                <div class="item">
                  <el-form-item prop="level">
                    <el-select
                      v-model="ruleForm.level"
                      :placeholder="gL('vipType')"
                      @change="changeSelect"
                    >
                      <el-option
                        v-for="item in vip_state_list"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div class="item">
                  <!-- <el-input  v-model="ruleForm.balance">
                  <template slot="append">{{gL('VipNewPricetwo')}}</template>
                </el-input> -->
                  <el-form-item prop="balance" v-if="!addBox">
                    <!-- <el-col :span="10"> -->
                    <!-- <el-button type="warning" v-if="addBox" @click="openRechargeBox(2)">{{gL('recharge')}}</el-button> -->
                    <!-- <el-button type="warning" v-if="!addBox" >{{gL('amout')}}</el-button> -->
                    <!-- <span>{{gL('amout')}}</span> -->
                    <!-- </el-col> -->
                    <!-- <el-col :span="14"> -->
                    <el-input
                      v-model="balance"
                      :disabled="true"
                      :placeholder="gL('amout')"
                    ></el-input>
                    <!-- </el-col> -->
                  </el-form-item>
                </div>
                <div class="item">
                  <el-form-item prop="password">
                    <el-input
                      type="password"
                      maxlength="6"
                      v-model="ruleForm.password"
                      :placeholder="gL('password')"
                      show-password
                    ></el-input>
                  </el-form-item>
                </div>
                <div class="item">
                  <el-form-item prop="password_confirmation">
                    <el-input
                      type="password"
                      maxlength="6"
                      v-model="ruleForm.password_confirmation"
                      :placeholder="gL('repass')"
                      show-password
                    ></el-input>
                  </el-form-item>
                </div>

                <div class="item">
                  <el-form-item prop="card_number">
                    <el-input
                      type="text"
                      v-model="ruleForm.card_number"
                      :placeholder="gL('memberCardNumber')"
                    ></el-input>
                  </el-form-item>
                </div>
              </div>
            </div>
            <div class="btn" @click="addVip">
              {{ gL("confirm") }}
            </div>
          </el-form>
        </div>
      </div>
      <rechargeVip
        ref="recharge"
        :number_box="rechargeBox"
        :rowData="rowData"
        :numbers="amout"
        @cancelRechargeBox="cancelRechargeBox"
        @recharge="recharge"
      ></rechargeVip>

      <!-- 开班 -->
     <startJob :number_box="job_box"  @startBox="startBox"></startJob>
    </div>
  </div>
</template>

<script>
var self;
import rechargeVip from "../components/rechargeVip.vue";
import { debounce } from "./../utils/utils.js"
import { getVipListAPI,  postVipAddAPI, putVipUpdateAPI, getCustomerDetailsAPI, getVipLevelsAPI, getVipRechargeLogAPI } from "./../api/index.js";
import startJob from "../components/startJob.vue";
const detail = {
  activated: function () {
    self = this;
    self.serachText = ""; //搜索内容
    self.delete_text = false; //搜索框删除按钮
    self.gLang = localStorage.getItem("langId");
    self.pageSize = 1;
    self.gLang = localStorage.getItem("langId");
    self.getData();
    self.detail = false;
    self.record = true;
    self.recordType = 0;
  },
  data() {
    return {
      gLang: 1,
      msg: "member",
      serachText: "", //搜索内容
      delete_text: false, //搜索框删除按钮
      tableData: [],
      totalCount: 0,
      lastPage: 0,
      pageSize: 1,
      currentPage: 0,
      addVipBox: false, //添加新会员
      detail: false, //会员详情
      rowData: {}, //行数据
      vip_state_list: {}, //会员类型
      ruleForm: {
        mobile: "",
        password: "",
        password_confirmation: "",
        name: "",
        sex: 0,
        birth_day: "",
        level: "",
        payment_type: "",
        remark: "",
        card_number: "",
      },
      balance: "",
      rules: {
        mobile: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
          { min: 11, max: 11, message: this.gL("mobile"), trigger: "blur" },
        ],
        // password: [
        //   { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
        // ],
        // password_confirmation: [
        //   { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
        // ],
        name: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        level: [
          {
            required: true,
            message: this.gL("plaeseChooise"),
            trigger: "change",
          },
        ],
        birth_day: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        card_number: [
          { required: true, message: this.gL("memberCardNumber"), trigger: "blur" },
        ]
      },
      rechargeBox: false, //VIP登录窗口
      addeditBox: false, //添加或编辑充值
      addBox: true,
      add_title: this.gL("addVip"),
      record: true, //是否消费记录
      recordList: [], //消费和充值列表
      recordTypeList: [
        this.gL("all"),
        this.gL("record"),
        this.gL("rechargeRecord"),
      ], //消费和充值
      recordType: 0, //消费和充值
      twice: true, //双次按
      amout: "",
      tableLoading: false,
      job_box: false,
    };
  },
  mounted: function () {},
  methods: {
    //搜索输入框输入时候
    inputSearch() {
      if (self.serachText.length != 0) {
        self.delete_text = true;
      } else {
        self.delete_text = false;
      }
      debounce(this.getData, 1000);
      // self.getData();
    },
    //删除搜索内容
    removeSerach() {
      self.serachText = "";
      self.delete_text = false;
      self.getData();
    },
    //获取数据
    getData() {
      var data = {};
      if (self.serachText.length != 0) {
        self.pageSize = 1;
        data.keyword = self.serachText;
      }
      data.limit = 10;
      data.page = self.pageSize;
      this.tableLoading = true;
      getVipListAPI(data).then((response) => {
          if (response.status >= 200 && response.status < 300) {
            self.tableData = response.data.data;
            self.totalCount = response.data.meta.total;
            self.lastPage = response.data.meta.last_page;
            self.currentPage = response.data.meta.current_page;

            if (self.rowData) {
              for(let i = 0; i < response.data.data.length; i++) {
                if (self.rowData.id == response.data.data[i].id) {
                  self.rowData = response.data.data[i];
                }
              }
            }
          }
        }).finally(() => {
          this.tableLoading = false;
        });
    },
    handleSizeChange(val) {
      self.CurrentChange = val;
      self.getData();
    },
    handleCurrentChange(val) {
      self.pageSize = val;
      self.getData();
    },
    //模态框性别按钮
    clickSex(e) {
      self.ruleForm.sex = e;
    },
    cancelAdd(e) {
      self.addVipBox = false;
      self.emptyForm();
    },
    //点击会员模态框确定按钮
    addVip(formName) {
      if (self.twice) {
        self.twice = false;
        self.$refs.ruleForm.validate((valid) => {
          if (valid) {
            if (self.addBox) {
              // if(self.ruleForm.balance!=''){
              if (
                self.ruleForm.password == self.ruleForm.password_confirmation
              ) {
                postVipAddAPI(self.ruleForm).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                      self.$message({
                        message: self.gL("successfulOperation"),
                        type: "success",
                        customClass: self.$toastClass(),
                        offset: 120
                      });
                      self.cancelAdd();
                      self.emptyForm();
                      self.getData();
                    }
                  }).finally(() => {
                    self.twice = true;
                  })
              } else {
                self.twice = true;
                self.$message({
                  message: self.gL("passError"),
                  type: "error",
                  customClass: self.$toastClass(),
                  offset: 120
                });
              }
              // }else{
              //   self.twice = true;
              //   self.$message({
              //     message:self.gL('beforeRecharge'),
              //     type: "error",
              //     customClass:self.$toastClass()
              //   });
              // }
            } else {
              if (
                self.ruleForm.password == self.ruleForm.password_confirmation
              ) {
                putVipUpdateAPI(self.rowData.id, self.ruleForm).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                      self.$message({
                        message: self.gL("successfulOperation"),
                        type: "success",
                        customClass: self.$toastClass(),
                        offset: 120
                      });
                      self.getRowData();
                      self.cancelAdd();
                      self.emptyForm();
                      self.getData();
                    }
                  });
              } else {
                self.twice = true;
                self.$message({
                  message: self.gL("passError"),
                  type: "error",
                  customClass: self.$toastClass(),
                  offset: 120
                });
              }
            }
          } else {
            self.twice = true;
            return false;
          }
        });
      }
    },
    //编辑后更新详情数据
    getRowData() {
      getCustomerDetailsAPI(self.rowData.id).then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.rowData = response.data.data;
        }
      });
    },
    //清空FORM
    emptyForm() {
      self.ruleForm = {
        mobile: "",
        password: "",
        password_confirmation: "",
        name: "",
        sex: 0,
        birth_day: "",
        level: "",
        payment_type: "",
        remark: "",
        card_number: "",
      };
      self.balance = "";
    },
    //点击添加会员按钮
    addVipBoxs(e, s) {
      self.add_title = this.gL("addVip");
      self.addVipBox = true;
      self.twice = true;
      self.getVipList();
      self.addBox = true;
      if (e == 1) {
        self.rowData = s;
        self.addBox = false;
        self.editDetail();
      } else if (e == 2) {
        self.addBox = false;
        self.editDetail();
      }
    },
    //编辑
    editDetail() {
      console.log(self.rowData);
      self.ruleForm.mobile = self.rowData.mobile;
      self.ruleForm.name = self.rowData.name;
      self.ruleForm.sex = self.rowData.sex;
      self.ruleForm.birth_day = self.rowData.birth_day;
      self.ruleForm.level = self.rowData.level;
      self.balance = "￥" + self.rowData.balance;
      self.add_title = this.gL("editVip");
      this.ruleForm.card_number = this.rowData.card_number;
    },
    vipDetail(row) {
      self.rowData = row;
      self.detail = true;
      self.record = true;
    },
    backDetail() {
      self.detail = false;
    },
    //会员类型列表
    getVipList() {
      getVipLevelsAPI().then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.vip_state_list = response.data.data;
          this.ruleForm.level = response.data.data[0].id;
        }
      });
    },
    /**
     * 选择VIP类型更改充值价格
     */
    changeSelect(e) {
      if (self.addBox) {
        self.vip_state_list.forEach((item) => {
          if (item.id == e) {
            self.balance = "￥" + item.amount;
            // self.amout = item.amount;
          }
        });
      }
    },
    /**
     * 显示VIP充值登录窗口
     */
    openRechargeBox(s, e) {
      const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      if (storeInfo.isHandOver) {
        this.job_box = true;
        return;
      }

      if (s == 0) {
        self.rowData = e;
      }
      console.log(s);
      if (s == 2) {
        self.addeditBox = true;
        self.$refs.recharge.getAmout();
      }
      // console.log(self.rowData);
      self.rechargeBox = true;
    },

    // 关闭开班和开班成功
    startBox() {
      this.job_box = false;
    },

    /**
     * 关闭VIP充值登录窗口
     */
    cancelRechargeBox(e) {
      console.log(e);
      if (e) {
        self.getData();
      }
      self.rechargeBox = false;
    },
    //充值
    recharge() {
      //如果添加新会员不需要请求
      // if(self.addeditBox){
      //   self.cancelRechargeBox();
      //   self.ruleForm.balance = s;
      //   self.balance = self.ruleForm.balance;
      //   self.ruleForm.payment_type = e;
      // }else{
      //   self.$post('customer/recharge',{
      //       customer_id:self.rowData.id,
      //       payment_type_id:e,
      //       recharge_amount:s,
      //   }).then((response) => {
      //     if(response.status >= 200 && response.status < 300){
      //       self.cancelRechargeBox();
      //       self.$message({
      //         message:self.gL('successfulOperation'),
      //         type: "success",
      //         customClass:self.$toastClass()
      //       });
      // self.rowData.balance = response.data.data.balance;
      self.getData();
      //     }
      //   }).catch(err => {
      //     if(err.response.status>=400){
      //       if(err.response.status==401){
      //         self.$message({
      //           message:err.response.data.message,
      //           type: "error",
      //           customClass:self.$toastClass()
      //         });
      //       }
      //     }
      //   });
      // }
    },
    //点击消费和详情
    recordClick(e) {
      if (e == 1) {
        self.record = false;
        self.getRecordList(0);
      } else {
        self.record = true;
      }
    },
    //消费充值列表
    getRecordList(e) {
      var data = {};
      data.customer_id = self.rowData.id;
      if (e != 0) {
        data.type = e;
      }
      getVipRechargeLogAPI(data).then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.recordList = response.data.data.reverse();
        }
      });
    },
  },
  components: {
    rechargeVip,
    startJob,
  },
};
export default detail;
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .top {
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    background-color: #2e3033;
    color: #ffffff;
    height: 7%;
    align-items: center;
    border-bottom: 1px solid #666666;
    .serach {
      width: 250px;
      background: #4d4d4d;
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 80%;
      .input {
        //  width: 70%;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        width: 70%;
        background: #4d4d4d;
        &::placeholder {
          color: #cccccc;
        }
      }
      .search {
        color: #cccccc;
        font-size: 24px;
        padding: 0 10px;
        vertical-align: -2px;
      }
      .del {
        width: 57px;
        height: 100%;
        color: #cccccc;
        justify-content: center;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 22px;
        }
      }
    }
    .btn {
      background-color: #ff9c00;
      width: 200px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25px;
      height: 80%;
      font-size: 24px;
      cursor: pointer;
      .iconfont {
        font-size: 19px;
      }
    }
    .detail-tit {
      font-size: 24px;
      display: flex;
      .menus {
        font-size: 22px;
        padding: 10px 20px;
        text-align: center;
        cursor: pointer;
      }
      .active {
        background: #139d59;
        color: #ffffff;
      }
    }
    .btns {
      display: flex;
      .btn-item {
        font-size: 22px;
        margin-left: 20px;
        padding: 10px 0;
        width: 130px;
        text-align: center;
        cursor: pointer;
      }
      .edit {
        background-color: #139d59;
      }
      .back {
        background-color: #ffffff;
        color: #1a1a1a;
      }
      .rechar {
        background-color: #ff9c00;
      }
    }
    .all {
      display: flex;
      font-size: 24px;
      width: 500px;
      justify-content: space-between;
      .price {
        color: #ff9c00;
      }
    }
    .select {
      padding: 10px 0;
    }
  }
  .paging {
    text-align: center;
    padding-top: 10px;
    position: fixed;
    right: 0;
    bottom: 10px;
  }
  .recharge {
    background: #139d59;
    color: #ffffff;
    padding: 9px 0;
    width: 110px;
    margin: 0 auto;
    cursor: pointer;
  }
  //数字键盘提示框
  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .box {
      background-color: #ffffff;
      width: 920px;
      font-size: 30px;
      -webkit-animation: fadelogIn 0.4s;
      animation: fadelogIn 0.4s;
      .title {
        background-color: #e6e6e6;
        color: #1a1a1a;
        padding: 25px 20px;
        position: relative;
        text-align: center;
        .iconfont {
          position: absolute;
          right: 20px;
          font-size: 23px;
          color: #666666;
          cursor: pointer;
        }
      }
      .content {
        padding: 50px 70px 0 70px;
        display: flex;
        justify-content: space-between;
      }
      .content-input {
        width: 47%;
        .item {
          width: 100%;
          margin-bottom: 30px;
        }
        .select {
          margin-bottom: 20px;
        }
        .sex {
          display: flex;
          justify-content: space-between;
          .check {
            width: 48%;
            background-color: #f2f2f2;
            border: 1px solid #cccccc;
            color: #666666;
            font-size: 18px;
            text-align: center;
            cursor: pointer;
            padding: 10px;
          }
          .active {
            background-color: #139d59;
            color: #fff;
          }
        }
      }
      .btn {
        margin: 20px 70px;
        background: #139d59;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        padding: 15px 0;
        cursor: pointer;
      }
    }
  }
  .detail {
    padding: 40px 25px;
    background-color: #f2f2f2;
    height: 92%;
    .row {
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      padding: 25px;
      .colmun {
        display: flex;
        width: 49%;
        font-size: 22px;
        .colmun-item {
          width: 60%;
        }
        .label {
          width: 40%;
          color: #666666;
        }
        .price {
          color: #ff9c00;
        }
        .integral {
          color: #139d59;
        }
      }
      &:nth-of-type(odd) {
        background-color: #ffffff;
      }
      &:nth-of-type(even) {
        background-color: #f5f5f5;
      }
    }
  }
  .rmb {
    color: #ff9c00;
  }
  .edits {
    color: #139d59;
  }
  .line {
    color: #fff;
    border-left: 1px solid #ccc;
    padding-right: 10px;
  }
}
/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

@-webkit-keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
  }
}
</style>
