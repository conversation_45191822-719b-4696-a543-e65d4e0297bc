@font-face {
  font-family: "icons"; /* Project id 4476871 */
  src: url('./../fonts/icons.ttf?t=1711019281996') format('truetype');
}

.icons {
  font-family: "icons" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icons-eye-slash-fill:before {
  content: "\e7aa";
}

.icons-eye-fill:before {
  content: "\e7ab";
}

.icons-open_eye:before {
  content: "\e6e6";
}

.icons-close_eye:before {
  content: "\e6e7";
}

.icons-mini-window:before {
  content: "\e65a";
}

.icons-max-window-1:before {
  content: "\e65d";
}

.icons-close:before {
  content: "\e660";
}

.icons-max-window-2:before {
  content: "\e692";
}

