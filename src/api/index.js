import request from "./request.js";

// 获取餐桌列表
export async function getTablesListAPI(params) {
  return await request({
    url: `/cloud/api/v2/tables`,
    method: "get",
    params
  });
}

// 获取餐桌详情
export async function getTableDetailItemAPI(id) {
  return await request({
    url: `/cloud/api/v2/order/${id}`,
    method: "get"
  });
}

// 获取区域列表
export async function getAreasListAPI() {
  return await request({
    url: `/api/v1/areas`,
    method: "get"
  });
}

// 获取餐厅列表
export async function postAuthMerchantsAPI(data, options) {
  return await request({
    url: `/api/v2/auth/merchants`,
    method: "post",
    data,
    options
  });
}

// 登录
export async function postAuthorizationsAPI(data, options) {
  return await request({
    url: `/api/v2/auth/login`,
    method: "post",
    data,
    options
  });
}

// 退出登录
export async function deleteAuthorizationsAPI(data) {
  return await request({
    url: `/api/v1/authorizations`,
    method: "delete",
    data
  });
}

// 修改密码
export async function postResetPasswordAPI(data) {
  return await request({
    url: `/api/v2/auth/reset-password`,
    method: "post",
    data
  });
}

// 设置操作密码
export async function postOperationPasswordAPI(data) {
  return await request({
    url: `/api/v2/auth/operation-password`,
    method: "post",
    data
  });
}

// 发送重置密码验证码
export async function postResetPasswordSendCodeAPI(data) {
  return await request({
    url: `/api/v2/auth/reset-password-code`,
    method: "post",
    data
  });
}

// 发送设置操作密码验证码
export async function postOperationPasswordSendCodeAPI(data) {
  return await request({
    url: `/api/v2/auth/operation-password-code`,
    method: "post",
    data
  });
}

// 获取用户信息
export async function getAuthInfoAPI() {
  return await request({
    url: `/api/v2/auth/info`,
    method: "get"
  });
}

// 获取美食分类列表
export async function getFoodCategoriesAPI() {
  return await request({
    url: `/cloud/api/v2/foodCategories`,
    method: "get"
  });
}

// 获取美食列表
export async function getFoodsAPI(params) {
  return await request({
    url: `/cloud/api/v2/foods`,
    method: "get",
    params
  });
}
export async function getSpecFoodsAPI(params) {
  return await request({
    url: `/api/v2/foods/specs`,
    method: "get",
    params
  });
}

// 获取备注列表
export async function getRemarksAPI(params) {
  return await request({
    url: `/api/v1/remarks`,
    method: "get",
    params
  });
}

// 获取餐桌详情
export async function getTableDetailsAPI(id) {
  return await request({
    url: `/cloud/api/v2/table-details/${id}`,
    method: "get"
  });
}

// 取消订单
export async function postOrderCancelAPI(id) {
  return await request({
    url: `/cloud/api/v2/orders/cancel-all-foods/${id}`,
    method: "post"
  });
}

// 追加添加美食
export async function postOrderDetailsAddAPI(id, data) {
  return await request({
    url: `/api/v1/orders/${id}/details/add`,
    method: "post",
    data
  });
}

// 提交订单添加美食
export async function postOrderDetailsAPI(id, data) {
  return await request({
    url: `/cloud/api/v2/orders/add-foods/${id}`,
    method: "post",
    data
  });
}

// 获取支付类型列表
export async function getPaymentTypesListAPI(params) {
  return await request({
    url: `/api/v1/paymentTypes/list-by-type`,
    method: "get",
    params
  });
}

// 获取订单支付列表
export async function getOrderPaymentListAPI(id) {
  return await request({
    url: `/cloud/api/v2/orders/payment-list/${id}`,
    method: "get",
  });
}

// 撤销支付
export async function postPaymentReverseAPI(data) {
  return await request({
    url: `/cloud/api/v2/payment/reverse`,
    method: "post",
    data
  });
}

// 结账接口
export async function postOrderCheckoutAPI(order_id) {
  return await request({
    url: `/cloud/api/v2/orders/checkout/${order_id}`,
    method: "post"
  });
}

// 抹零接口
export async function postOrderIgnorePriceAPI(order_id, price) {
  return await request({
    url: `/cloud/api/v2/orders/ignore-price?order_id=${order_id}&price=${price}`,
    method: "post",
  });
}

// vip结账
export async function postPaymentCustomerPayAPI(data) {
  return await request({
    url: `/cloud/api/v2/payment/customer-pay`,
    method: "post",
    data
  });
}

// 是否扫码支付
export async function getPayStatusAPI(payment_no, options) {
  return await request({
    url: `/cloud/api/v2/payment/query/${payment_no}`,
    method: "get",
    options
  });
}

// 输入尾4位搜索会员
export async function getVipPhoneListAPI(params) {
  return await request({
    url: `/api/v1/vip/phone-list`,
    method: "get",
    params
  });
}

// 订单结账
export async function postPaymentOfflineAPI(data, options) {
  return await request({
    url: `/cloud/api/v2/payment/offline`,
    method: "post",
    data,
    options
  });
}

// 获取支付二维码
export async function postNativePayAPI(data) {
  return await request({
    url: `/cloud/api/v2/payment/qrcode`,
    method: "post",
    data
  });
}

// 扫码支付
export async function postMicroPayAPI(data) {
  return await request({
    url: `/cloud/api/v2/payment/micro-pay`,
    method: "post",
    data
  });
}

// 全单催菜
export async function getOrderAllUrgeAPI(table_id, order_id) {
  return await request({
    url: `/cloud/api/v2/orders/urge/all/${table_id}/${order_id}`,
    method: "get"
  });
}

// 催菜
export async function getOrderUrgeAPI(table_id, order_id, order_detail_id) {
  return await request({
    url: `/cloud/api/v2/orders/urge/${table_id}/${order_id}/${order_detail_id}`,
    method: "get"
  });
}

// 退菜
export async function postOrderDetailsCancelAPI(order_id, id, data) {
  return await request({
    // url: `/api/v1/orders/${order_id}/details/${id}/cancel`,
    url: `/cloud/api/v2/orders/cancel-food/${order_id}/${id}`,
    method: "post",
    data
  });
}

// 退菜
export async function postOrderDetailsUndoCancelAPI(order_id, id) {
  return await request({
    // url: `/api/v1/orders/${order_id}/details/${order_detail_id}/undo-cancel/${id}`,
    url: `/cloud/api/v2/orders/undo-cancel-food/${order_id}/${id}`,
    method: "post"
  });
}

// 全单退菜
export async function postOrderAllFoodsCancelAPI(id, data) {
  return await request({
    url: `/cloud/api/v2/orders/cancel-all-foods/${id}`,
    method: "post",
    data
  });
}

// 订单换台
export async function postOrderChangeTableAPI(id, data) {
  return await request({
    url: `/cloud/api/v2/orders/changeTable/${id}`,
    method: "post",
    data
  });
}

// 订单并单
export async function postOrderCollageAPI(id, data) {
  return await request({
    url: `/cloud/api/v2/orders/collage/${id}`,
    method: "post",
    data
  });
}

// 订单分单
export async function postOrderSplitAPI(id, data) {
  return await request({
    url: `/cloud/api/v2/orders/split/${id}`,
    method: "post",
    data
  });
}

// 登录vip
export async function postCustomerVerifyAPI(data) {
  return await request({
    url: `/api/v1/customer/verify`,
    method: "post",
    data
  });
}

// 获取打印机列表
export async function getPrinterListAPI(params) {
  return await request({
    url: `/api/v2/printers`,
    method: "get",
    params
  });
}

// 获取支付类型列表
export async function getPaymentTypesAPI(params) {
  return await request({
    url: `/api/v1/paymentTypes`,
    method: "get",
    params
  });
}

// 获取支付类型列表
export async function getPaymentTypesPayableListAPI(params) {
  return await request({
    url: `/api/v2/payment-types-for-recharge`,
    method: "get",
    params
  });
}

// 会员充值金额
export async function postCustomerRechargeAPI(data) {
  return await request({
    url: `/api/v2/recharge/offline-pay`,
    method: "post",
    data
  });
}

// 会员充值金额（微信）
export async function postNativePayRechargeAPI(data) {
  return await request({
    url: `/api/v2/recharge/native-pay`,
    method: "post",
    data
  });
}

// 会员充值刷码支付
export async function getOrderQueryForRechargeAPI(paymentNo, options) {
  return await request({
    url: `/api/v2/recharge/query/${paymentNo}`,
    method: "get",
    options
  });
}

// 会员充值刷卡支付
export async function postMicroPayRechargeAPI(data) {
  return await request({
    url: `/api/v2/recharge/micro-pay`,
    method: "post",
    data
  });
}

// 点击餐桌信息
export async function postSellClearSetCountAPI(data) {
  return await request({
    url: `/api/v1/sell-clear/set-count`,
    method: "post",
    data
  });
}

// 验证餐厅
export async function getCheckMerchantAPI() {
  return await request({
    url: `/api/v1/check-merchant`,
    method: "get"
  });
}

// 获取餐厅分类列表
export async function getMerchantCategoryListAPI() {
  return await request({
    url: `/api/v1/merchant_categories`,
    method: "get"
  });
}

// 获取城市列表
export async function getRegionsListAPI() {
  return await request({
    url: `/api/v1/regions`,
    method: "get"
  });
}

// 获取区域列表
export async function getRegionsAreaListAPI(id) {
  return await request({
    url: `/api/v1/region-areas/${id}`,
    method: "get"
  });
}

// 初始化餐厅设置
export async function postInitMerchantAPI(data) {
  return await request({
    url: `/api/v1/init-merchant`,
    method: "post",
    data
  });
}

// 获取
export async function getShiftIndexAPI() {
  return await request({
    url: `/api/v1/shift/index`,
    method: "get"
  });
}

// 获取
export async function postOpenAPI(data) {
  return await request({
    url: `/api/v2/handover/open`,
    method: "post",
    data
  });
}

// 获取餐桌
export async function getCollageOrderTablesAPI(params) {
  return await request({
    url: `/api/v1/collageorder/tables`,
    method: "get",
    params
  });
}

// 账单页面退菜
export async function postOrderRefundAPI(id, data) {
  return await request({
    url: `/api/v2/order/refund/${id}`,
    method: "post",
    data
  });
}

// 获取账单
export async function getBillListAPI(params) {
  return await request({
    url: `/api/v2/bill/list`,
    method: "get",
    params
  });
}

// 获取支付类型列表
export async function getBillPaymentTypesListAPI() {
  return await request({
    url: `/api/v1/paymentTypes/list`,
    method: "get"
  });
}

// 获取支付类型列表
export async function getBillPaymentTypesManageListAPI() {
  return await request({
    url: `/api/v1/paymentTypes/manage-list`,
    method: "get"
  });
}

// 获取用户信息
export async function getBillUserAPI() {
  return await request({
    url: `/api/v1/user/users`,
    method: "get"
  });
}

// 获取账单详情
export async function getBillDetailAPI(id) {
  return await request({
    url: `/api/v2/bill/detail/${id}`,
    method: "get"
  });
}

// 获取
export async function getReprintOrderAPI(id) {
  return await request({
    url: `/api/v1/reprint-order?id=${id}`,
    method: "get"
  });
}

// 修改美食状态
export async function putFoodStateAPI(id) {
  return await request({
    url: `/api/v2/foods/${id}/state`,
    method: "put"
  });
}

// 美食图片列表
export async function getFoodDefaultImageAPI() {
  return await request({
    url: `/api/v1/foods_default_images`,
    method: "get"
  });
}

// 用关键字获取美食图片
export async function getLibraryFoodsImageAPI(params) {
  return await request({
    url: `/api/v1/library-foods/images`,
    method: "get",
    params
  });
}

// 获取美食列表
export async function getFoodListAPI(params) {
  return await request({
    url: `/api/v2/foods`,
    method: "get",
    params
  });
}

// 获取formatList
export async function getFoodsFormatAPI() {
  return await request({
    url: `/api/v2/food-formats`,
    method: "get"
  });
}

// 添加美食
export async function postFoodAddAPI(data) {
  return await request({
    url: `/api/v1/food/add`,
    method: "post",
    data
  });
}

// 修改美食
export async function putFoodUpdateAPI(id, data) {
  return await request({
    url: `/api/v1/food/update/${id}`,
    method: "put",
    data
  });
}

// 删除美食
export async function deleteFoodAPI(id) {
  return await request({
    url: `/api/v2/foods/${id}`,
    method: "delete"
  });
}

// 获取美食分类
export async function getFoodCategoriesListAPI() {
  return await request({
    url: `/api/v2/foodCategories`,
    method: "get"
  });
}

// 修改美食分类状态
export async function putFoodCategoriesStateAPI(id) {
  return await request({
    url: `/api/v2/foodCategories/${id}/state`,
    method: "put"
  });
}

// 添加美食分类
export async function postFoodCategoriesAddAPI(data) {
  return await request({
    url: `/api/v2/foodCategories`,
    method: "post",
    data
  });
}

// 编辑美食分类
export async function putFoodCategoriesUpdateAPI(id, data) {
  return await request({
    url: `/api/v2/foodCategories/${id}`,
    method: "put",
    data
  });
}

// 删除美食
export async function deleteFoodCategoriesAPI(id) {
  return await request({
    url: `/api/v2/foodCategories/${id}`,
    method: "delete"
  });
}

// 从库房导入美食
export async function postLibraryFoodsImportAPI(data) {
  return await request({
    url: `/api/v1/library-foods/import`,
    method: "post",
    data
  });
}

// 库房的美食添加
export async function getLibraryFoodsListAPI(params) {
  return await request({
    url: `/api/v1/library-foods/list`,
    method: "get",
    params
  });
}

// 获取美食列表
export async function getLibraryFoodsCategoryListAPI() {
  return await request({
    url: `/api/v1/library-foods-category/list`,
    method: "get"
  });
}

// 获取沽清列表
export async function getSellClearFoodsAPI() {
  return await request({
    url: `/api/v1/sell-clear/foods`,
    method: "get"
  });
}

// 删除全部设置沽清的美食
export async function postSellClearAPI() {
  return await request({
    url: `/api/v1/sell-clear/clear`,
    method: "post"
  });
}

// 删除设置沽清的美食
export async function postSellClearDeleteAPI(data) {
  return await request({
    url: `/api/v1/sell-clear/delete`,
    method: "post",
    data
  });
}

// 获取vip列表
export async function getVipListAPI(params) {
  return await request({
    url: `/api/v1/vip/list`,
    method: "get",
    params
  });
}

// 添加vip用户
export async function postVipAddAPI(data) {
  return await request({
    url: `/api/v1/vip/add`,
    method: "post",
    data
  });
}

// 编辑vip用户
export async function putVipUpdateAPI(id, data) {
  return await request({
    url: `/api/v1/vip/update/${id}`,
    method: "put",
    data
  });
}

// 获取编辑后更新详情
export async function getCustomerDetailsAPI(id) {
  return await request({
    url: `/api/v1/customer/details/${id}`,
    method: "get"
  });
}

// 获取会员类型列表
export async function getVipLevelsAPI() {
  return await request({
    url: `/api/v1/levels`,
    method: "get"
  });
}

// 获取消费充值列表
export async function getVipRechargeLogAPI(params) {
  return await request({
    url: `/api/v1/vip/recharge-log`,
    method: "get",
    params
  });
}

// 获取值班信息
export async function getHandoverAPI() {
  return await request({
    url: `/api/v2/handover/info`,
    method: "get"
  });
}

// 获取值班信息
export async function postHandoverAPI(data) {
  return await request({
    url: `/api/v2/handover/handover/web`,
    method: "post",
    data
  });
}

// 修改打印机状态
export async function putPrinterStateAPI(id, data) {
  return await request({
    url: `/api/v2/printers/${id}/status`,
    method: "put",
    data
  });
}

// 获取打印机能打印的美食列表
export async function getPrinterFoodsAllAPI(params) {
  return await request({
    url: `/api/v2/printers/foods`,
    method: "get",
    params
  });
}

// 提交打印美食
export async function postPrinterFoodsAPI(data) {
  return await request({
    url: `/api/v2/printers/foods`,
    method: "post",
    data
  });
}

// 删除打印机
export async function deletePrinterAPI(id) {
  return await request({
    url: `/api/v2/printers/${id}`,
    method: "delete"
  });
}

// 云打印机测试
export async function getPrinterCloudTestAPI(params) {
  return await request({
    url: `/api/v1/printer/cloud-printer-test`,
    method: "get",
    params
  });
}

// 添加打印机
export async function postPrinterAddAPI(data) {
  return await request({
    url: `/api/v2/printers`,
    method: "post",
    data
  });
}

// 编辑打印机
export async function putPrinterAPI(id, data) {
  return await request({
    url: `/api/v2/printers/${id}`,
    method: "put",
    data
  });
}

// 获取员工信息
export async function getStaffUsersAPI(params) {
  return await request({
    url: `/api/v2/merchant/employees`,
    method: "get",
    params
  });
}

// 获取角色列表
export async function getStaffUserRolesAPI() {
  return await request({
    url: `/api/v2/merchant/roles`,
    method: "get"
  });
}

// 获取权限列表
export async function getPermissionAPI() {
  return await request({
    url: `/api/v2/permissions`,
    method: "get"
  });
}

// 添加角色
export async function postRolesAPI(data) {
  return await request({
    url: `/api/v2/merchant/roles`,
    method: "post",
    data
  });
}

// 编辑角色
export async function putRolesAPI(id, data) {
  return await request({
    url: `/api/v2/merchant/roles/${id}`,
    method: "put",
    data
  });
}

// 获取角色详情
export async function getRolesDetailAPI(id) {
  return await request({
    url: `/api/v2/merchant/roles/${id}`,
    method: "get"
  });
}

// 更新角色状态
export async function putRolesStatusAPI(id, data) {
  return await request({
    url: `/api/v2/merchant/roles/${id}/status`,
    method: "put",
    data
  });
}

// 修改员工状态
export async function putStaffUserStateAPI(id, data) {
  return await request({
    url: `/api/v2/merchant/employees/${id}/status`,
    method: "put",
    data
  });
}

// 添加员工
export async function postStaffUserAddAPI(data) {
  return await request({
    url: `/api/v2/merchant/employees`,
    method: "post",
    data
  });
}

// 编辑员工
export async function putStaffUserUpdateAPI(id, data) {
  return await request({
    url: `/api/v2/merchant/employees/${id}`,
    method: "put",
    data
  });
}

// 修改支付状态
export async function getPaymentTypesStateAPI(id) {
  return await request({
    url: `/api/v1/paymentTypes/state/${id}`,
    method: "get"
  });
}

// 添加支付类型
export async function postPaymentTypesAddAPI(data) {
  return await request({
    url: `/api/v1/paymentTypes/add`,
    method: "post",
    data
  });
}

// 编辑支付类型
export async function putPaymentTypesUpdateAPI(id, data) {
  return await request({
    url: `/api/v1/paymentTypes/update/${id}`,
    method: "put",
    data
  });
}

// 删除支付类型
export async function deletePaymentTypesDeleteAPI(id) {
  return await request({
    url: `/api/v1/paymentTypes/delete/${id}`,
    method: "delete"
  });
}

// 获取备注列表
export async function getRemarksListAPI(params) {
  return await request({
    url: `/api/v1/remarks/list`,
    method: "get",
    params
  });
}

// 获取角色列表
export async function getRamarkCategoryListAPI() {
  return await request({
    url: `/api/v1/remarkCategory`,
    method: "get"
  });
}

// 修改备注状态
export async function getRemarksStateAPI(id) {
  return await request({
    url: `/api/v1/remarks/state/${id}`,
    method: "get"
  });
}

// 添加备注
export async function postRemarksAddAPI(data) {
  return await request({
    url: `/api/v1/remarks/add`,
    method: "post",
    data
  });
}

// 编辑备注
export async function putRemarksUpdateAPI(id, data) {
  return await request({
    url: `/api/v1/remarks/update/${id}`,
    method: "put",
    data
  });
}

// 删除备注
export async function deleteRemarksDeleteAPI(id) {
  return await request({
    url: `/api/v1/remarks/delete/${id}`,
    method: "delete"
  });
}

// 添加备注
export async function postClearPasswordAPI(data) {
  return await request({
    url: `/api/v1/clear-password`,
    method: "post",
    data
  });
}

// 获取商家类型
export async function getMerchantCategoriesAPI() {
  return await request({
    url: `/api/v1/merchant_categories`,
    method: "get"
  });
}

// 获取商家信息
export async function getMerchantInfoAPI() {
  return await request({
    url: `/api/v1/merchant_info`,
    method: "get"
  });
}

// 编辑商家信息
export async function putMerchatUpdateAPI(data) {
  return await request({
    url: `/api/v1/update/merchant`,
    method: "put",
    data
  });
}

// 获取班次列表
export async function getShiftListAPI(id) {
  return await request({
    url: `/api/v1/shift/list`,
    method: "get"
  });
}

// 修改班次状态
export async function getShiftStateAPI(id) {
  return await request({
    url: `/api/v1/shift/state/${id}`,
    method: "get"
  });
}

// 添加班次
export async function postShiftStoreAPI(data) {
  return await request({
    url: `/api/v1/shift/store`,
    method: "post",
    data
  });
}

// 编辑班次
export async function putShiftUpdateAPI(id, data) {
  return await request({
    url: `/api/v1/shift/update/${id}`,
    method: "put",
    data
  });
}

// 删除班次
export async function deleteShiftDeleteAPI(id) {
  return await request({
    url: `/api/v1/shift/delete/${id}`,
    method: "delete"
  });
}

// 获取smart
export async function getSmartListAPI(params) {
  return await request({
    url: `/api/v1/smart`,
    method: "get",
    params
  });
}

// 获取smart
export async function getSmartBindStatusAPI() {
  return await request({
    url: `/api/v1/smart-bind-status`,
    method: "get"
  });
}

// 获取服务列表
export async function getServiceListAPI() {
  return await request({
    url: `/api/v1/service/list`,
    method: "get"
  });
}

// 编辑服务
export async function postServiceUpdateAPI(data) {
  return await request({
    url: `/api/v1/service/update`,
    method: "post",
    data
  });
}

// 添加服务
export async function postServiceAddAPI(data) {
  return await request({
    url: `/api/v1/service/add`,
    method: "post",
    data
  });
}

// 删除服务
export async function postServiceDeleteAPI(data) {
  return await request({
    url: `/api/v1/service/delete`,
    method: "post",
    data
  });
}

// 获取下载点菜端
export async function getAppUpdateAPI(params) {
  return await request({
    url: `/api/v1/app-update`,
    method: "get",
    params
  });
}

// 获取餐厅设置
export async function getMerchantConfigListAPI(params) {
  return await request({
    url: `/api/v1/merchantconfig/list`,
    method: "get",
    params
  });
}

// 编辑餐厅设置
export async function postMerchantConfigUpdateAPI(data) {
  return await request({
    url: `/api/v1/merchantconfig/update`,
    method: "post",
    data
  });
}

// 获取短信列表
export async function getSmsListAPI(params) {
  return await request({
    url: `/api/v1/sms/list`,
    method: "get",
    params
  });
}

// 获取检查短信支付状态
export async function getSmsQueryAPI(params) {
  return await request({
    url: `/api/v1/sms/query`,
    method: "get",
    params
  });
}

// 获取检查短信支付状态
export async function getSmsPayAPI(params) {
  return await request({
    url: `/api/v1/sms/pay`,
    method: "get",
    params
  });
}

// 获取营业统计
export async function getStatisticsBusinessAPI(params) {
  return await request({
    url: `/api/v2/statistics/business`,
    method: "get",
    params
  });
}

// 获取营业统计
export async function getStatisticsFoodsAPI(params) {
  return await request({
    url: `/api/v2/statistics/foods`,
    method: "get",
    params
  });
}

// 获取营业统计
export async function getStatisticsHandoverAPI(params) {
  return await request({
    url: `/api/v2/statistics/handover`,
    method: "get",
    params
  });
}

// 获取统计图表
export async function getStatisticsGraphAPI(params) {
  return await request({
    url: `/api/v2/statistics/graph`,
    method: "get",
    params
  });
}

// 获取桌子列表
export async function getTableListAPI(params) {
  return await request({
    url: `/api/v1/table/list`,
    method: "get",
    params
  });
}

// 修改桌子状态
export async function getTableStateAPI(id) {
  return await request({
    url: `/api/v1/table/state/${id}`,
    method: "get"
  });
}

// 添加桌子信息
export async function postTableAddAPI(data) {
  return await request({
    url: `/api/v1/table/add`,
    method: "post",
    data
  });
}

// 编辑桌子信息
export async function putTableUpdateAPI(id, data) {
  return await request({
    url: `/api/v1/table/update/${id}`,
    method: "put",
    data
  });
}

// 删除桌子信息
export async function deleteTableDeleteAPI(id) {
  return await request({
    url: `/api/v1/table/delete/${id}`,
    method: "delete"
  });
}

// 获取餐厅区域列表
export async function getAreaListAPI() {
  return await request({
    url: `/api/v1/area/list`,
    method: "get"
  });
}

// 获取餐厅区域列表
export async function getAreaStateAPI(id) {
  return await request({
    url: `/api/v1/area/state/${id}`,
    method: "get"
  });
}

// 添加餐厅区域
export async function postAreaAddAPI(data) {
  return await request({
    url: `/api/v1/area/add`,
    method: "post",
    data
  });
}

// 编辑餐厅区域
export async function putAreaUpdateAPI(id, data) {
  return await request({
    url: `/api/v1/area/update/${id}`,
    method: "put",
    data
  });
}

// 删除餐厅区域
export async function deleteAreaDeleteAPI(id) {
  return await request({
    url: `/api/v1/area/delete/${id}`,
    method: "delete"
  });
}

// 获取餐桌信息
export async function postOrdersAPI(params) {
  return await request({
    url: `/cloud/api/v2/orders`,
    method: "post",
    params
  });
}

// 切换扫码点菜小程序下单
export async function getChangeSupportScanOrderAPI(food_id) {
  return await request({
    url: `/api/v2/foods/${food_id}/support-scan-order`,
    method: "put"
  });
}

// 修改退款密码后获取用户数据
export async function getProfileMeAPI() {
  return await request({
    url: `/api/v1/profile/me`,
    method: "get"
  });
}

// 获取挂账人列表
export async function getCreditListAPI(page, limit, keyword) {
  return await request({
    url: `/api/v2/debt/holders?page=${page}&limit=${limit}&keyword=${keyword}`,
    method: "get",
  });
}

// 手机号搜索赊账人
export async function getCreditAvailableAPI(keyword) {
  return await request({
    url: `/api/v2/debt/holders/available?keyword=${keyword}`,
    method: "get",
  });
}

// 添加挂账人
export async function psotCreditAddAPI(data) {
  return await request({
    url: `/api/v2/debt/holders`,
    method: "post",
    data
  });
}

// 编辑挂账人
export async function putCreditEditAPI(id, data) {
  return await request({
    url: `/api/v2/debt/holders/${id}`,
    method: "put",
    data
  });
}

// 修改挂账人状态
export async function putCreditStatusAPI(id, data) {
  return await request({
    url: `/api/v2/debt/holders/${id}/status`,
    method: "put",
    data
  });
}

// 挂账交易历史记录
export async function getCreditHistoryAPI(query) {
  return await request({
    url: `/api/v2/debt/transactions${query}`,
    method: "get",
  });
}

// 挂账交易历史记录
export async function postCreditPayAPI(data) {
  return await request({
    url: `/cloud/api/v2/payment/debt-pay`,
    method: "post",
    data,
  });
}

// 挂账线下还账
export async function postCreditRePaymentAPI(data) {
  return await request({
    url: `/api/v2/debt/repayment/offline-pay`,
    method: "post",
    data,
  });
}

// 挂账微信扫码还账
export async function postCreditNativePayAPI(data) {
  return await request({
    url: `/api/v2/debt/repayment/native-pay`,
    method: "post",
    data,
  });
}

// 挂账还账状态查询
export async function getCreditRePaymentQueryAPI(no) {
  return await request({
    url: `/api/v2/debt/repayment/query/${no}`,
    method: "get",
  });
}

// 挂账付款码还账
export async function postCreditRePaymentMicroPayAPI(data) {
  return await request({
    url: `/api/v2/debt/repayment/micro-pay`,
    method: "post",
    data
  });
}

// 规格列表
export async function getFoodSpecsAPI() {
  return await request({
    url: `/api/v2/food-specs`,
    method: "get"
  });
}

// 规格添加
export async function postFoodSpecsAPI(data) {
  return await request({
    url: `/api/v2/food-specs`,
    method: "post",
    data
  });
}

// 规格删除
export async function deleteFoodSpecsAPI(id) {
  return await request({
    url: `/api/v2/food-specs/${id}`,
    method: "delete"
  });
}

// 规格已关联的美食列表
export async function getFoodSpecsFoodsAPI(id) {
  return await request({
    url: `/api/v2/food-specs/${id}/foods`,
    method: "get"
  });
}

// 规格 获取美食列表
export async function getFoodsParentsAPI(id) {
  return await request({
    url: `/api/v2/foods/parents?category_id=${id}`,
    method: "get"
  });
}

// 规格保存美食关联关系
export async function postFoodSpecsFoodsAPI(data) {
  return await request({
    url: `/api/v2/food-specs/foods`,
    method: "post",
    data
  });
}

// 设置排序
export async function postFoodSpecsSortAPI(data) {
  return await request({
    url: `/api/v2/food-specs/sort`,
    method: "post",
    data
  });
}

// 添加做法分组
export async function postFoodMethodGroupAPI(data) {
  return await request({
    url: `/api/v2/method-groups`,
    method: "post",
    data
  });
}

// 获取做法分组
export async function getFoodMethodGroupAPI() {
  return await request({
    url: `/api/v2/method-groups`,
    method: "get"
  });
}

// 删除做法分组
export async function deleteFoodMethodGroupAPI(id) {
  return await request({
    url: `/api/v2/method-groups/${id}`,
    method: "delete"
  });
}

// 编辑做法分组
export async function putFoodMethodGroupAPI(id, data) {
  return await request({
    url: `/api/v2/method-groups/${id}`,
    method: "put",
    data
  });
}

// 添加做法
export async function postFoodMethodAPI(data) {
  return await request({
    url: `/api/v2/methods`,
    method: "post",
    data
  });
}

// 获取美食做法列表
export async function getFoodMethodListAPI(id, keyword) {
  return await request({
    url: `/api/v2/methods?group_id=${id}&keyword=${keyword}`,
    method: "get",
  });
}

// 获取做法详情
export async function getFoodMethodDetailAPI(id) {
  return await request({
    url: `/api/v2/methods/${id}`,
    method: "get",
  });
}

// 删除做法
export async function deleteFoodMethodAPI(id) {
  return await request({
    url: `/api/v2/methods/${id}`,
    method: "delete"
  });
}
// 编辑做法
export async function putFoodMethodAPI(id, data) {
  return await request({
    url: `/api/v2/methods/${id}`,
    method: "put",
    data
  });
}

// 保存做法分组排序
export async function postFoodMethodGroupSortAPI(data) {
  return await request({
    url: `/api/v2/method-groups/sort`,
    method: "post",
    data
  });
}

// 保存做法排序
export async function postFoodMethodSortAPI(data) {
  return await request({
    url: `/api/v2/methods/sort`,
    method: "post",
    data
  });
}

// 做法关联的美食列表
export async function getFoodMethodFoodsAPI(id) {
  return await request({
    url: `/api/v2/methods/${id}/foods`,
    method: "get"
  });
}

// 保存做法美食关联关系
export async function postFoodMethodFoodsAPI(data) {
  return await request({
    url: `/api/v2/methods/foods`,
    method: "post",
    data
  });
}

// 添加加料分组
export async function postFoodChargeGroupAPI(data) {
  // 加料分组（美食分类类型为 2 ）
  return await request({
    url: `/api/v2/food-addition-groups`,
    method: "post",
    data
  });
}

// 获取加料分组列表
export async function getFoodChargeGroupAPI() {
  return await request({
    url: `/api/v2/food-addition-groups`,
    method: "get"
  });
}
// 删除加料分组
export async function deleteFoodChargeGroupAPI(id) {
  return await request({
    url: `/api/v2/food-addition-groups/${id}`,
    method: "delete"
  });
}

// 编辑加料分组
export async function putFoodChargeGroupAPI(id, data) {
  return await request({
    url: `/api/v2/food-addition-groups/${id}`,
    method: "put",
    data
  });
}

// 加料分组排序
/**
 *
 * @param {Object} data
 * example: {
    "items": [
        {
            "id": 192,
            "sort": 2
        },
        {
            "id": 202,
            "sort": 1
        }
    ]
}
 * @returns
 */
export async function postFoodChargeGroupSortAPI(data) {
  return await request({
    url: `/api/v2/food-addition-groups/sort`,
    method: "post",
    data
  });
}

// 获取加料列表
/**
 * 获取加料列表
 * @param {Number} category_id 加料分组id
 * @param {String} keyword 搜索关键词
 * @param {Number} state 状态 0 禁用 1 启用 -1 全部
 * @returns
 */
export async function getFoodChargeListAPI(category_id, keyword = '', state = 1) {
  let url = `/api/v2/food-additions`;
  return await request({
    url: url,
    params: {
      category_id,
      keyword,
      state
    },
    method: "get"
  });
}

// 创建加料
export async function postFoodChargeAPI(data) {
  return await request({
    url: `/api/v2/food-additions`,
    method: "post",
    data
  });
}

// 编辑加料
export async function putFoodChargeAPI(id, data) {
  return await request({
    url: `/api/v2/food-additions/${id}`,
    method: "put",
    data
  });
}

// 删除加料
export async function deleteFoodChargeAPI(id) {
  return await request({
    url: `/api/v2/food-additions/${id}`,
    method: "delete"
  });
}

// 获取加料绑定的美食列表
export async function getFoodChargeFoodsAPI(id) {
  return await request({
    url: `/api/v2/food-additions/${id}/foods`,
    method: "get"
  });
}

// 保存加料美食关联关系
export async function postFoodChargeBindFoodsAPI(data) {
  return await request({
    url: `/api/v2/food-additions/foods`,
    method: "post",
    data
  });
}

/**
 * 保存加料排序
 * @param {Object} data  example: {
    "lunch_box_id": 5195,
    "foods": [
        {
            "food_id": 657,
            "price": 0
        }, // 不加价
        {
            "food_id": 661,
            "price": 100
        }, // 加价1元（以分为单位）
        {
            "food_id": 662,
            "price": 50
        }, // 加价0.5元
    ]
 * }
 * @returns
 */
export async function postFoodChargeSortAPI(data) {
  return await request({
    url: `/api/v2/food-additions/sort`,
    method: "post",
    data
  });
}
/**
 * 解除加料美食关联关系
 * @param {Number} addition_id 加料id
 * @param {Array} food_ids 美食id
 * @returns
 */
export async function deleteFoodChargeBindFoodsAPI(addition_id, food_ids) {
  return await request({
    url: `/api/v2/food-additions/foods`,
    method: "delete",
    data: {
      addition_id: addition_id,
      food_ids: food_ids
    }
  });
}
