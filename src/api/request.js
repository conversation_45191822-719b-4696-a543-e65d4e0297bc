import axios from "axios";
import { Message } from "element-ui";

let messageKey = true;
function messageBound(message) {
  if (messageKey) {
    Message({
      message: message,
      type: "error",
      customClass: localStorage.getItem("langId") == 1 ? "uy-toast" : "zh-toast"
    });
    messageKey = false;
    setTimeout(() => {
      messageKey = true;
    }, 2000);
  }
}

function JsonToForm(data) {
  const formData = new FormData();
  for (let key in data) {
    if (data[key] || data[key] == 0) {
      formData.append(key, data[key]);
    }
  }
  return formData;
}

export default function(options) {
  return new Promise(async function(resolve, reject) {
    const requestData =
      options && options.options && options.options.isForm
        ? JsonToForm(options.data)
        : options.data;

    const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));

    const appsData = window.electronAPI && window.electronAPI.getApps && await window.electronAPI.getApps()
    const appVersion = {
      versioncode: 10006
    };
    if (appsData) {
      appVersion.terminal = "windows-cashier";
      appVersion.platform = appsData.platform;
    } else {
      appVersion.terminal = "web-cashier";
      appVersion.platform = "browser";
    }

    const headers = {
      Authorization: "Bearer " + localStorage.getItem("token"),
      Accept: "application/json",
      "Content-Type": "application/json",
      MerchantNo: storeInfo ? storeInfo.merchant_no : "",
      ...appVersion,
      ...options.headers
    };
    if (options.lang) {
      headers["Accept-Language"] = options.lang;
    } else {
      headers["Accept-Language"] =
        localStorage.getItem("langId") == 1 ? "ug-CN" : "zh-CN";
    }

    axios({
      method: options.method || "GET",
      url: options.url || "",
      data: requestData || {},
      params: options.params || {},
      headers
    })
      .then(res => {
        // if (res.status == 200 || res.status == 201 || res.status == 204) {
        if (res.status >= 200 || res.status < 300) {
          resolve(res);
          return;
        }

        if (res.data.status == 401) {
          messageBound(res.data.message);
          if (options.options && options.options.noLogin) {

          } else {
            window.location.href = "#/login";
          }
          resolve(res.data.message);
          localStorage.removeItem("token");
          localStorage.removeItem('merchant_info')
          localStorage.setItem("shouldRefresh", true);
          return;
        }
        console.log("服务器返回格式错误", res);
        if (options && options.options && !options.options.isNoMessage) {
          if (res.response && res.response.data && res.response.data.message) {
            Message({
              message: res.response.data.message,
              type: "error",
              customClass:
                localStorage.getItem("langId") == 1 ? "uy-toast" : "zh-toast"
            });
          } else {
            Message({
              message: res.data.message
                ? res.data.message
                : "كەلگەن ئۇچۇردا مەسىلە بار",
              type: "error",
              customClass:
                localStorage.getItem("langId") == 1 ? "uy-toast" : "zh-toast"
            });
          }
        }
        resolve(res);
      })
      .catch(err => {
        console.log("网络请求错误 -> ", err.response);
        if (!err.response) {
          reject(err);
          Message({
            message: "مۇلازىمىتېردا خاتالىق كۆرۈلدى",
            type: "error",
            customClass:
              localStorage.getItem("langId") == 1 ? "uy-toast" : "zh-toast"
          });
          return;
        }
        // if (err.response.status == 402 || err.response.status == 422 || err.response.status == 400) {
        if (err.response.status == 401) {
          messageBound(err.response.data.message);
          if (options.options && options.options.noLogin) {

          } else {
            window.location.href = "#/login";
          }
          reject(err);
          localStorage.removeItem("token");
          localStorage.removeItem('merchant_info')
          localStorage.setItem("shouldRefresh", true);
          return;
        } else if (err.response.status >= 400) {
          if (options && options.options && options.options.isNoMessage) {

          } else {
            messageBound(err.response.data.message);
          }
          reject(err);
          return;
        }
        reject(err);
      });
  });
}
