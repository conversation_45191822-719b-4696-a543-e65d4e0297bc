
/**
 * 正整数输入验证指令
 * 
 * 功能特性：
 * 1. 只允许输入正整数
 * 2. 自动过滤小数点和非数字字符
 * 3. 支持最小值验证（默认1）
 * 4. 支持粘贴操作
 * 5. 实时验证和格式化
 * 
 * 使用示例：
 * 
 * // 基础使用 - 默认最小值为1
 * <input v-positive-integer />
 * 
 * // 自定义最小值
 * <input v-positive-integer="10" />
 * 
 * // 在Vue组件中使用
 * <el-input v-positive-integer="1" v-model="quantity" />
 * 
 * 参数说明：
 * - binding.value: 最小值，默认为1
 */
export function validatePositiveInteger(vue) {
  vue.directive("positive-integer", {
    bind(el, binding) {
      const minValue = binding.value || 1;
      const input = el.tagName === 'INPUT' ? el : el.querySelector('input') || el;
      let isProcessing = false;

      // 核心验证函数 - 添加小数点过滤
      const validate = (value) => {
        if (!value) return "";

        // 过滤非数字字符和小数点
        const filteredValue = value.replace(/[^\d]/g, "");
        const numValue = parseInt(filteredValue, 10) || 0;

        return numValue < minValue ? "" : numValue.toString();
      };

      // 统一处理值更新
      const updateValue = (newValue) => {
        if (isProcessing) return;
        isProcessing = true;

        const oldValue = input.value;
        const validatedValue = validate(newValue);

        if (validatedValue !== oldValue) {
          input.value = validatedValue;
          const event = new Event('input', { bubbles: true, cancelable: true });
          el.dispatchEvent(event);
        }

        isProcessing = false;
      };

      // 输入事件处理 - 添加即时小数点过滤
      const handleInput = (e) => {
        // 如果输入的是小数点，立即阻止并恢复之前的值
        if (e.data === '.') {
          e.preventDefault();
          input.value = input.value.replace('.', '');
          return;
        }
        updateValue(e.target.value);
      };

      // 粘贴事件处理
      const handlePaste = (e) => {
        e.preventDefault();
        const pasteData = e.clipboardData.getData('text/plain');
        updateValue(pasteData);
      };

      // 失去焦点事件处理
      const handleBlur = () => updateValue(input.value);

      // 添加事件监听
      input.addEventListener('input', handleInput);
      input.addEventListener('paste', handlePaste);
      input.addEventListener('blur', handleBlur);

      // 清理函数
      el._positiveIntegerCleanup = () => {
        input.removeEventListener('input', handleInput);
        input.removeEventListener('paste', handlePaste);
        input.removeEventListener('blur', handleBlur);
      };
    },

    unbind(el) {
      if (el._positiveIntegerCleanup) {
        el._positiveIntegerCleanup();
      }
    }
  });
}

/**
 * 货币输入验证指令
 * 
 * 功能特性：
 * 1. 自动过滤非数字字符（保留小数点和负号）
 * 2. 限制小数位数（默认2位）
 * 3. 支持最小值和最大值验证
 * 4. 可选择是否允许负数
 * 5. 失去焦点时自动格式化显示
 * 6. 支持粘贴操作
 * 
 * 使用示例：
 * 
 * // 基础使用 - 默认配置
 * <input v-currency />
 * 
 * // 自定义小数位数
 * <input v-currency="{ precision: 3 }" />
 * 
 * // 设置范围限制
 * <input v-currency="{ min: 0, max: 1000 }" />
 * 
 * // 允许负数
 * <input v-currency="{ allowNegative: true }" />
 * 
 * // 不格式化显示
 * <input v-currency="{ formatOnBlur: false }" />
 * 
 * // 完整配置示例
 * <input v-currency="{
 *   precision: 2,        // 小数位数
 *   min: 0,             // 最小值
 *   max: 999999999.99,  // 最大值
 *   allowNegative: false, // 是否允许负数
 *   formatOnBlur: true   // 失去焦点时是否格式化
 * }" />
 * 
 * 配置参数说明：
 * - precision: 小数位数，默认2位
 * - min: 最小值，默认0
 * - max: 最大值，默认999999999.99
 * - allowNegative: 是否允许负数，默认false
 * - formatOnBlur: 失去焦点时是否格式化，默认true
 */
export function validateCurrency(vue) {
  vue.directive("currency", {
    bind(el, binding) {
      const input = el.tagName === 'INPUT' ? el : el.querySelector('input') || el;
      let isProcessing = false;
      
      // 获取配置参数
      const config = binding.value || {};
      const {
        precision = 2,        // 小数位数，默认2位
        min = 0,             // 最小值
        max = 999999999.99,  // 最大值
        allowNegative = false, // 是否允许负数
        formatOnBlur = true   // 失去焦点时是否格式化
      } = config;

      // 核心验证函数
      const validate = (value) => {
        if (!value) return "";

        // 移除所有非数字字符（除了小数点和负号）
        let filteredValue = value.replace(/[^\d.-]/g, "");
        
        // 处理负号
        if (!allowNegative) {
          filteredValue = filteredValue.replace(/-/g, "");
        } else {
          // 只允许一个负号在开头
          const minusCount = (filteredValue.match(/-/g) || []).length;
          if (minusCount > 1) {
            filteredValue = filteredValue.replace(/-/g, "");
          }
        }

        // 处理小数点
        const dotCount = (filteredValue.match(/\./g) || []).length;
        if (dotCount > 1) {
          // 只保留第一个小数点
          const parts = filteredValue.split('.');
          filteredValue = parts[0] + '.' + parts.slice(1).join('');
        }

        // 限制小数位数
        if (filteredValue.includes('.')) {
          const parts = filteredValue.split('.');
          if (parts[1].length > precision) {
            filteredValue = parts[0] + '.' + parts[1].substring(0, precision);
          }
        }

        // 转换为数字进行范围验证
        const numValue = parseFloat(filteredValue);
        if (isNaN(numValue)) return "";

        // 范围验证
        if (numValue < min) return min.toString();
        if (numValue > max) return max.toString();

        return filteredValue;
      };

      // 格式化显示
      const formatDisplay = (value) => {
        if (!value) return "";
        
        const numValue = parseFloat(value);
        if (isNaN(numValue)) return "";

        // 格式化为固定小数位数
        return numValue.toFixed(precision);
      };

      // 统一处理值更新
      const updateValue = (newValue, shouldFormat = false) => {
        if (isProcessing) return;
        isProcessing = true;

        const oldValue = input.value;
        const validatedValue = validate(newValue);
        const finalValue = shouldFormat ? formatDisplay(validatedValue) : validatedValue;

        if (finalValue !== oldValue) {
          input.value = finalValue;
          const event = new Event('input', { bubbles: true, cancelable: true });
          el.dispatchEvent(event);
        }

        isProcessing = false;
      };

      // 输入事件处理
      const handleInput = (e) => {
        updateValue(e.target.value, false);
      };

      // 粘贴事件处理
      const handlePaste = (e) => {
        e.preventDefault();
        const pasteData = e.clipboardData.getData('text/plain');
        updateValue(pasteData, false);
      };

      // 失去焦点事件处理
      const handleBlur = () => {
        if (formatOnBlur) {
          updateValue(input.value, true);
        } else {
          updateValue(input.value, false);
        }
      };

      // 获得焦点事件处理
      const handleFocus = () => {
        // 移除格式化，显示原始值
        const numValue = parseFloat(input.value);
        if (!isNaN(numValue)) {
          input.value = numValue.toString();
        }
      };

      // 添加事件监听
      input.addEventListener('input', handleInput);
      input.addEventListener('paste', handlePaste);
      input.addEventListener('blur', handleBlur);
      input.addEventListener('focus', handleFocus);

      // 清理函数
      el._currencyCleanup = () => {
        input.removeEventListener('input', handleInput);
        input.removeEventListener('paste', handlePaste);
        input.removeEventListener('blur', handleBlur);
        input.removeEventListener('focus', handleFocus);
      };
    },

    unbind(el) {
      if (el._currencyCleanup) {
        el._currencyCleanup();
      }
    }
  });
}

// export function validatePositiveInteger(vue) {
//   vue.directive("positive-integer", {
//     bind(el, binding) {
//       const minValue = binding.value || 1;
//       const input = el.tagName === 'INPUT' ? el : el.querySelector('input') || el;
//       let isProcessing = false;

//       // 核心验证函数 - 添加负号过滤
//       const validate = (value) => {
//         if (!value) return "";

//         // 过滤非数字字符、小数点和负号
//         const filteredValue = value.replace(/[^\d]/g, "");
//         const numValue = parseInt(filteredValue, 10) || 0;

//         return numValue < minValue ? "" : numValue.toString();
//       };

//       // 统一处理值更新
//       const updateValue = (newValue) => {
//         if (isProcessing) return;
//         isProcessing = true;

//         const oldValue = input.value;
//         const validatedValue = validate(newValue);

//         if (validatedValue !== oldValue) {
//           input.value = validatedValue;
//           const event = new Event('input', { bubbles: true, cancelable: true });
//           el.dispatchEvent(event);
//         }

//         isProcessing = false;
//       };

//       // 输入事件处理 - 添加负号和小数点过滤
//       const handleInput = (e) => {
//         // 如果输入的是小数点或负号，立即阻止
//         if (e.data === '.' || e.data === '-') {
//           e.preventDefault();
//           input.value = input.value.replace(/[.-]/g, '');

//           // 尝试设置光标位置（兼容非number类型）
//           try {
//             if (input.type !== 'number') {
//               input.setSelectionRange(input.value.length, input.value.length);
//             }
//           } catch (error) {
//             // 忽略不支持selection的错误
//             if (!error.message.includes('does not support selection')) {
//               console.warn('光标设置错误:', error);
//             }
//           }
//           return;
//         }
//         updateValue(e.target.value);
//       };

//       // 粘贴事件处理 - 添加负号过滤
//       const handlePaste = (e) => {
//         e.preventDefault();
//         const pasteData = e.clipboardData.getData('text/plain');
//         // 移除粘贴内容中的负号
//         const filteredData = pasteData.replace(/[^\d]/g, "");
//         updateValue(filteredData);
//       };

//       // 失去焦点事件处理
//       const handleBlur = () => updateValue(input.value);

//       // 添加事件监听
//       input.addEventListener('input', handleInput);
//       input.addEventListener('paste', handlePaste);
//       input.addEventListener('blur', handleBlur);

//       // 清理函数
//       el._positiveIntegerCleanup = () => {
//         input.removeEventListener('input', handleInput);
//         input.removeEventListener('paste', handlePaste);
//         input.removeEventListener('blur', handleBlur);
//       };
//     },

//     unbind(el) {
//       if (el._positiveIntegerCleanup) {
//         el._positiveIntegerCleanup();
//       }
//     }
//   });
// }
