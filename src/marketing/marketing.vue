<template>
  <div class="wraps food">
    <div class="menus">
      <div class="box">
        <div
          class="menus-item"
          v-for="(item, index) in menus_list"
          :class="activeIndex == index ? 'active' : ''"
          @click="clickTopMenu(index)"
          :key="index"
        >
          {{ item }}
        </div>
      </div>
    </div>
    <div class="top topp">
      <div class="menu" :class="gLang == 2 ? 'menu-zh' : ''">
        <el-tabs
          v-model="activeName"
          type="card"
          class="menu-item"
          @tab-click="clickMenu"
        >
          <el-tab-pane
            v-for="item in tables"
            :key="item.id"
            :label="
              item.foods_count == undefined || Number.isNaN(item.foods_count)
                ? (gLang == 1 ? item.name_ug : item.name_zh)
                : (gLang == 1 ? item.name_ug : item.name_zh) + ' (' + item.foods_count + ')'
            "
            :name="item.item"
            :id="item.id"
          >
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="serach">
        <div class="input">
          <span class="iconfont icon-search search"></span>
          <input
            type="text"
            oninput="if(value.length>40)value=value.slice(0,40)"
            :placeholder="gL('shortcut') + '/' + gL('names')"
            v-model="serachText"
            v-on:input="inputSearch"
            @keyup.enter="inputSearch"
          />
        </div>
        <div class="del" v-if="delete_text" @click="removeSerach">
          <span class="iconfont icon-jia-copy"></span>
        </div>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        header-cell-class-name="headers"
        :header-cell-style="{ background: '#e6e6e6' }"
        :cell-style="cell"
        row-class-name="row"
        height="100%"
        style="width: 100%"
      >
        <el-table-column
          :label="gL('serialNumber')"
          type="index"
          align="center"
          width="60"
        >
        </el-table-column>
        <el-table-column align="center" width="80px" :label="gL('img')">
          <template slot-scope="scope">
            <img
              :src="scope.row.image"
              width="50px"
              height="50px"
              alt=""
              style="border: 1px solid #cccccc"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" align="center" :label="gL('foodName')">
        </el-table-column>
        <el-table-column
          prop="shortcut_code"
          align="center"
          width="95px"
          :label="gL('shortcut')"
        >
        </el-table-column>
        <el-table-column
          prop="food_category_name"
          align="center"
          :label="gL('type')"
        >
        </el-table-column>
        <el-table-column
          prop="price"
          width="85px"
          align="center"
          :label="gL('price')"
        >
        </el-table-column>
        <el-table-column
          prop="vip_price"
          width="90px"
          align="center"
          :label="gL('vipPrice')"
        >
        </el-table-column>
        <el-table-column
          prop="cost_price"
          align="center"
          width="100px"
          :label="gL('costPrice')"
        >
        </el-table-column>
        <el-table-column
          prop="sort"
          align="center"
          width="80px"
          :label="gL('sort')"
        >
        </el-table-column>
        <el-table-column align="center" width="70" :label="gL('state')">
          <template slot-scope="scope">
            <el-switch
              active-color="#13ce66"
              inactive-color="#ff4949"
              v-model="scope.row.state == 0 ? false : true"
              @change="changeSwitch(scope.$index, scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <!-- <el-table-column
          :label="gL('operation')"
          prop="dosome"
          align="center"
          width="120px"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="iconfont icon-shiliangzhinengduixiang"
              @click="editData(scope.row)"
              circle
            ></el-button>
            <span style="padding-right: 9px; padding-left: 15px">
              <span class="line"></span>
            </span>
            <el-button
              type="text"
              class="danger"
              @click="delData(scope.row)"
              icon="iconfont icon-qingkong"
              circle
            ></el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <div class="mask" v-if="modal_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ modal_title }}</span>
          <span class="iconfont icon-jia-copy" @click="cancel(2)"></span>
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <div class="content" style="flex-direction: row">
            <div class="items">
              <div class="row">
                <el-form-item prop="food_category_id">
                  <el-select
                    v-model="ruleForm.food_category_id"
                    :placeholder="gL('foodCate')"
                  >
                    <el-option
                      v-for="(item, index) in cate_list"
                      :key="index"
                      :label="gLang == 1 ? item.name_ug : item.name_zh"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="row">
                <el-form-item prop="format_id">
                  <el-select
                    v-model="ruleForm.format_id"
                    :placeholder="gL('format')"
                  >
                    <el-option
                      v-for="(item, index) in format_list"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="row">
                <el-form-item prop="shortcut_code">
                  <el-input
                    v-model="ruleForm.shortcut_code"
                    type="number"
                    oninput="if(value.length>4)value=value.slice(0,4)"
                    :placeholder="gL('shortcutCode')"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="row">
                <el-form-item prop="name_ug">
                  <el-input
                    v-model="ruleForm.name_ug"
                    :placeholder="gL('foodName') + gL('ug')"
                    :class="gLang == 1 ? 'uy-input input-ug' : ''"
                    maxlength="40"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="row">
                <el-form-item prop="name_zh">
                  <el-input
                    v-model="ruleForm.name_zh"
                    :placeholder="gL('foodName') + gL('zh')"
                    :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                    maxlength="40"
                  ></el-input>
                </el-form-item>
              </div>
            </div>
            <div class="items">
              <div class="row inp">
                <div class="input-item">
                  <div class="lab">{{ gL("costPrice") }}</div>
                  <el-form-item prop="cost_price">
                    <div class="input">
                      <input
                        v-model="ruleForm.cost_price"
                        type="text"
                        maxlength="4"
                        class="number"
                        @input="testing('cost_price')"
                      />
                    </div>
                  </el-form-item>
                </div>
                <div class="input-item">
                  <div class="lab">{{ gL("price") }}</div>
                  <el-form-item prop="price">
                    <div class="input">
                      <input
                        v-model="ruleForm.price"
                        type="text"
                        maxlength="4"
                        class="number"
                        @input="parallel('price')"
                      />
                    </div>
                  </el-form-item>
                </div>
                <div class="input-item">
                  <div class="lab">{{ gL("vipPrice") }}</div>
                  <el-form-item prop="vip_price">
                    <div class="input">
                      <input
                        v-model="ruleForm.vip_price"
                        type="text"
                        maxlength="4"
                        class="number"
                        @input="testing('vip_price')"
                        @keyup.enter="confirm(2)"
                      />
                    </div>
                  </el-form-item>
                </div>
              </div>
              <div class="row">
                <el-form-item prop="sort">
                  <el-input
                    :placeholder="gL('sort')"
                    v-model="ruleForm.sort"
                    type="number"
                    oninput="if(value.length>3)value=value.slice(0,3)"
                  >
                    <template slot="append">{{ gL("sort") }}</template>
                  </el-input>
                </el-form-item>
              </div>
              <div class="row type">
                <el-form-item prop="image">
                  <div class="img" @click="openImg">
                    <img :src="ruleForm.image" alt="" />
                  </div>
                </el-form-item>
                <div class="checks">
                  <div
                    class="check"
                    :class="ruleForm.state == 0 ? 'active' : ''"
                    @click="ruleForm.state = 0"
                  >
                    {{ gL("off") }}
                  </div>
                  <div
                    class="check"
                    :class="ruleForm.state == 1 ? 'active' : ''"
                    @click="ruleForm.state = 1"
                  >
                    {{ gL("open") }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form>
        <div class="adds">
          <div class="btn add-btn" @click="confirm(1)" v-if="addBox">
            {{ gL("continueAdd") }}
          </div>
          <div class="btn" @click="confirm(2)" :class="!addBox ? 'edit' : ''">
            {{ gL("confirm") }}
          </div>
        </div>
      </div>
    </div>
    <div class="mask" v-if="img_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ modal_title }}</span>
          <span class="iconfont icon-jia-copy" @click="cancelImg"></span>
        </div>
        <div class="content image">
          <div class="img-item">
            <el-upload
              class="avatar-uploader"
              :action="url"
              name="image"
              :data="imageData"
              :headers="imageHeader"
              :on-success="handleAvatarSuccess"
            >
              <img v-if="ruleForm.image" :src="ruleForm.image" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
          <div
            class="img-item"
            v-for="item in img_list"
            :key="item.id"
            @click="imgClick(item.image_url, item.id)"
            id="img-item"
            :identifier="'r' + item.id"
          >
            <img :src="item.image_url" alt="" />
          </div>
        </div>
        <div class="btn" @click="confirmImg">
          {{ gL("confirm") }}
        </div>
      </div>
    </div>
    <modal
      :number_box="confirm_box"
      :modal_content="modal_content"
      :modal_title="modals_title"
      @cancel="cancels"
      @confirm="confirmBox"
    ></modal>
  </div>
</template>

<script>
import modal from "../components/modal.vue";
import { getFoodListAPI, getFoodsFormatAPI, getFoodCategoriesAPI, postFoodAddAPI, putFoodUpdateAPI, deleteFoodAPI, getFoodDefaultImageAPI } from "./../api/index.js"
var self;
export default {
  activated: function () {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.activeName = "first";
    self.activeId = "";
    self.ruleForm.food_category_id = "";
    (self.serachText = ""), //搜索内容
      (self.delete_text = ""), //搜索框删除按钮
      self.getData();
    self.getList();
    self.formatList();
    self.url = self.$getBaseStaticURL() + "upload/image";
    self.activeIndex = "";

    const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
    this.imageHeader.MerchantNo = storeInfo.merchant_no;
  },
  data() {
    return {
      gLang: 1,
      tableData: [],
      serachText: "", //搜索内容
      delete_text: "", //搜索框删除按钮
      activeId: "",
      modal_box: false,
      tables: [],
      cate_list: [],
      modal_title: this.gL("addFood"),
      modal_content: "",
      modals_title: "",
      ruleForm: {
        state: 1,
        food_category_id: "",
        shortcut_code: "",
        cost_price: "",
        vip_price: "",
        price: "",
        name_zh: "",
        name_ug: "",
        image: "",
        sort: "",
        format_id: 1,
      },
      url: "",
      rules: {
        food_format_id: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        sort: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        shortcut_code: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        food_category_id: [
          {
            required: true,
            message: this.gL("plaeseChooise"),
            trigger: "blur",
          },
        ],
        name_zh: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        name_ug: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        cost_price: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        vip_price: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        price: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        image: [
          { required: true, message: this.gL("uploadImg"), trigger: "blur" },
        ],
      },
      imageData: {
        folder: "foods",
      },
      imageHeader: {
        Authorization: "Bearer " + localStorage.getItem("token"),
        MerchantNo: "",
      },
      addBox: true,
      activeName: "first",
      img_box: false,
      confirm_box: false,
      img_list: [],
      defaultImg: 'this.src="' + require("../../static/images/defau.png") + '"', //默认图片
      format_list: [], //按斤按数量
      menus_list: [this.gL("discountFoods"), this.gL("characteristicFoods")],
      activeIndex: 0,
    };
  },
  methods: {
    clickTopMenu(e) {
      self.activeIndex = e;
      console.log(e);
    },
    cell({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 2) {
        if (self.gLang == 1) {
          return "direction: rtl";
        }
      }
    },
    //添加
    addFood() {
      self.modal_box = true;
      self.addBox = true;
      self.ruleForm.format_id = 1;
      if (self.tableData.length > 0) {
        self.ruleForm.sort =
          Math.max.apply(
            Math,
            self.tableData.map(function (o) {
              return o.sort;
            })
          ) + 1;
      }
    },
    //搜索输入框输入时候
    inputSearch() {
      if (self.serachText.length != 0) {
        self.delete_text = true;
      } else {
        self.delete_text = false;
      }
      self.getData();
    },
    //删除搜索内容
    removeSerach() {
      self.serachText = "";
      self.delete_text = false;
      self.getData();
    },
    //点击点菜
    clickMenu(e) {
      self.activeId = e.$attrs.id;
      if (e.$attrs.id != 0) {
        self.ruleForm.food_category_id = e.$attrs.id;
      }
      self.getData();
    },
    //获取数据
    getData() {
      var data = {};
      if (self.activeId != "") {
        data.food_category_id = self.activeId;
      }
      if (self.serachText != "") {
        data.keyword = self.serachText;
      }
      getFoodListAPI(data).then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.tableData = response.data.data;
        }
      });
    },
    formatList() {
      self.format_list = [];
      getFoodsFormatAPI().then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.format_list = response.data.data;
        }
      });
    },
    //获取角色列表
    getList() {
      self.cate_list = [];
      getFoodCategoriesAPI().then((response) => {
          if (response.status >= 200 && response.status < 300) {
            var count = 0;
            response.data.data.forEach((item) => {
              count += item.foods_count;
            });
            var all = {
              name_ug: self.gL("all"),
              name_zh: self.gL("all"),
              item: "first",
              id: 0,
              foods_count: count,
            };
            response.data.data.unshift(all);
            self.tables = response.data.data;
            response.data.data.forEach((item) => {
              if (item.id != 0) {
                self.cate_list.push(item);
              }
            });
          }
        });
    },
    //开关
    changeSwitch(index, row) {
      putFoodStateAPI(row.id).then((response) => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            if (row.state == 0) {
              row.state = 1;
            } else {
              row.state = 0;
            }
          }
        });
    },
    //编辑
    editData(row) {
      self.id = row.id;
      self.addBox = false;
      self.modal_box = true;
      self.ruleForm.food_category_id = row.food_category_id;
      self.ruleForm.shortcut_code = row.shortcut_code;
      self.ruleForm.cost_price = row.cost_price;
      self.ruleForm.vip_price = row.vip_price;
      self.ruleForm.state = row.state;
      self.ruleForm.price = row.price;
      self.ruleForm.name_zh = row.name_zh;
      self.ruleForm.name_ug = row.name_ug;
      self.ruleForm.sort = row.sort;
      self.ruleForm.image = row.image;
      self.ruleForm.format_id = row.food_format_id;
      self.modal_title = self.gL("editFood");
    },
    cancel(e) {
      if (e == 2) {
        self.modal_box = false;
      } else {
        self.ruleForm.sort = self.ruleForm.sort + 1;
      }
      self.ruleForm.state = 1;
      // self.ruleForm.food_category_id = '';
      self.ruleForm.shortcut_code = "";
      self.ruleForm.cost_price = "";
      self.ruleForm.vip_price = "";
      self.ruleForm.state = 1;
      self.ruleForm.price = "";
      self.ruleForm.name_zh = "";
      self.ruleForm.name_ug = "";
      self.ruleForm.image = "";
      self.ruleForm.food_format_id = 1;
      self.modal_title = self.gL("addFood");
    },
    confirm(e) {
      if (self.addBox) {
        self.$refs.ruleForm.validate((valid) => {
          if (valid) {
            console.log(self.ruleForm);
            self.ruleForm.format_id = self.ruleForm.food_format_id;
            postFoodAddAPI(slef.ruleForm).then((response) => {
                if (response.status >= 200 && response.status < 300) {
                  self.$message({
                    message: self.gL("successfulOperation"),
                    type: "success",
                    customClass: self.$toastClass(),
                    offset: 120
                  });
                  self.getData();
                  self.getList();
                  self.cancel(e);
                }
              });
          } else {
            return false;
          }
        });
      } else {
        self.$refs.ruleForm.validate((valid) => {
          if (valid) {
            putFoodUpdateAPI(self.id, self.ruleForm).then((response) => {
                if (response.status >= 200 && response.status < 300) {
                  self.$message({
                    message: self.gL("successfulOperation"),
                    type: "success",
                    customClass: self.$toastClass(),
                    offset: 120
                  });
                  self.getData();
                  self.cancel(e);
                }
              });
          } else {
            return false;
          }
        });
      }
    },
    //删除
    delData(row) {
      self.id = row.id;
      self.confirm_box = true;
      if (self.gLang == 1) {
        self.modal_content =
          "《" +
          row.name +
          "》" +
          self.gL("confirs") +
          self.gL("confirmdelete");
      } else {
        self.modal_content =
          self.gL("confirmdelete") +
          "《" +
          row.name +
          "》" +
          self.gL("confirs");
      }
      self.modals_title = self.gL("tips");
    },
    cancels() {
      self.confirm_box = false;
    },
    confirmBox() {
      self.cancels();
      self.delRowData();
    },
    delRowData() {
      deleteFoodAPI(self.id).then((response) => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            self.getData();
          }
        });
    },
    cancelImg() {
      self.img_box = false;
      self.ruleForm.image = "";
    },
    confirmImg() {
      self.img_box = false;
    },
    openImg() {
      self.img_box = true;
      getFoodDefaultImageAPI().then((response) => {
        self.img_list = response.data.data;
      });
    },
    imgClick(e, v) {
      self.ruleForm.image = e;
      // let img = document.querySelector("#img-item");
      // console.log(img);
      // img.classList.add("img-active");
      let imgs = document.querySelectorAll("#img-item");
      imgs.forEach((element) => {
        element.classList.remove("img-active");
      });
      let img = document.querySelector("#img-item[identifier=r" + v + "]");
      img.classList.add("img-active");
    },
    handleAvatarSuccess(res, file) {
      if (res.message) {
        self.ruleForm.image = res.message;
        self.img_box = false;
      } else {
        self.$message({
          message: self.gL("uploadError"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    //输入售价，同时填充会员价
    parallel(e) {
      self.testing(e);
      self.ruleForm.vip_price = self.ruleForm.price;
    },
    //验证价格
    testing(e) {
      self.ruleForm[e] = self.ruleForm[e].replace(/[^\d.]/g, ""); //先把非数字的都替换掉，除了数字和.
      self.ruleForm[e] = self.ruleForm[e].replace(/^\./g, ""); //必须保证第一个为数字而不是.
      self.ruleForm[e] = self.ruleForm[e]
        .replace(".", "$#$")
        .replace(/\./g, "")
        .replace("$#$", "."); //保证.只出现一次，而不能出现两次以上
    },
  },
  components: {
    modal,
  },
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.img-active {
  border: 3px solid #139d59 !important;
}
.img-active::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-top: 30px solid #139d59;
  border-right: 30px solid transparent;
}
.wraps {
  width: 100%;
  height: 100%;
  .menus {
    display: flex;
    background-color: @bgColor;
    padding: 5px;
    font-size: 24px;
    color: #ffffff;
    justify-content: space-between;
    border-bottom: 1px solid @grayColor;
    .box {
      display: flex;
    }
    .menus-item {
      padding: 12px 40px;
      text-align: center;
      cursor: pointer;
    }
    .active {
      background-color: @greenColor;
    }
  }
  .top {
    height: 50px;
    width: 100%;
    background-color: #2e3033;
    display: flex;
    justify-content: space-between;
    .menu {
      width: 82%;
      display: flex;
      color: #ffffff;
      height: 100%;
      // overflow-x: scroll;
      .menu-item {
        width: 100%;
        height: 100%;
        // padding: 0 25px;
        // line-height: 40px;
        font-size: 26px;
        cursor: pointer;
      }
      .active {
        background-color: #139d59;
      }
    }
    .serach {
      width: 280px;
      background: #4d4d4d;
      margin: 5px 15px 5px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .input {
        //  width: 70%;
        display: flex;
        align-items: center;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        width: 70%;
        background: #4d4d4d;
        &::placeholder {
          color: #cccccc;
        }
      }
      .search {
        color: #cccccc;
        font-size: 24px;
        padding: 0 10px;
      }
      .del {
        width: 20%;
        height: 100%;
        color: #cccccc;
        justify-content: center;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 22px;
        }
      }
    }
  }
  .table {
    height: 93%;
    overflow-y: scroll;
  }
  //s提示框
  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .box {
      background-color: #ffffff;
      width: 920px;
      font-size: 30px;
      -webkit-animation: fadelogIn 0.4s;
      animation: fadelogIn 0.4s;
      .title {
        background-color: #e6e6e6;
        color: #1a1a1a;
        padding: 25px 20px;
        position: relative;
        text-align: center;
        .iconfont {
          position: absolute;
          right: 20px;
          font-size: 23px;
          color: #666666;
          cursor: pointer;
        }
      }
      .content {
        padding: 50px 75px 0 75px;
        display: flex;
        justify-content: space-between;
        .items {
          width: 48%;
          .row {
            margin-bottom: 20px;
          }
          .inp {
            display: flex;
            font-size: 20px;
            border: 1px solid #cccccc;
            border-radius: 4px;
            .input-item {
              width: 33.333333%;
              border-right: 1px solid #cccccc;
              &:last-child {
                border-right: none;
              }
              input {
                width: 100%;
                outline: none;
                text-align: center;
              }
              .input {
                text-align: center;
                padding: 7px 0;
                font-size: 20px;
              }
              .lab {
                text-align: center;
                padding: 13px 0;
                background: #f2f2f2;
                border-bottom: 1px solid #cccccc;
              }
            }
          }
          .type {
            display: flex;
            justify-content: space-between;
            .img {
              width: 100%;
              background-image: url("../../static/images/defau.png");
              background-size: 100% 100%;
              cursor: pointer;
              border: 1px solid #cccccc;
              overflow: hidden;
              height: 120px;
              img {
                width: 170px;
                height: 120px;
              }
            }
            .checks {
              width: 48%;
              .check {
                width: 100%;
                border: 1px solid #cccccc;
                color: #666666;
                font-size: 18px;
                text-align: center;
                cursor: pointer;
                margin-bottom: 30px;
                height: 45px;
                line-height: 45px;
                border-radius: 3px;
                &:last-child {
                  margin-bottom: 0;
                }
              }
              .active {
                background-color: #139d59;
                color: #fff;
              }
            }
          }
        }
      }
      .image {
        flex-wrap: wrap;
        justify-content: left;
        .img-item {
          width: 170px;
          height: 120px;
          margin-right: 22px;
          margin-bottom: 20px;
          overflow: hidden;
          cursor: pointer;
          border: 1px solid #999;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .adds {
        display: flex;
        margin: 20px 75px 20px 75px;
        justify-content: space-between;
        .add-btn {
          background-color: #ff9c00;
        }
        .btn {
          width: 48%;
          margin: 0;
        }
        .edit {
          width: 100%;
        }
      }
      .btn {
        background: #139d59;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        padding: 15px 0;
        cursor: pointer;
        margin: 20px 75px 20px 75px;
      }
    }
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 170px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}
.avatar {
  width: 170px !important;
  height: 120px !important;
  display: block;
}
/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

@-webkit-keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
  }
}
</style>
