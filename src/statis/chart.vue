<template>
  <div class="wraps">
    <div class="top">
      <div class="search">
        <el-date-picker
          v-model="picker"
          type="datetimerange"
          :editable="false"
          range-separator="~"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          prefix-icon="el-icon-date"
          :start-placeholder="gL('startTime')"
          :end-placeholder="gL('endTime')"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="pickerBeginDateBefore"
        >
        </el-date-picker>
        <div class="btn-search" @click="search">{{ gL("serach") }}</div>
      </div>
      <div class="day-box">
        <div
          class="day-item"
          :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          :class="activeDay == item.id ? 'active' : ''"
          v-for="(item, index) in day_list"
          @click="activeDay != item.id && clickDay(item)"
          :key="index"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="chart-area">
      <el-row>
        <el-col :span="12">
          <div class="chart-container">
            <bar-chart
              ref="businessChart"
              :height="chartHeight"
              :chart-data="chartData.business"
            />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-container">
            <bar-chart
              ref="orderChart"
              :height="chartHeight"
              :chart-data="chartData.order"
            />
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="chart-container">
            <linear-chart
              ref="paymentChart"
              :height="chartHeight"
              :chart-data="chartData.payment"
            />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-container">
            <linear-chart
              ref="customerChart"
              :height="chartHeight"
              :chart-data="chartData.customer"
            />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<style lang="less" scoped>
.wraps {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  z-index: 1;
  .top {
    display: flex;
    padding: 0 20px;
    background-color: #2e3033;
    color: #ffffff;
    height: 55px;
    align-items: center;
    border-bottom: 1px solid #666666;
    justify-content: space-between;
    font-size: 22px;
    .search {
      display: flex;
      .btn-search {
        padding: 0 20px;
        background: #139d59;
        display: flex;
        align-items: center;
        margin-left: 15px;
        font-size: 22px;
        cursor: pointer;
      }
    }
    .prices {
      background: #ff9c00;
      height: 75%;
      display: flex;
      align-items: center;
      padding: 0 40px;
      cursor: pointer;
    }
    .serach {
      width: 280px;
      background: #4d4d4d;
      display: flex;
      align-items: center;
      justify-content: space-between;
      input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        width: 70%;
        background: #4d4d4d;
        &::placeholder {
          color: #cccccc;
        }
      }
      .searchs {
        color: #cccccc;
        font-size: 24px;
        padding: 0 10px;
      }
      .del {
        width: 20%;
        height: 100%;
        color: #cccccc;
        justify-content: center;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 22px;
        }
      }
    }
    .selects {
      margin-right: 25px;
    }
    .btn {
      background-color: #ff9c00;
      width: 150px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25px;
      height: 70%;
      font-size: 26px;
      cursor: pointer;
      .iconfont {
        font-size: 19px;
      }
    }
    .day-box {
      display: flex;
      justify-content: space-between;
      width: 360px;
      .day-item {
        padding: 5px 25px;
        background: #4d4d4d;
        border: 3px solid #4d4d4d;
        cursor: pointer;
      }
      .active {
        border-color: #139d59;
      }
    }
  }
  .chart-area {
    .chart-container {
      padding: 10px;
    }
  }
}
</style>
<script>
let self;
import BarChart from "../statisticComponents/BarChart.vue";
import LinearChart from "../statisticComponents/LinearChart.vue";
import moment from "moment";
import { getStatisticsGraphAPI } from "./../api/index.js"
export default {
  created() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.dateList(0);
  },
  data() {
    return {
      gLang: 1,
      chartHeight: 178,
      colorsForPaymentType: [
        "#07C160CC",
        "#1677FFCC",
        "#FF5722CC",
        "#9B22FFCC",
        "#FF2282CC",
        "#22FFF0CC",
      ],
      pickerBeginDateBefore: {
        disabledDate(time) {
          return time.getTime() >= moment().hour(23).minute(59).second(59).millisecond(999).valueOf();
        },
      },
      picker: [],
      day_list: [
        { name: this.gL("today"), id: 1, day: 0 },
        { name: this.gL("sevenDay"), id: 2, day: -7 },
        { name: this.gL("thirty"), id: 4, day: -30 },
      ],
      activeDay: 1,
      chartData: {
        business: {
          labels: [],
          datasets: [
            {
              label: this.gL('businessSales'),
              backgroundColor: "#139D59cc",
              data: [],
            },
          ],
        },
        order: {
          labels: [],
          datasets: [
            {
              label: this.gL('orderCount'),
              backgroundColor: "#FFC107cc",
              data: [],
            },
          ],
        },
        payment: {
          labels: [],
          datasets: [],
        },
        customer: {
          labels: [],
          datasets: [
            {
              label: this.gL('customerCount'),
              backgroundColor: "#CDDC39cc",
              data: [],
            },
          ],
        },
      },
    };
  },
  methods: {
    //获取今天，最近3天，最近7天
    dateList(day) {
      this.picker = [moment().add(day, 'days').format("YYYY-MM-DD") + " 00:00:00", moment().format("YYYY-MM-DD HH:mm:ss")];
      self.getData();
    },
    //点击天
    clickDay(e) {
      self.activeDay = e.id;
      self.dateList(e.day);
    },
    prepareData(data) {
      console.log(data);
      switch (data.type) {
        case 1:
          self.prepareDataForSeason(
            data.business_statistics,
            data.customer_statistics,
            data.pay_type_statistics
          );
          break;
        case 2:
          self.prepareDataForMonth(
            data.business_statistics,
            data.customer_statistics,
            data.pay_type_statistics
          );
          break;
        case 3:
          self.prepareDataForDay(
            data.business_statistics,
            data.customer_statistics,
            data.pay_type_statistics
          );
          break;
        default:
          break;
      }
    },
    prepareDataForDay(business, customer, payment) {
      // 3
      let dates = self.getHours(
        new Date(self.picker[0]),
        new Date(self.picker[1])
      );

      self.chartData.business.labels = dates;
      self.chartData.order.labels = dates;
      self.chartData.customer.labels = dates;
      self.chartData.payment.labels = dates;

      let businessFineData = [];
      let orderFineData = [];
      let customerFineData = [];
      let paymentFineData = [];

      let paymentTypes = [];

      for (let i = 0; i < business.length; i++) {
        businessFineData[business[i].day] = business[i].real_amount;
      }

      for (let i = 0; i < customer.length; i++) {
        orderFineData[customer[i].day_paid_at] = customer[i].day_order_count;
        customerFineData[customer[i].day_paid_at] =
          customer[i].day_customers_count;
      }
      self.chartData.payment.datasets = [];
      for (let i = 0; i < payment.length; i++) {
        if (!self.$in_array(payment[i].payment_type_id, paymentTypes)) {
          paymentTypes.push(payment[i].payment_type_id);
          self.chartData.payment.datasets.push({
            label: payment[i].name,
            backgroundColor: self.colorsForPaymentType[paymentTypes.length - 1],
            data: [],
          });
          paymentFineData[paymentTypes.length - 1] = [];
          paymentFineData[paymentTypes.length - 1][payment[i].day] =
            payment[i].daily_price;
        } else {
          let dataSetIndex = paymentTypes.indexOf(payment[i].payment_type_id);
          paymentFineData[dataSetIndex][payment[i].day] =
            payment[i].daily_price;
        }
      }

      let businessDataValues = [];
      let orderDataValues = [];
      let customerDataValues = [];
      let paymentDataValues = [];
      for (let i = 0; i < dates.length; i++) {
        businessDataValues.push(
          businessFineData[dates[i]] === undefined
            ? 0
            : businessFineData[dates[i]]
        );
        if (orderFineData[dates[i]] === undefined) {
          orderDataValues.push(0);
          customerDataValues.push(0);
        } else {
          orderDataValues.push(orderFineData[dates[i]]);
          customerDataValues.push(customerFineData[dates[i]]);
        }
        for (let j = 0; j < paymentFineData.length; j++) {
          if (paymentDataValues[j] === undefined) {
            paymentDataValues[j] = [];
          }

          if (paymentFineData[j][dates[i]] === undefined) {
            paymentDataValues[j].push(0);
          } else {
            paymentDataValues[j].push(paymentFineData[j][dates[i]]);
          }
        }
      }

      self.chartData.business.datasets[0].data = businessDataValues;
      self.chartData.order.datasets[0].data = orderDataValues;
      self.chartData.customer.datasets[0].data = customerDataValues;
      for (let j = 0; j < paymentDataValues.length; j++) {
        self.chartData.payment.datasets[j].data = paymentDataValues[j];
      }

      self.$refs.businessChart.refresh();
      self.$refs.orderChart.refresh();
      self.$refs.customerChart.refresh();
      self.$refs.paymentChart.refresh();
    },
    prepareDataForMonth(business, customer, payment) {
      // 2
      let dates = self.getDates(
        new Date(self.picker[0]),
        new Date(self.picker[1])
      );

      self.chartData.business.labels = dates;
      self.chartData.order.labels = dates;
      self.chartData.customer.labels = dates;
      self.chartData.payment.labels = dates;

      let businessFineData = [];
      let orderFineData = [];
      let customerFineData = [];
      let paymentFineData = [];

      let paymentTypes = [];

      for (let i = 0; i < business.length; i++) {
        businessFineData[business[i].day] = business[i].real_amount;
      }

      for (let i = 0; i < customer.length; i++) {
        orderFineData[customer[i].day_paid_at] = customer[i].day_order_count;
        customerFineData[customer[i].day_paid_at] =
          customer[i].day_customers_count;
      }
      self.chartData.payment.datasets = [];
      for (let i = 0; i < payment.length; i++) {
        if (!self.$in_array(payment[i].payment_type_id, paymentTypes)) {
          paymentTypes.push(payment[i].payment_type_id);
          self.chartData.payment.datasets.push({
            label: payment[i].name,
            backgroundColor: self.colorsForPaymentType[paymentTypes.length - 1],
            data: [],
          });
          paymentFineData[paymentTypes.length - 1] = [];
          paymentFineData[paymentTypes.length - 1][payment[i].day] =
            payment[i].daily_price;
        } else {
          let dataSetIndex = paymentTypes.indexOf(payment[i].payment_type_id);
          paymentFineData[dataSetIndex][payment[i].day] =
            payment[i].daily_price;
        }
      }

      let businessDataValues = [];
      let orderDataValues = [];
      let customerDataValues = [];
      let paymentDataValues = [];
      for (let i = 0; i < dates.length; i++) {
        businessDataValues.push(
          businessFineData[dates[i]] === undefined
            ? 0
            : businessFineData[dates[i]]
        );
        if (orderFineData[dates[i]] === undefined) {
          orderDataValues.push(0);
          customerDataValues.push(0);
        } else {
          orderDataValues.push(orderFineData[dates[i]]);
          customerDataValues.push(customerFineData[dates[i]]);
        }
        for (let j = 0; j < paymentFineData.length; j++) {
          if (paymentDataValues[j] === undefined) {
            paymentDataValues[j] = [];
          }

          if (paymentFineData[j][dates[i]] === undefined) {
            paymentDataValues[j].push(0);
          } else {
            paymentDataValues[j].push(paymentFineData[j][dates[i]]);
          }
        }
      }

      self.chartData.business.datasets[0].data = businessDataValues;
      self.chartData.order.datasets[0].data = orderDataValues;
      self.chartData.customer.datasets[0].data = customerDataValues;
      for (let j = 0; j < paymentDataValues.length; j++) {
        self.chartData.payment.datasets[j].data = paymentDataValues[j];
      }

      self.$refs.businessChart.refresh();
      self.$refs.orderChart.refresh();
      self.$refs.customerChart.refresh();
      self.$refs.paymentChart.refresh();
    },
    prepareDataForSeason(business, customer, payment) {
      // 1
      let dates = self.getMonth(
        new Date(self.picker[0]),
        new Date(self.picker[1])
      );


console.log(dates)
      self.chartData.business.labels = dates;
      self.chartData.order.labels = dates;
      self.chartData.customer.labels = dates;
      self.chartData.payment.labels = dates;

      let businessFineData = [];
      let orderFineData = [];
      let customerFineData = [];
      let paymentFineData = [];

      let paymentTypes = [];

      for (let i = 0; i < business.length; i++) {
        businessFineData[business[i].day] = business[i].real_amount;
      }

      for (let i = 0; i < customer.length; i++) {
        orderFineData[customer[i].day_paid_at] = customer[i].day_order_count;
        customerFineData[customer[i].day_paid_at] =
          customer[i].day_customers_count;
      }
      self.chartData.payment.datasets = [];
      for (let i = 0; i < payment.length; i++) {
        if (!self.$in_array(payment[i].payment_type_id, paymentTypes)) {
          paymentTypes.push(payment[i].payment_type_id);
          self.chartData.payment.datasets.push({
            label: payment[i].name,
            backgroundColor: self.colorsForPaymentType[paymentTypes.length - 1],
            data: [],
          });
          paymentFineData[paymentTypes.length - 1] = [];
          paymentFineData[paymentTypes.length - 1][payment[i].day] =
            payment[i].daily_price;
        } else {
          let dataSetIndex = paymentTypes.indexOf(payment[i].payment_type_id);
          paymentFineData[dataSetIndex][payment[i].day] =
            payment[i].daily_price;
        }
      }

      let businessDataValues = [];
      let orderDataValues = [];
      let customerDataValues = [];
      let paymentDataValues = [];
      for (let i = 0; i < dates.length; i++) {
        businessDataValues.push(
          businessFineData[dates[i]] === undefined
            ? 0
            : businessFineData[dates[i]]
        );
        if (orderFineData[dates[i]] === undefined) {
          orderDataValues.push(0);
          customerDataValues.push(0);
        } else {
          orderDataValues.push(orderFineData[dates[i]]);
          customerDataValues.push(customerFineData[dates[i]]);
        }
        for (let j = 0; j < paymentFineData.length; j++) {
          if (paymentDataValues[j] === undefined) {
            paymentDataValues[j] = [];
          }

          if (paymentFineData[j][dates[i]] === undefined) {
            paymentDataValues[j].push(0);
          } else {
            paymentDataValues[j].push(paymentFineData[j][dates[i]]);
          }
        }
      }

      self.chartData.business.datasets[0].data = businessDataValues;
      self.chartData.order.datasets[0].data = orderDataValues;
      self.chartData.customer.datasets[0].data = customerDataValues;
      for (let j = 0; j < paymentDataValues.length; j++) {
        self.chartData.payment.datasets[j].data = paymentDataValues[j];
      }

      self.$refs.businessChart.refresh();
      self.$refs.orderChart.refresh();
      self.$refs.customerChart.refresh();
      self.$refs.paymentChart.refresh();
    },
    getDates(startTime, endTime) {
      let date_all = [];
      let i = 0;
      while (endTime.getTime() - startTime.getTime() >= 0) {
        let year = startTime.getFullYear();
        let month = startTime.getMonth() + 1;
        let day = startTime.getDate();
        if (month < 10) {
          month = "0" + month;
        }
        if (day < 10) {
          day = "0" + day;
        }
        date_all[i] = year + "-" + month + "-" + day;
        startTime.setDate(startTime.getDate() + 1);
        i += 1;
      }
      return date_all;
    },
    getHours(startTime, endTime) {
      let hour_all = [];
      let i = 0;
      while (endTime.getTime() - startTime.getTime() >= 0) {
        let year = startTime.getFullYear();
        let month = startTime.getMonth() + 1;
        let day = startTime.getDate();
        let hour = startTime.getHours();
        if (month < 10) {
          month = "0" + month;
        }
        if (day < 10) {
          day = "0" + day;
        }
        if (hour < 10) {
          hour = "0" + hour;
        }
        hour_all[i] = year + "-" + month + "-" + day + " "+ hour;
        startTime.setHours(startTime.getHours() + 1);
        i += 1;
      }
      return hour_all;
    },
    getMonth(startTime, endTime) {
      let date_all = [];
      let i = 0;
      while (endTime.getTime() - startTime.getTime() >= 0) {
        let year = startTime.getFullYear();
        let month = startTime.getMonth() + 1;
        if (month < 10) {
          month = "0" + month;
        }
        date_all[i] = year + "-" + month;
        startTime.setMonth(startTime.getMonth() + 1);
        i += 1;
      }
      return date_all;
    },
    getData() {
      let data = {
        begin_at: self.picker[0],
        end_at: self.picker[1],
      };
      let loading = this.$loading();
      getStatisticsGraphAPI(data).then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.prepareData(response.data.data);
        }
      }).finally(() => {
        loading.close();
      });
    },
    search() {
      if (self.picker != null) {
        self.getData();
      } else {
        self.$message({
          message: self.gL("pleaceChooiseTime"),
          type: "error",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
  },
  components: {
    BarChart,
    LinearChart,
  },
};
</script>
