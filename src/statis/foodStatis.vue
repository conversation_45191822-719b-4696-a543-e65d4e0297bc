<template>
    <div class="wraps staff">
        <div class="top">
            <div class="search">
                <el-date-picker
                    v-model="picker"
                    type="datetimerange"
                    :editable="false"
                    range-separator="~"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyy-MM-dd HH:mm:ss"
                    prefix-icon="el-icon-date"
                    :start-placeholder="gL('startTime')"
                    :end-placeholder="gL('endTime')"
                    :picker-options="pickerBeginDateBefore"
                    :default-time="['00:00:00', '23:59:59']">
                </el-date-picker>
                <div class="btn-search" @click="serach">{{gL('serach')}}</div>
            </div>

            <div
              class="prices"
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
              @click="printFoodStatistic"
            >
              {{ gL("print") }}
            </div>
            <!-- <div class="print" @click="printFoodStatistic">
              <span class="print-icon iconfont icon-dayinji-"></span>
            </div> -->
        </div>
        <div class="top">
          <div class="day-box">
                <div class="day-item" :style="{direction:gLang==2?'ltr':'rtl'}" :class="activeDay==item.id?'active':''" v-for="(item,index) in day_list" @click="() => activeDay != item.id && clickDay(item)" :key="index">{{item.name}}</div>
          </div>
        </div>
        <!-- 总览 -->
        <div class="price-list" v-if="tableData.food_category_proportion">
            <div class="price-item" v-for="(item,index) in tableData.food_category_proportion" :key="index">
              <div class="num">{{$toMoney(item.amount)}}</div>
              <div class="text">{{ gLang == 1 ? item.category_name_ug : item.category_name_zh }}</div>
            </div>
        </div>
        <!-- 美食列表 -->
        <div class="table-box">
        <div class='table-list'  v-for="(item,index) in tableData.list" :key="index">
          <template>
            <el-table
              :data="item.foods_list"
              :header-cell-style="{background:'#e6e6e6'}"
              :row-style="{background:'#f2f2f2'}"
              :row-class-name="rows"
              :cell-style="cell"
              :header-cell-class-name="cells"
              border
              style="width: 100%">
              <el-table-column
                prop="name"
                align="center"
                width="320"
                :label="item.category_name">
              </el-table-column>
              <el-table-column
                prop="price"
                align="center"
                :label="gL('price')">
              </el-table-column>
              <el-table-column
                prop="count"
                align="center"
                :label="gL('count')">
              </el-table-column>
              <el-table-column
                prop="real_amount"
                align="center"
                :label="gL('amountCollected')">
              </el-table-column>
              <el-table-column
                prop="cost_amount"
                align="center"
                :label="gL('costPrice')">
              </el-table-column>
              <el-table-column
                prop="discount_amount"
                align="center"
                :label="gL('discount')">
              </el-table-column>
              <el-table-column
                prop="diposit_amount"
                align="center"
                :label="gL('profit')">
              </el-table-column>
            </el-table>
          </template>
        </div>
        </div>
    </div>
</template>

<script>
var self;
import moment from "moment";
import { getStatisticsFoodsAPI, getBillPaymentTypesListAPI, getStaffUsersAPI, getBillDetailAPI } from "./../api/index.js"
export default {
  created: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.dateList(0);
    // self.getData();
    self.getPayTypeList();
    // self.getUserList();
  },
  data() {
    return {
        gLang:1,
        tableData:[],
        pickerBeginDateBefore:{
          disabledDate(time){
            return time.getTime() >= moment().hour(23).minute(59).second(59).millisecond(999).valueOf();
          }
        },
        modal_title:this.gL('tableNumber'),
        payList:[],
        pay_id:'',
        userList:[],
        user_id:'',
        picker:[],
        row_data:[],
        modal_box:false,
        day_list:[
            {name:this.gL('today'),id:1,day:0},
            {name:this.gL('sevenDay'),id:2,day:-7},
            {name:this.gL('fifteen'),id:3,day:-15},
            {name:this.gL('thirty'),id:4,day:-30}
          ],
        activeDay:1,
        amount:0,
        sales:0,
        order_id:'',
        width:"",
        printerKey: false
    };
  },
  methods: {
    printFoodStatistic(){
      let disablePrint = localStorage.getItem('disablePrint') == 'true'
      if (disablePrint) {
        this.$message({
          message: this.gL('disablePrinter'),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (this.printerKey) {
        this.$message({
          message: this.gL('etcFiveSecond'),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (this.picker === null || this.picker.length !== 2) {
        this.$message({
          message: this.gL('pleaceChooiseTime'),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      if (storeInfo == "" || storeInfo == null) {
        this.$message({
          message: this.gL("noUserInfo"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      this.$message({
        message: this.gL("sendPrinter"),
        type: "success",
        customClass: this.$toastClass(),
        offset: 120
      });

      const data = {
        action: "美食统计",
        time: moment().format("YYYY-MM-DD HH:mm:ss"),
        merchant_no: storeInfo.merchant_no,
        order: {
          merchant: this.tableData.merchant,
          begin_date: this.tableData.begin_date,
          end_date: this.tableData.end_date,
          food_category_proportion: this.tableData.food_category_proportion,
          list: this.tableData.list,
          printers: {
            ...this.tableData.printers,
            lang: parseInt(this.gLang)
          }
        }
      }

      this.printerKey = true;
      setTimeout(() => {
        this.printerKey = false;
      }, 5000)
      console.log("data", data);

      this.$bus.$emit("customPrint", data)
      window.electronAPI && window.electronAPI.customPrint(data);
    },
    cell({row, column, rowIndex, columnIndex}) {
      if(columnIndex == 0){
        if(self.gLang==1){
          return 'direction: rtl'
        }
      }
    },
    cells({row, column, rowIndex, columnIndex}) {
      if(columnIndex == 0){
        return 'food-name'
      }
    },
    rows({row, column, rowIndex, columnIndex}) {
      if(rowIndex == 0){
        return 'grenn-num'
      }
    },
    //获取今天，最近3天，最近7天
    dateList(day){
      this.picker = [moment().add(day, 'days').format("YYYY-MM-DD") + " 00:00:00", moment().format("YYYY-MM-DD HH:mm:ss")];
      self.getData();
   },
    //获取数据
    getData(){
      var data = {
        begin_at:self.picker[0],
        end_at:self.picker[1],
      }
      let loading = this.$loading();
      getStatisticsFoodsAPI(data).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.tableData = response.data.data;
        }
      }).finally(() => {
        loading.close();
      });
    },
    //获取数据
    getPayTypeList(){
      getBillPaymentTypesListAPI().then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.payList = response.data.data;
        }
      });
    },
    //获取数据
    getUserList(){
      getStaffUsersAPI({
           role_id:2
       }).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.userList = response.data.data;
        }
      });
    },
    //点击天
    clickDay(e){
        self.activeDay = e.id;
        self.dateList(e.day);
    },
    //切换列表
    changeSlecet(){
        self.getData();
    },
    //切换列表
    serach(){
      if(self.picker!=null){
        self.getData();
      }else{
        self.$message({
          message:self.gL('pleaceChooiseTime'),
          type: "error",
          customClass:self.$toastClass(),
          offset: 120
        });
      }
    },
    //订单详情
    rowClick(row){
      self.modal_box = true;
      self.order_id = row.order_id;
      getBillDetailAPI(row.order_id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.row_data = response.data.data;
        }
      });
    },
    // invoice(){
    //   self.$post('bill/tax/'+self.order_id).then((response) => {
    //     if(response.status >= 200 && response.status < 300){
    //       self.$message({
    //         message:self.gL('successfulOperation'),
    //         type: "success",
    //         customClass:self.$toastClass()
    //       });
    //       }
    //     }).catch(err => {
    //       if(err.response.status>=400){
    //         if(err.response.status==401){
    //           self.$router.push({ path: "/login" });
    //           self.$message({
    //             message:err.response.data.message,
    //             type: "error",
    //             customClass:self.$toastClass()
    //           });
    //         }else{
    //           self.$message({
    //             message:err.response.data.message,
    //             type: "error",
    //             customClass:self.$toastClass()
    //           });
    //         }
    //       }
    //   });
    // },
  }
};
</script>

<style lang="less" scoped>
.wraps{
    width: 100%;
    height: 100%;
    // overflow-y: scroll;
    z-index: 1;
     .top{
        display: flex;
        padding:0 20px;
        background-color: #2e3033;
        color:#ffffff;
        height: 55px;
        align-items: center;
        border-bottom: 1px solid #666666;
        justify-content: space-between;
        font-size:22px;
        .search{
            display: flex;
            .btn-search{
                padding: 0 20px;
                background: #139d59;
                display: flex;
                align-items: center;
                margin-left: 15px;
                font-size: 22px;
                cursor: pointer;
            }
        }
        .prices{
          background:#ff9c00;
          height: 75%;
          display: flex;
          align-items: center;
          padding: 0 40px;
          cursor: pointer;
        }
    .serach{
       width: 280px;
       background: #4d4d4d;
       display: flex;
       align-items: center;
       justify-content: space-between;
       .input{
        //  width: 70%;
       }
       input{
         outline: none;
         font-size: 22px;
         color:#ffffff;
         width: 70%;
         background: #4d4d4d;
         &::placeholder{
             color:#cccccc;
         }
       }
       .searchs{
         color:#cccccc;
         font-size: 24px;
         padding: 0 10px;
       }
       .del{
         width: 20%;
         height: 100%;
        color: #cccccc;
         justify-content: center;
         display: flex;
         align-items: center;
         cursor: pointer;
         .iconfont{
             font-size: 22px;
         }
       }
     }
     .selects{
         margin-right: 25px;
     }
     .btn{
        background-color: #ff9c00;
        width: 150px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 25px;
        height: 70%;
        font-size: 26px;
        cursor: pointer;
        .iconfont{
            font-size: 19px;
        }
      }
      .day-box{
          display: flex;
          justify-content: space-between;
          width: 470px;
          .day-item{
              padding: 5px 25px;
              background: #4d4d4d;
              border:3px solid #4d4d4d;
              cursor: pointer;
          }
          .active{
              border-color:#139d59;
          }
      }
      .print{
        background: #139d59;
        padding: 5px;
        cursor: pointer;
        .print-icon{
          font-size: 30px;
        }
      }
    }
    .table-box{
      height: 74%;
      overflow-y: scroll;
    }
    .table-list{
      margin-top: 20px;
      &:last-child {
        margin-bottom: 20px;
      }
    }
    .price-list{
      display: flex;
      padding:0 20px;
      background-color: #2e3033;
      color:#ffffff;
      align-items: center;
      border-bottom: 1px solid #666666;
      justify-content: flex-start;
      overflow-x: auto;
      font-size:20px;
      .price-item{
        flex-shrink: 0;
        flex-grow: 0;
        text-align: center;
        width: 140px;
        padding: 15px 0px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .text{
          color:#e6e6e6;
          padding-top: 10px;
          white-space: nowrap;
        }
        .num{
          font-size:30px;
          color:#ff9c00;
          font-weight: bold;
        }
      }
    }
    .vip{
      background-color: #f2f2f2;
    }
    .table{
      height: 86%;
      overflow-y: scroll;
    }
  }
  @media screen and (max-width: 1366px) {
    .wraps {
      .table-box{
        height: 78%;
      }
    }
  }
</style>
