<template>
  <div class="wraps staff">
    <div class="top">
      <div class="search">
        <el-date-picker
          v-model="picker"
          type="datetimerange"
          :editable="false"
          range-separator="~"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          prefix-icon="el-icon-date"
          :start-placeholder="gL('startTime')"
          :end-placeholder="gL('endTime')"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="pickerBeginDateBefore"
        >
        </el-date-picker>
        <div class="btn-search" @click="serach">{{ gL("serach") }}</div>
        <div class="search" style="margin-left: 15px">
          <div class="select">
            <el-select
              v-model="user_id"
              clearable
              filterable
              :placeholder="gL('cashier')"
              @change="changeSlecet"
            >
              <el-option
                v-for="item in userList"
                :key="item.id"
                :label="gLang == 1 ? item.name_ug : item.name_zh"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="day-box">
        <div
          class="day-item"
          :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          :class="activeDay == item.id ? 'active' : ''"
          v-for="(item, index) in day_list"
          @click="() => activeDay != item.id && clickDay(item)"
          :key="index"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="custom-box">
      <!-- 收银员 -->
      <div v-for="(item, index) in tableData" :key="index">
        <div class="customer_list">
          <div class="customer_item">
            <div class="text" style="background: #666666; color: #ffffff">
              {{ item.small_aggregate.name }}
            </div>
            <div class="num"></div>
          </div>
          <div class="customer_item">
            <div class="text">{{ gL("frequency") }}</div>
            <div class="num">{{ item.small_aggregate.shift_count }}</div>
          </div>
          <div class="customer_item">
            <div
              class="text"
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            >
              {{ gL("totalWorking") }}
            </div>
            <div class="num">{{ item.small_aggregate.working_hours }}</div>
          </div>
          <div class="customer_item">
            <div class="text">{{ gL("totalTransaction") }}</div>
            <div class="num">
              {{ $toMoney(item.small_aggregate.total_amount) }}
            </div>
          </div>
        </div>
        <!-- 美食列表 -->
        <div class="table-list">
          <template>
            <el-table
              :data="item.list"
              :header-cell-style="{ background: '#e6e6e6' }"
              header-cell-class-name="table-header"
              :row-style="{ background: '#f2f2f2' }"
              border
              style="width: 100%"
            >
              <el-table-column
                align="center"
                width="310"
                :label="gL('workShift')"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.start_at }}</span>
                  <span style="font-family: auto">~</span>
                  <span>{{ scope.row.leave_at }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                align="center"
                :label="gL('workShifts')"
              >
              </el-table-column>
              <el-table-column align="center" :label="gL('reserveFund')">
                <template slot-scope="scope">
                  <span>{{ $toMoney(scope.row.alternate_amount) }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" :label="gL('workBalance')">
                <template slot-scope="scope">
                  <span>{{ $toMoney(scope.row.working_balance) }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" :label="gL('totalTransaction')">
                <template slot-scope="scope">
                  <span>{{ $toMoney(scope.row.paid_amount) }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" :label="gL('actualAmounts')">
                <template slot-scope="scope">
                  <span>{{ $toMoney(scope.row.submitted_amount) }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" :label="gL('operation')">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    style="padding: 0"
                    icon="iconfont icon-dayinji-"
                    @click="print(scope.row)"
                    circle
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>
      </div>
      <div class="empty" v-if="tableData.length == 0">
        <span>{{ gL("noData") }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import { getStatisticsHandoverAPI, getStaffUsersAPI } from "./../api/index.js"
var self;
export default {
  created: function () {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.dateList(1);
    // self.getData();
    self.getUserList();
  },
  data() {
    return {
      gLang: 1,
      tableData: [],
      pickerMinDate: null,
      pickerBeginDateBefore: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && this.pickerMinDate) {
            this.pickerMinDate = null;
          } else if (minDate) {
            this.pickerMinDate = minDate.getTime();
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            const month = 1000 * 60 * 60 * 24 * 30;
            let maxTime = this.pickerMinDate + month;
            let minTime = this.pickerMinDate - month;
            return time.getTime() > maxTime || time.getTime() < minTime || time.getTime() >= moment().hour(23).minute(59).second(59).millisecond(999).valueOf();
          } else {
            return time.getTime() >= moment().hour(23).minute(59).second(59).millisecond(999).valueOf();
          }
        },
      },
      modal_title: this.gL("tableNumber"),
      payList: [],
      pay_id: "",
      userList: [],
      user_id: "",
      picker: [],
      row_data: [],
      modal_box: false,
      day_list: [
        { name: this.gL("sevenDay"), id: 1, day: 1 },
        { name: this.gL("thisMonth"), id: 2, day: 2 },
      ],
      activeDay: 1,
      amount: 0,
      sales: 0,
      order_id: "",
      width: "",
      printerInfo: null,
      printerKey: false
    };
  },
  methods: {
    //获取今天，最近3天，最近7天
    dateList(day) {
      if (day == 1) {
        this.picker = [moment().add(-7, 'days').format("YYYY-MM-DD") + " 00:00:00", moment().format("YYYY-MM-DD HH:mm:ss")];
      } else {
        console.log("day", moment().days());
        this.picker = [moment().format("YYYY-MM") + "-01 00:00:00", moment().format("YYYY-MM-DD HH:mm:ss")];
      }

      self.getData();
    },
    //打印
    print(row) {
      let disablePrint = localStorage.getItem('disablePrint') == 'true'
      if (disablePrint) {
        this.$message({
          message: this.gL('disablePrinter'),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (this.printerKey) {
        this.$message({
          message: this.gL('etcFiveSecond'),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      if (storeInfo == "" || storeInfo == null) {
        this.$message({
          message: this.gL("noUserInfo"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      if (this.printerInfo == null) {
        this.$message({
          message: self.gL("noPrinterInfo"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      this.$message({
        message: this.gL("sendPrinter"),
        type: "success",
        customClass: this.$toastClass(),
        offset: 120
      });

      const data = {
        action: "换班",
        time: moment().format("YYYY-MM-DD HH:mm:ss"),
        merchant_no: storeInfo.merchant_no,
        order: {
          merchant: {
            name_ug: storeInfo.merchant_name_ug,
            name_zh: storeInfo.merchant_name_zh,
          },
          start_at: row.start_at,
          leave_at: row.leave_at,
          alternate_amount: row.alternate_amount,
          working_balance: row.working_balance,
          submitted_amount: row.submitted_amount,
          paid_amount: row.paid_amount,
          total_payment_amount: row.total_payment_amount,
          printers: {...this.printerInfo, lang: this.gLang},
          paymentProportion: row.paymentProportion,
        }
      }
      console.log("data -> ", data);

      this.printerKey = true;
      setTimeout(() => {
        this.printerKey = false;
      }, 5000)

      this.$bus.$emit("customPrint", data)
      window.electronAPI && window.electronAPI.customPrint(data);
    },
    //获取数据
    getData() {
      var data = {
        begin_at: self.picker[0],
        end_at: self.picker[1],
      };
      if (self.user_id != "") {
        data.user_id = self.user_id;
      }
      let loading = this.$loading();
      getStatisticsHandoverAPI(data).then((response) => {
          if (response.status >= 200 && response.status < 300) {
            self.tableData = response.data.data;
            this.printerInfo = response.data.data.printers;
          }
        })
        .catch((err) => {
          if (err.response.status == 400) {
            self.tableData = [];
          }
        }).finally(() => {
          loading.close();
        });
    },
    //获取数据
    getUserList() {
      getStaffUsersAPI().then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.userList = response.data.data;
        }
      });
    },
    //点击天
    clickDay(e) {
      self.activeDay = e.id;
      self.dateList(e.day);
    },
    //切换列表
    changeSlecet() {
      self.getData();
    },
    //切换列表
    serach() {
      if (self.picker != null) {
        self.getData();
      } else {
        self.$message({
          message: self.gL("pleaceChooiseTime"),
          type: "error",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    }
  },
};
</script>

<style lang="less" scoped>
.wraps {
  width: 100%;
  height: 100%;
  // overflow-y: scroll;
  z-index: 1;
  .top {
    display: flex;
    padding: 0 20px;
    background-color: #2e3033;
    color: #ffffff;
    height: 55px;
    align-items: center;
    border-bottom: 1px solid #666666;
    justify-content: space-between;
    font-size: 22px;
    .search {
      display: flex;
      .btn-search {
        padding: 0 20px;
        background: #139d59;
        display: flex;
        align-items: center;
        margin-left: 15px;
        font-size: 22px;
        cursor: pointer;
      }
    }
    .prices {
      background: #ff9c00;
      height: 75%;
      display: flex;
      align-items: center;
      padding: 0 40px;
      cursor: pointer;
    }
    .serach {
      width: 280px;
      background: #4d4d4d;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .input {
        //  width: 70%;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        width: 70%;
        background: #4d4d4d;
        &::placeholder {
          color: #cccccc;
        }
      }
      .searchs {
        color: #cccccc;
        font-size: 24px;
        padding: 0 10px;
      }
      .del {
        width: 20%;
        height: 100%;
        color: #cccccc;
        justify-content: center;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 22px;
        }
      }
    }
    .selects {
      margin-right: 25px;
    }
    .btn {
      background-color: #ff9c00;
      width: 150px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25px;
      height: 70%;
      font-size: 26px;
      cursor: pointer;
      .iconfont {
        font-size: 19px;
      }
    }
    .day-box {
      display: flex;
      justify-content: space-between;
      width: 220px;
      .day-item {
        padding: 5px 25px;
        background: #4d4d4d;
        border: 3px solid #4d4d4d;
        cursor: pointer;
      }
      .active {
        border-color: #139d59;
      }
    }
  }
  .custom-box {
    height: 93%;
    overflow-y: scroll;
  }
  .customer_list {
    display: flex;
    font-size: 22px;
    margin-top: 20px;
    background-color: #f2f2f2;
    .customer_item {
      text-align: center;
      // border-right:1px solid #cccccc;
      width: 25%;
      &:last-child {
        border-right: none;
      }
      .text {
        background-color: #e6e6e6;
        padding: 15px 0;
      }
      .num {
        padding: 15px 0;
        background-color: #f2f2f2;
        color: #139d59;
        font-weight: bold;
      }
    }
  }
  .table-list {
    //   margin-top: 20px;
  }
  .vip {
    background-color: #f2f2f2;
  }
  .table {
    height: 86%;
    overflow-y: scroll;
  }
  .empty {
    font-size: 26px;
    height: 90%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
