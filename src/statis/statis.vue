<template>
	<div class="wrap">
    <div class="menus">
        <div class="menus-item" v-for="(item,index) in menus_list" :class="activeIndex==index?'active':''" @click="clickMenu(index)" :key="index">{{item}}</div>
    </div>
    <!-- 餐桌 -->
    <div class="table">
        <report v-if="activeIndex==0"></report>
        <foodStatis v-if="activeIndex==1"></foodStatis>
        <handOver v-if="activeIndex==2"></handOver>
        <chart v-if="activeIndex==3"></chart>
    </div>
    <!-- 餐桌状态 -->
	</div>
</template>

<script>
import report from '../statis/report.vue'
// import bill from '../statis/bill.vue'
import foodStatis from '../statis/foodStatis.vue'
import handOver from '../statis/handOver.vue'
import chart from '../statis/chart.vue'
var self;
export default {
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.activeIndex=0;
  },
  data() {
    return {
      gLang:1,
      menus_list:[this.gL('businessReport'),this.gL('timeDish'),this.gL('shiftRecord'),this.gL('statisticChart')],
      activeIndex:0,
    };
  },
  methods: {
    //删除搜索内容
    clickMenu(e){
        self.activeIndex = e;
    },

  },
   components:{
    report,
    foodStatis,
    handOver,
    chart
  }
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.empty{
  margin: 0 auto;
}
 .wrap{
    height: 100%;
    width: 100%;
   //菜单和输入框
   .menus{
       display: flex;
       background-color: @bgColor;
       padding: 5px;
       font-size: 26px;
       color:#ffffff;
        border-bottom: 1px solid @grayColor;
        .menus-item{
            padding: 12px 40px;
            text-align: center;
            cursor: pointer;
        }
        .active{
            background-color: @greenColor;
        }
   }
   .table{
     height: 93%;
     overflow: hidden;
   }
}
 @media screen and (max-width: 1366px) {
    .wrap {
      .table{
        height: 91%;
      }
    }
  } 
</style>
