<template>
  <div class="wraps staff">
    <div class="top">
      <div class="search">
        <el-date-picker
          v-model="picker"
          type="datetimerange"
          :editable="false"
          range-separator="~"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          prefix-icon="el-icon-date"
          :start-placeholder="gL('startTime')"
          :end-placeholder="gL('endTime')"
          :picker-options="pickerBeginDateBefore"
          :default-time="['00:00:00', '23:59:59']"
        >
        </el-date-picker>
        <div class="btn-search" @click="serach">{{ gL("serach") }}</div>
      </div>
      <div
        class="prices"
        :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
        @click="print"
      >
        {{ gL("print") }}
      </div>
    </div>
    <div class="top" style="justify-content:flex-start">
      <div class="search" style="justify-content:space-between">
        <!-- <div class="select">
                    <el-select v-model="pay_id"  clearable filterable :placeholder="gL('paymentMethod')" @change="changeSlecet">
                        <el-option
                          v-for="item in payList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id">
                        </el-option>
                    </el-select>
                </div> -->
        <div class="select selects">
          <el-select
            v-model="user_id"
            clearable
            filterable
            :placeholder="gL('staff')"
            @change="changeSlecet"
          >
            <el-option
              v-for="item in userList"
              :key="item.user_id"
              v-if="item.user_id != 1"
              :label="gLang == 1 ? item.name_ug : item.name_zh"
              :value="item.user_id"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="day-box">
        <div
          class="day-item"
          :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          :class="activeDay == item.id ? 'active' : ''"
          v-for="(item, index) in day_list"
          @click="() => activeDay != item.id && clickDay(item)"
          :key="index"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <!-- 总览 -->
    <div class="price-list" v-if="tableData.headCashInfo">
      <div
        class="price-item"
        v-for="(item, index) in tableData.headCashInfo"
        :key="index"
      >
        <div class="num">{{ $toMoney(item.value) }}</div>
        <div class="text">{{ item.name }}</div>
      </div>
    </div>
    <!-- 客户 -->
    <div class="body-box">
      <div class="customer_list" v-if="tableData.orderStatisticsInfo">
        <div class="customer_item">
          <div class="text">{{ gL("totalPepole") }}</div>
          <div class="num">
            {{ tableData.orderStatisticsInfo.customers_count }}
          </div>
        </div>
        <div class="customer_item">
          <div class="text">{{ gL("totalOrder") }}</div>
          <div class="num">{{ tableData.orderStatisticsInfo.order_count }}</div>
        </div>
        <div class="customer_item">
          <div class="text">{{ gL("average") }}</div>
          <div class="num">
            {{ $toMoney(tableData.orderStatisticsInfo.order_avg) }}
          </div>
        </div>
        <div class="customer_item">
          <div class="text">{{ gL("pepoleAverage") }}</div>
          <div class="num">
            {{ $toMoney(tableData.orderStatisticsInfo.customers_avg) }}
          </div>
        </div>
      </div>
      <!-- 支付类型 -->
      <div class="bill-list" v-if="tableData.order_proportion">
        <div class="tit">
          <div class="customer_item texts">
            <div class="text">{{ gL("amountCollected") }}</div>
          </div>
          <div class="customer_item">
            <div class="text">{{ gL("totalCount") }}</div>
          </div>
          <div class="customer_item">
            <div class="text">{{ gL("prices") }}</div>
          </div>
          <div class="customer_item">
            <div class="text">{{ gL("amountCollected") }}</div>
          </div>
          <div class="customer_item">
            <div class="text">{{ gL("drawback") }}</div>
          </div>
        </div>
        <div
          class="tit"
          v-for="(item, index) in tableData.order_proportion"
          :key="index"
        >
          <div class="customer_item texts">
            <div class="num">
              {{ gLang == 1 ? item.name_ug : item.name_zh }}
            </div>
          </div>
          <div class="customer_item">
            <div class="num">{{ item.order_count }}</div>
          </div>
          <div class="customer_item">
            <div class="num">{{ $toMoney(item.amount) }}</div>
          </div>
          <div class="customer_item">
            <div class="num">{{ $toMoney(item.real_received) }}</div>
          </div>
          <div class="customer_item">
            <div class="num">{{ $toMoney(item.refunded_amount) }}</div>
          </div>
        </div>
      </div>
      <!-- 会员 -->
      <div class="bill-list vip" v-if="tableData.vip">
        <div class="tit">
          <div class="customer_item texts">
            <div class="text">{{ gL("vip") }}</div>
          </div>
          <!-- <div class="customer_item" style="width:15%">
                <div class='text'>{{gL('vipTotalPrice')}}</div>
              </div> -->
          <div class="customer_item">
            <div class="text">{{ gL("rechargeTotal") }}</div>
          </div>
          <div class="customer_item">
            <div class="text">{{ gL("memberPersentPlaceholder") }}</div>
          </div>
        </div>
        <div class="tit">
          <div class="customer_item texts">
            <div class="num pay">{{ tableData.vip.vip_count }}</div>
          </div>
          <div class="customer_item_box" style="width:75%">
            <div
              class="customer_item"
              style="width:50%; display: inline-block;"
            >
              <div class="num pay_nums">
                {{ $toMoney(tableData.vip.vip_recharge_amount) }}
              </div>
            </div>
            <div
              class="customer_item"
              style="width:calc(50% - 8px); display: inline-block;border: none;"
            >
              <div class="num pay_nums">
                {{ $toMoney(tableData.vip.vip_recharge_present_amount) }}
              </div>
            </div>
            <div
              class="customer_item"
              style="width:100%; display: inline-block;"
            >
              <div class="recharge_list" style="width:100%">
                <div
                  class="recharge_item"
                  v-for="(item, index) in tableData.vip.vip_proportion"
                  :key="index"
                  :style="width"
                >
                  <div class="text">{{ item.pay_type_name }}</div>
                  <div class="num">{{ $toMoney(item.amount) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 挂账 -->
      <div class="bill-list vip" v-if="tableData.debt">
        <div class="tit">
          <div class="customer_item texts">
            <div class="text">{{ gL("creditHolderCount") }}</div>
          </div>
          <div class="customer_item" style="width:75%">
            <div class="text">{{ gL("creditTotalRePayment") }}</div>
          </div>
        </div>
        <div class="tit">
          <div class="customer_item texts">
            <div class="num pay">{{ tableData.debt.holder_count }}</div>
          </div>
          <div class="customer_item_box" style="width:75%">
            <div
              class="customer_item"
              style="width:100%; display: inline-block;"
            >
              <div class="num pay_nums">
                {{ $toMoney(tableData.debt.debt_repayment_amount) }}
              </div>
            </div>

            <div
              class="customer_item"
              style="width:100%; display: inline-block;"
            >
              <div class="recharge_list" style="width:100%">
                <div
                  class="recharge_item"
                  v-for="(item, index) in tableData.debt.debt_proportion"
                  :key="index"
                  :style="width"
                >
                  <div class="text">{{ item.pay_type_name }}</div>
                  <div class="num">{{ $toMoney(item.amount) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import {
  getStatisticsBusinessAPI,
  getBillPaymentTypesListAPI,
  getStaffUsersAPI,
  getBillDetailAPI
} from "./../api/index.js";
var self;
export default {
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.activeDay = 1;
    self.user_id = "";
    self.dateList(0);
    // self.getData();
    // self.getPayTypeList();
    self.getUserList();
  },
  created() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.activeDay = 1;
    self.user_id = "";
    self.dateList(0);
    // self.getData();
    // self.getPayTypeList();
    self.getUserList();
  },
  data() {
    return {
      gLang: 1,
      tableData: [],
      pickerBeginDateBefore: {
        disabledDate(time) {
          return (
            time.getTime() >=
            moment()
              .hour(23)
              .minute(59)
              .second(59)
              .millisecond(999)
              .valueOf()
          );
        }
      },
      modal_title: this.gL("tableNumber"),
      payList: [],
      pay_id: "",
      userList: [],
      user_id: "",
      picker: [],
      row_data: [],
      modal_box: false,
      day_list: [
        { name: this.gL("today"), id: 1, day: 0 },
        { name: this.gL("sevenDay"), id: 2, day: -7 },
        { name: this.gL("fifteen"), id: 3, day: -15 },
        { name: this.gL("thirty"), id: 4, day: -30 }
      ],
      activeDay: 1,
      amount: 0,
      sales: 0,
      order_id: "",
      width: "",
      printerKey: false
    };
  },
  methods: {
    print() {
      let disablePrint = localStorage.getItem("disablePrint") === "true";
      if (disablePrint) {
        self.$message({
          message: self.gL("disablePrinter"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      }

      if (this.printerKey) {
        this.$message({
          message: this.gL("etcFiveSecond"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (self.picker === null || self.picker.length !== 2) {
        self.$message({
          message: self.gL("pleaceChooiseTime"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      }

      const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      if (storeInfo == null || storeInfo == "") {
        self.$message({
          message: self.gL("noUserInfo"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      }

      this.$message({
        message: this.gL("sendPrinter"),
        type: "success",
        customClass: this.$toastClass(),
        offset: 120
      });

      const data = {
        action: "营业统计",
        time: moment().format("YYYY-MM-DD HH:mm:ss"),
        merchant_no: storeInfo.merchant_no,
        order: {
          merchant: {
            name_ug: storeInfo.merchant_name_ug,
            name_zh: storeInfo.merchant_name_zh
          },
          begin_date: this.tableData.begin_date,
          end_date: this.tableData.end_date,
          orderStatisticsInfo: this.tableData.orderStatisticsInfo,
          vip: this.tableData.vip,
          headCashInfo: this.tableData.headCashInfo,
          order_proportion: this.tableData.order_proportion,
          printers: {
            ...this.tableData.printers,
            lang: parseInt(this.gLang)
          }
        }
      };

      this.printerKey = true;
      setTimeout(() => {
        this.printerKey = false;
      }, 5000);

      this.$bus.$emit("customPrint", data);
      window.electronAPI && window.electronAPI.customPrint(data);
    },
    //获取今天，最近3天，最近7天
    dateList(day) {
      this.picker = [
        moment()
          .add(day, "days")
          .format("YYYY-MM-DD") + " 00:00:00",
        moment().format("YYYY-MM-DD HH:mm:ss")
      ];
      this.getData();
    },
    //获取数据
    getData() {
      var data = {
        begin_at: self.picker[0],
        end_at: self.picker[1]
      };
      if (self.user_id != "") {
        data.user_id = self.user_id;
      }
      if (self.pay_id != "") {
        data.payment_type_id = self.pay_id;
      }
      let loading = this.$loading();
      getStatisticsBusinessAPI(data)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.tableData = response.data.data;
            var length = self.tableData.vip.vip_proportion.length;
            if (length != 0) {
              self.width = "width:" + 100 / length + "%";
            } else {
              self.width = "width:100%";
            }

            // self.amount = response.data.real_amount;
            // self.sales = response.data.total_amount;
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    //获取数据
    getPayTypeList() {
      getBillPaymentTypesListAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.payList = response.data.data;
        }
      });
    },
    //获取数据
    getUserList() {
      getStaffUsersAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.userList = response.data.data;
        }
      });
    },
    //点击天
    clickDay(e) {
      self.activeDay = e.id;
      self.dateList(e.day);
    },
    //切换列表
    changeSlecet() {
      self.getData();
    },
    //切换列表
    serach() {
      if (self.picker != null) {
        self.getData();
      } else {
        self.$message({
          message: self.gL("pleaceChooiseTime"),
          type: "error",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    //订单详情
    rowClick(row) {
      self.modal_box = true;
      self.order_id = row.order_id;
      getBillDetailAPI(row.order_id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.row_data = response.data.data;
        }
      });
    }
    // invoice() {
    //   console.log("开发票");
    //   self
    //     .$post("bill/tax/" + self.order_id)
    //     .then(response => {
    //       if (response.status >= 200 && response.status < 300) {
    //         self.$message({
    //           message: self.gL("successfulOperation"),
    //           type: "success",
    //           customClass: self.$toastClass()
    //         });
    //       }
    //     })
    //     .catch(err => {
    //       if (err.response.status >= 400) {
    //         if (err.response.status == 401) {
    //           self.$router.push({ path: "/login" });
    //           self.$message({
    //             message: err.response.data.message,
    //             type: "error",
    //             customClass: self.$toastClass()
    //           });
    //         } else {
    //           self.$message({
    //             message: err.response.data.message,
    //             type: "error",
    //             customClass: self.$toastClass()
    //           });
    //         }
    //       }
    //     });
    // }
  }
};
</script>

<style lang="less" scoped>
.wraps {
  width: 100%;
  height: 100%;
  // overflow-y: scroll;
  z-index: 1;
  .top {
    display: flex;
    padding: 0 20px;
    background-color: #2e3033;
    color: #ffffff;
    height: 55px;
    align-items: center;
    border-bottom: 1px solid #666666;
    justify-content: space-between;
    font-size: 22px;
    .search {
      display: flex;
      .btn-search {
        padding: 0 20px;
        background: #139d59;
        display: flex;
        align-items: center;
        margin-left: 15px;
        font-size: 22px;
        cursor: pointer;
      }
    }
    .prices {
      background: #ff9c00;
      height: 75%;
      display: flex;
      align-items: center;
      padding: 0 40px;
      cursor: pointer;
    }
    .serach {
      width: 280px;
      background: #4d4d4d;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .input {
        //  width: 70%;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        width: 70%;
        background: #4d4d4d;
        &::placeholder {
          color: #cccccc;
        }
      }
      .searchs {
        color: #cccccc;
        font-size: 24px;
        padding: 0 10px;
      }
      .del {
        width: 20%;
        height: 100%;
        color: #cccccc;
        justify-content: center;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 22px;
        }
      }
    }
    .selects {
      margin-right: 25px;
    }
    .btn {
      background-color: #ff9c00;
      width: 150px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25px;
      height: 70%;
      font-size: 26px;
      cursor: pointer;
      .iconfont {
        font-size: 19px;
      }
    }
    .day-box {
      display: flex;
      justify-content: space-between;
      width: 470px;
      .day-item {
        padding: 5px 25px;
        background: #4d4d4d;
        border: 3px solid #4d4d4d;
        cursor: pointer;
      }
      .active {
        border-color: #139d59;
      }
    }
  }
  .price-list {
    display: flex;
    padding: 0 20px;
    background-color: #2e3033;
    color: #ffffff;
    align-items: center;
    border-bottom: 1px solid #666666;
    justify-content: space-between;
    font-size: 20px;
    .price-item {
      text-align: center;
      padding: 15px 0;
      .text {
        color: #e6e6e6;
        padding-top: 10px;
      }
      .num {
        font-size: 30px;
        color: #ff9c00;
        font-weight: bold;
      }
    }
  }
  .body-box {
    height: 75%;
    overflow-y: scroll;
  }
  .customer_list {
    display: flex;
    font-size: 22px;
    margin-top: 20px;
    .customer_item {
      text-align: center;
      border-right: 1px solid #cccccc;
      width: 25%;
      &:last-child {
        border-right: none;
      }
      .text {
        background-color: #e6e6e6;
        padding: 15px 0;
      }
      .num {
        padding: 15px 0;
        background-color: #f2f2f2;
        color: #139d59;
        font-weight: bold;
      }
    }
  }
  .bill-list {
    font-size: 22px;
    margin-top: 20px;
    .tit {
      display: flex;
      &:last-child {
        .num {
          border-bottom: none;
        }
      }
      .recharge_list {
        display: flex;
        justify-content: space-between;
        .recharge_item {
          border-right: 1px solid #cccccc;
        }
      }
    }
    .customer_item {
      text-align: center;
      border-right: 1px solid #cccccc;
      width: 37.5%;
      &:last-child {
        border-right: none;
      }
      .text {
        background-color: #e6e6e6;
        padding: 15px 0;
      }
      .num {
        color: #666666;
        padding: 15px 0;
        background-color: #f2f2f2;
        font-weight: normal;
        border-bottom: 1px solid #cccccc;
      }
      .pay {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #139d59;
        font-weight: bold;
      }
      .pay_nums {
        color: #139d59;
        font-weight: bold;
      }
    }
    .texts {
      width: 25%;
      .text {
        background-color: #666666;
        color: #ffffff;
      }
    }
  }
  .vip {
    background-color: #f2f2f2;
    margin-bottom: 20px;
  }
  .table {
    height: 86%;
    overflow-y: scroll;
  }
  //s提示框
  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .box {
      background-color: #ffffff;
      width: 700px;
      font-size: 30px;
      .title {
        color: #1a1a1a;
        padding: 25px 20px;
        position: relative;
        text-align: center;
        .iconfont {
          position: absolute;
          right: 20px;
          font-size: 26px;
          color: #666666;
          cursor: pointer;
        }
      }
      .content {
        padding: 0 50px 20px 50px;
        font-size: 18px;
        .info {
          border-bottom: 1px solid #666666;
          .row {
            display: flex;
            justify-content: space-between;
            padding-bottom: 5px;
            .row-item {
              width: 60%;
            }
            .size {
              width: 43%;
            }
          }
        }
        .prices {
          padding: 20px 0;
          .row {
            display: flex;
            justify-content: space-between;
            padding-bottom: 10px;
          }
          .strong {
            font-size: 22px;
            border-top: 1px solid #666666;
            padding-top: 10px;
          }
        }
        .btns {
          display: flex;
          font-size: 20px;
          justify-content: space-between;
          .btn-item {
            background: #666666;
            width: 48%;
            padding: 15px 0;
            color: #ffffff;
            text-align: center;
            cursor: pointer;
          }
        }
        .detail {
          height: 250px;
          overflow-y: scroll;
        }
      }
    }
  }
}
</style>
