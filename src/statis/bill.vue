<template>
    <div class="wraps staff">
        <div class="top">
            <div class="search">
                <el-date-picker
                    v-model="picker"
                    type="datetimerange"
                    :editable="false"
                    range-separator="~"
                    value-format="yyyy-MM-dd HH:mm"
                    format="yyyy-MM-dd HH:mm"
                    prefix-icon="el-icon-date"
                    :start-placeholder="gL('startTime')"
                    :end-placeholder="gL('endTime')"
                    :popper-class="gLang==1?'uy-date':'zh-date'"
                    :default-time="['00:00:00', '23:59:59']"
                    :picker-options="pickerBeginDateBefore">
                </el-date-picker>
                <div class="btn-search" @click="serach">{{gL('serach')}}</div>
            </div>
        </div>
        <div class="top" style="justify-content:flex-start">
            <div class="search" style="justify-content:space-between">
                <div class="select">
                    <el-select v-model="user_id" clearable filterable :placeholder="gL('staff')" @change="changeSlecet">
                        <el-option
                          v-for="item in userList"
                          v-if="item.id!=1"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id">
                        </el-option>
                    </el-select>
                </div>
                <div class="select selects">
                    <el-select v-model="pay_id"  clearable filterable :placeholder="gL('paymentMethod')" @change="changeSlecet">
                        <el-option
                          v-for="item in payList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id">
                        </el-option>
                    </el-select>
                </div>
            </div>
            <div class="day-box">
                <div class="day-item" :style="{direction:gLang==2?'ltr':'rtl'}" :class="activeDay==item.id?'active':''" v-for="(item,index) in day_list" @click="clickDay(item)" :key="index">{{item.name}}</div>
            </div>
        </div>
        <!-- 总览 -->
        <div class="price-list" v-if="tableData.paymentProportion">
            <div class="price-item" v-for="(item,index) in tableData.paymentProportion" :key="index">
              <div class="num">{{$toMoney(item.amount)}}</div>
              <div class="text">{{item.payment_type_name}}</div>
            </div>
        </div>
        <!-- 支付类型 -->
        <div class='bill-list'>
          <el-table
            :data="tableData.data"
            header-cell-class-name="headers"
            :header-cell-style="{background:'#e6e6e6'}"
            row-class-name="row"
            :cell-style="cell"
            v-loading="loading"
            style="width: 100%">
            <el-table-column
              prop="order_no"
              align="center"
              width="170"
              :label="gL('ordeNum')">
            </el-table-column>
            <el-table-column
              prop="original_price"
              align="center"
              :label="gL('recordPrice')">
            </el-table-column>
            <el-table-column
              prop="price"
              align="center"
              :label="gL('amountCollected')">
            </el-table-column>
            <el-table-column
              prop="payment_type"
              align="center"
              :label="gL('paymentMethod')">
            </el-table-column>
            <el-table-column
              prop="cashier_name"
              align="center"
              :label="gL('cashier')">
            </el-table-column>
            <el-table-column
              prop="paid_at"
              align="center"
              width="230"
              :label="gL('checkOutTime')">
            </el-table-column>
          </el-table>
          <div class="paging">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              background
              layout="prev, pager, next"
              :total="totalCount">
            </el-pagination>
          </div>
          <!-- <div class="tit title-text">
            <div class="customer_item">
              <div class='text'>{{gL('ordeNum')}}</div>
            </div>
            <div class="customer_item">
              <div class='text'>{{gL('recordPrice')}}</div>
            </div>
            <div class="customer_item">
              <div class='text'>{{gL('amountCollected')}}</div>
            </div>
            <div class="customer_item">
              <div class='text'>{{gL('paymentMethod')}}</div>
            </div>
            <div class="customer_item">
              <div class='text'>{{gL('cashier')}}</div>
            </div>
            <div class="customer_item" style="width:25%">
              <div class='text'>{{gL('checkOutTime')}}</div>
            </div>
          </div>
          <div class="bod">
              <div class="tit" v-for="(item,index) in tableData.data" :key="index">
                <div class="customer_item">
                <div class="num">{{item.order_no}}</div>
                </div>
                <div class="customer_item">
                <div class="num">{{$toMoney(item.original_price)}}</div>
                </div>
                <div class="customer_item">
                <div class="num">{{$toMoney(item.price)}}</div>
                </div>
                <div class="customer_item">
                <div class="num">{{item.payment_type}}</div>
                </div>
                <div class="customer_item">
                <div class="num">{{item.cashier_name}}</div>
                </div>
                <div class="customer_item" style="width:25%">
                <div class="num">{{item.paid_at}}</div>
                </div>
            </div>
          </div> -->
        </div>
    </div>
</template>

<script>
var self;
export default {
  created: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.pageSize = 1;
    self.dateList(0);
    // self.getData();
    self.getPayTypeList();
    self.getUserList();
  },
  data() {
    return {
        gLang:1,
        tableData:[],
        pickerBeginDateBefore:{
          disabledDate(time){
            return time.getTime() >= moment().hour(23).minute(59).second(59).millisecond(999).valueOf();
          }
        },
        modal_title:this.gL('tableNumber'),
        payList:[],
        pay_id:'',
        userList:[],
        user_id:'',
        picker:[],
        row_data:[],
        modal_box:false,
        day_list:[
            {name:this.gL('today'),id:1,day:0},
            {name:this.gL('sevenDay'),id:2,day:-7},
            {name:this.gL('fifteen'),id:3,day:-15},
            {name:this.gL('thirty'),id:4,day:-30}
          ],
        activeDay:1,
        amount:0,
        sales:0,
        order_id:'',
        loading:true,
        totalCount:0,
        lastPage:0,
        pageSize:1,
        currentPage:0,
    };
  },
  methods: {
    //获取今天，最近3天，最近7天
    dateList(day){
      var dd = new Date();
      dd.setDate(dd.getDate() + day);//获取p_count天后的日期
      var y = dd.getFullYear();
      var m = dd.getMonth() + 1;//获取当前月份的日期
      var d = dd.getDate();
      var pp = new Date();
      var yp = pp.getFullYear();
      var mp= pp.getMonth() + 1;//获取当前月份的日期
      var dp = pp.getDate();
      var h = pp.getHours();       //获取当前小时数(0-23)
      var i = pp.getMinutes();     //获取当前分钟数(0-59)
      var s = pp.getSeconds();     //获取当前分秒数(0-59)
      var Front = y + "-" + m + "-" + d + ' 00:00:00';
      var today = yp + "-" + mp + "-" + dp + ' ' + h + ":" + i + ":" + s;
      self.picker = [Front,today];
      self.getData();
   },
   cell({row, column, rowIndex, columnIndex}) {
      if(columnIndex == 0){
        if(self.gLang==1){
          return 'direction: rtl'
        }
      }
    },
    // 分页
    handleSizeChange(val) {
      self.CurrentChange = val;
      self.getData();
    },
    handleCurrentChange(val) {
      self.pageSize = val;
      self.getData();
    },
    doHandleMonth(month){
       var m = month;
       if(month.toString().length == 1){
          m = "0" + month;
       }
       return m;
    },
    //获取数据
    getData(){
      self.loading = true;
      var data = {
        begin_at:self.picker[0],
        end_at:self.picker[1],
      }
      if(self.user_id!=''){
        data.cashier_id = self.user_id;
      }
        if(self.pay_id!=''){
            data.payment_type_id = self.pay_id;
        }
        data.limit=10;
        data.page=self.pageSize;
       self.$fetch('statistics/bill',data).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.tableData = response.data;
          self.totalCount = response.data.meta.total;
          self.lastPage = response.data.meta.last_page;
          self.currentPage = response.data.meta.current_page;
        }
        self.loading = false;
      }).catch(err => {
        if(err.response.status>=400){
          if(err.response.status==401){
            self.$router.push({ path: "/login" });
            self.$message({
              message:err.response.data.message,
              type: "error",
              customClass:self.$toastClass(),
              offset: 120
            });
          }else{
            self.$message({
              message:err.response.data.message,
              type: "error",
              customClass:self.$toastClass(),
              offset: 120
            });
          }
        }
        self.loading = false;
      });
    },
    //获取数据
    getPayTypeList(){
       self.$fetch('paymentTypes/list').then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.payList = response.data.data;
        }
      }).catch(err => {
        if(err.response.status>=400){
          if(err.response.status==401){
            self.$router.push({ path: "/login" });
            self.$message({
              message:err.response.data.message,
              type: "error",
              customClass:self.$toastClass(),
              offset: 120
            });
          }else{
            self.$message({
              message:err.response.data.message,
              type: "error",
              customClass:self.$toastClass(),
              offset: 120
            });
          }
        }
      });
    },
    //获取数据
    getUserList(){
       self.$fetch('user/users').then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.userList = response.data.data;
        }
      }).catch(err => {
        if(err.response.status>=400){
          if(err.response.status==401){
            self.$router.push({ path: "/login" });
            self.$message({
              message:err.response.data.message,
              type: "error",
              customClass:self.$toastClass(),
              offset: 120
            });
          }else{
            self.$message({
              message:err.response.data.message,
              type: "error",
              customClass:self.$toastClass(),
              offset: 120
            });
          }
        }
      });
    },
    //点击天
    clickDay(e){
        self.activeDay = e.id;
        self.dateList(e.day);
    },
    //切换列表
    changeSlecet(){
        self.getData();
    },
    //切换列表
    serach(){
      if(self.picker!=null){
        self.getData();
      }else{
        self.$message({
          message:self.gL('pleaceChooiseTime'),
          type: "error",
          customClass:self.$toastClass(),
          offset: 120
        });
      }
    },
    //订单详情
    rowClick(row){
      self.modal_box = true;
      self.order_id = row.order_id;
      self.$fetch('bill/detail/'+row.order_id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.row_data = response.data.data;
        }
      }).catch(err => {
        if(err.response.status>=400){
          if(err.response.status==401){
            self.$router.push({ path: "/login" });
            self.$message({
              message:err.response.data.message,
              type: "error",
              customClass:self.$toastClass(),
              offset: 120
            });
          }else{
            self.$message({
              message:err.response.data.message,
              type: "error",
              customClass:self.$toastClass(),
              offset: 120
            });
          }
        }
      });
    },
    invoice(){
      console.log('开发票');
      self.$post('bill/tax/'+self.order_id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.$message({
            message:self.gL('successfulOperation'),
            type: "success",
            customClass:self.$toastClass(),
            offset: 120
          });
          }
        }).catch(err => {
          if(err.response.status>=400){
            if(err.response.status==401){
              self.$router.push({ path: "/login" });
              self.$message({
                message:err.response.data.message,
                type: "error",
                customClass:self.$toastClass(),
                offset: 120
              });
            }else{
              self.$message({
                message:err.response.data.message,
                type: "error",
                customClass:self.$toastClass(),
                offset: 120
              });
            }
          }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.paging{
    text-align: center;
    padding-top: 10px;
  }
.wraps{
    width: 100%;
    height: 100%;
    // overflow-y: scroll;
    z-index: 1;
     .top{
        display: flex;
        padding:0 20px;
        background-color: #2e3033;
        color:#ffffff;
        height: 55px;
        align-items: center;
        border-bottom: 1px solid #666666;
        justify-content: space-between;
        font-size:22px;
        .search{
            display: flex;
            .btn-search{
                padding: 0 20px;
                background: #139d59;
                display: flex;
                align-items: center;
                margin-left: 15px;
                font-size: 22px;
                cursor: pointer;
            }
        }
        .prices{
          background:#ff9c00;
          height: 75%;
          display: flex;
          align-items: center;
          padding: 0 40px;
          cursor: pointer;
        }
    .serach{
       width: 280px;
       background: #4d4d4d;
       display: flex;
       align-items: center;
       justify-content: space-between;
       .input{
        //  width: 70%;
       }
       input{
         outline: none;
         font-size: 22px;
         color:#ffffff;
         width: 70%;
         background: #4d4d4d;
         &::placeholder{
             color:#cccccc;
         }
       }
       .searchs{
         color:#cccccc;
         font-size: 24px;
         padding: 0 10px;
       }
       .del{
         width: 20%;
         height: 100%;
        color: #cccccc;
         justify-content: center;
         display: flex;
         align-items: center;
         cursor: pointer;
         .iconfont{
             font-size: 22px;
         }
       }
     }
     .selects{
         margin:0 20px;
     }
     .btn{
        background-color: #ff9c00;
        width: 150px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 25px;
        height: 70%;
        font-size: 26px;
        cursor: pointer;
        .iconfont{
            font-size: 19px;
        }
      }
      .day-box{
          display: flex;
          justify-content: space-between;
          width: 470px;
          .day-item{
              padding: 5px 25px;
              background: #4d4d4d;
              border:3px solid #4d4d4d;
              cursor: pointer;
          }
          .active{
              border-color:#139d59;
          }
      }
    }
    .price-list{
      display: flex;
      padding:0 20px;
      background-color: #2e3033;
      color:#ffffff;
      align-items: center;
      border-bottom: 1px solid #666666;
      justify-content: space-between;
      font-size:20px;
      .price-item{
        text-align: center;
        padding: 15px 0;
        .text{
          color:#e6e6e6;
          padding-top: 10px;
        }
        .num{
          font-size:30px;
          color:#ff9c00;
          font-weight: bold;
        }
      }
    }
    .bill-list{
      font-size: 22px;
        // width: 100%;
        height: 100%;

      .tit{
        display: flex;
        border-bottom: 1px solid #cccccc;
        &:last-child{
            border-bottom:none;
        }
      }
      .title-text{
        // position: fixed;
        // top: 231px;
        // width: 100%;
        // z-index: 2;
        border-right: 5px solid rgba(0,0,0,.2)
      }
      .bod{
        //   margin: 53px 0;
          width: 100%;
          height: 80%;
          overflow-y: scroll;
      }
      .customer_item{
        text-align: center;
        border-right:1px solid #cccccc;
        width: 15%;
        &:last-child{
          border-right:none;
        }
        .text{
          background-color: #e6e6e6;
          padding: 15px 0;
            color: #1a1a1a;
        }
        .num{
          color:#666666;
          padding: 15px 5px;
          background-color: #f2f2f2;
          font-weight: normal;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .pay{
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          color:#139d59;
          font-weight: bold;
        }
        .pay_nums{
          color:#139d59;
          font-weight: bold;
        }
      }
      .texts{
        width: 25%;
        .text{
          background-color: #666666;
          color:#ffffff;
        }
      }
    }
    .vip{
      background-color: #f2f2f2;
    }
    .table{
      height: 86%;
      overflow-y: scroll;
    }
      //s提示框
   .mask{
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      background-color: rgba(0,0,0,.5);
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 1366px;
      min-height: 768px;
      .box{
        background-color: #ffffff;
        width: 700px;
        font-size: 30px;
        .title{
          color:#1a1a1a;
          padding: 25px 20px;
          position: relative;
          text-align: center;
          .iconfont{
            position: absolute;
            right: 20px;
            font-size: 26px;
            color:#666666;
            cursor: pointer;
          }
        }
        .content{
          padding: 0 50px 20px 50px ;
          font-size: 18px;
         .info{
           border-bottom: 1px solid #666666;
           .row{
             display: flex;
             justify-content: space-between;
             padding-bottom: 5px;
             .row-item{
               width: 60%;
             }
              .size{
                width: 43%;
              }
           }
         }
         .prices{
           padding: 20px 0;
           .row{
             display: flex;
             justify-content: space-between;
             padding-bottom: 10px;
           }
           .strong{
             font-size: 22px;
             border-top: 1px solid #666666;
             padding-top: 10px;
           }
         }
         .btns{
           display: flex;
           font-size: 20px;
           justify-content: space-between;
           .btn-item{
             background: #666666;
             width: 48%;
             padding: 15px 0;
             color:#ffffff;
             text-align: center;
             cursor: pointer;
           }
         }
         .detail{
           height: 250px;
           overflow-y: scroll;
         }
        }
      }
    }
  }
   @media screen and (max-width: 1366px) {
    .wraps {
      .bill-list{
        height: 69%;
        overflow-y: scroll;
      }
    }
  }
</style>
