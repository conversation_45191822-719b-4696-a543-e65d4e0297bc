import moment from "moment";

export function formatDate(date, fmt) {
  if (fmt) {
    return moment(date).format(fmt);
  } else {
    return moment(date).format("YYYY-MM-DD HH:mm:ss");
  }
}

let debounceTimer = null;
export function debounce(fun, duration = 1000) {
  clearTimeout(debounceTimer);
  debounceTimer = null;
  // 返回一个promise对象，在指定时间后执行
  return new Promise(resolve => {
    debounceTimer = setTimeout(() => {
      fun();
      resolve();
    }, duration);
  });
}

/**
 * 数字格式化函数
 * @param {number|string} val - 要格式化的数字
 * @param {object} options - 配置选项
 * @param {boolean} [options.isCurrency=true] - 是否转换为人民币格式（带逗号分隔）
 * @param {number} [options.decimal=2] - 保留小数位数
 * @param {boolean} [options.autoFillZero=true] - 小数不足时是否自动补零
 * @param {boolean} [options.withSymbol=true] - 是否添加人民币符号（￥）
 * @returns {string} 格式化后的字符串
 */
export function formatNumber(val, options = {}) {
  // 默认配置
  const defaultOptions = {
    isCurrency: true,
    decimal: 2,
    autoFillZero: true,
    withSymbol: true
  };

  // 合并配置
  const { isCurrency, decimal, autoFillZero, withSymbol } = {
    ...defaultOptions,
    ...options
  };

  // 处理输入值
  if (val === null || val === undefined || val === "")
    return withSymbol ? "￥0.00" : "0.00";

  // 转换为数字
  let num = Number(val);
  if (isNaN(num)) return withSymbol ? "￥0.00" : "0.00";

  // 四舍五入到指定小数位
  num = Math.round(num * Math.pow(10, decimal)) / Math.pow(10, decimal);

  // 分割整数和小数部分
  const parts = num.toString().split(".");
  let integerPart = parts[0];
  let decimalPart = parts[1] || "";

  // 处理负数
  const sign = integerPart.startsWith("-") ? "-" : "";
  if (sign) integerPart = integerPart.slice(1);

  // 添加千位分隔符（人民币格式）
  if (isCurrency) {
    integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  // 处理小数部分
  if (decimal > 0) {
    if (autoFillZero) {
      // 自动补零
      decimalPart = decimalPart.padEnd(decimal, "0").slice(0, decimal);
    } else {
      // 不补零，直接截取
      decimalPart = decimalPart.slice(0, decimal);
    }
  } else {
    // 不需要小数部分
    decimalPart = "";
  }

  // 组合结果
  let result = sign + integerPart;
  if (decimalPart) result += "." + decimalPart;

  // 添加人民币符号
  if (withSymbol) {
    result = "￥" + result;
  }

  return result;
}

export function deepClone(data) {
  return JSON.parse(JSON.stringify(data));
}