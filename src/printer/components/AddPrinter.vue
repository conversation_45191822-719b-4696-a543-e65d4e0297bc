<template>
  <div class="mask add-print-modal" v-if="show">
    <div class="box">
      <div class="title">
        <span>{{ title }}</span>
        <span class="iconfont icon-jia-copy" @click="cancelHandler"></span>
      </div>
      <div v-if="!step">
        <div class="content">
          <!-- 上方选择打印机radio-button -->
          <div class="item">
            <el-radio-group
              size="medium"
              v-model="tagIndex"
              class="flex-center head-radio"
            >
              <el-radio-button size="medium" label="ip">IP</el-radio-button>
              <el-radio-button size="medium" label="usb">USB</el-radio-button>
              <el-radio-button size="medium" label="cloud">{{
                gL("cloudPrinter")
              }}</el-radio-button>
            </el-radio-group>
          </div>

          <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            v-if="['ip', 'usb'].includes(tagIndex)"
          >
            <!-- 输入IP -->
            <div class="item" v-if="tagIndex == 'ip'">
              <el-form-item prop="ip_address">
                <el-input
                  :placeholder="gL('inputPrinterIPTips')"
                  :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                  v-model="ruleForm.ip_address"
                >
                  <template slot="prepend">IP</template>
                </el-input>
              </el-form-item>
            </div>

            <!-- 输入Port -->
            <div class="item" v-if="tagIndex == 'ip'">
              <el-form-item prop="port">
                <el-col :span="18">
                  <!-- <el-col> -->
                  <el-input
                    :placeholder="gL('inputPrinterIPTips')"
                    :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                    v-model.number="ruleForm.port"
                  >
                    <template slot="prepend">Port</template>
                  </el-input>
                </el-col>
                <el-col :span="6" class="text-rtl">
                  <el-button
                    type="warning"
                    @click="() => testConnection('ip')"
                    >{{ gL("testConnection") }}</el-button
                  >
                </el-col>
              </el-form-item>
            </div>

            <!-- usb连接 -->
            <div class="item" v-if="tagIndex == 'usb'">
              <el-form-item prop="printer_usb">
                <el-col :span="18">
                  <!-- <el-col> -->
                  <div class="usb-input-box" @click="clickUsbInputHandler">
                    <el-input
                      :placeholder="gL('inputPrinterUSBTips')"
                      class="usb-input"
                      :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                      v-model="ruleForm.printer_usb"
                      :disabled="true"
                    >
                      <template slot="prepend">USB</template>
                    </el-input>
                  </div>
                </el-col>
                <el-col :span="6" class="text-rtl">
                  <el-button
                    type="warning"
                    @click="() => testConnection('usb')"
                    >{{ gL("testConnection") }}</el-button
                  >
                </el-col>
              </el-form-item>
            </div>

            <!-- 选择打印机纸大小 -->
            <div class="item">
              <el-form-item prop="paper_width">
                <div class="radio-item">
                  <el-radio-group size="medium" v-model="ruleForm.paper_width">
                    <el-radio-button size="medium" :label="80">
                      80MM
                    </el-radio-button>
                    <el-radio-button size="medium" :label="58">
                      58MM
                    </el-radio-button>
                  </el-radio-group>
                  <span
                    :style="
                      gLang == 1 ? 'padding-left:32px' : 'padding-right-32px'
                    "
                    >{{ gL("printerPaperSize") }}</span
                  >
                </div>
              </el-form-item>
            </div>

            <!-- 声音和图片打印功能 -->
            <div class="item">
              <el-form-item>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="item-switch-box">
                      <div class="item-block-gray">
                        {{ gL("beatifulPrint") }}
                      </div>
                      <div class="item-switch">
                        <el-switch
                          v-model="ruleForm.print_mode"
                          active-color="#13ce66"
                          inactive-color="#ff4949"
                        >
                        </el-switch>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="item-switch-box">
                      <div class="item-block-gray">
                        {{ gL("audioPrint") }}
                      </div>
                      <div class="item-switch">
                        <el-switch
                          v-model="ruleForm.buzzer"
                          active-color="#13ce66"
                          inactive-color="#ff4949"
                        >
                        </el-switch>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>

            <!-- 打印机维语名称 -->
            <div class="item">
              <el-form-item prop="name_ug">
                <el-input
                  :placeholder="gL('inputPrinterNameTipsUg')"
                  class="input-ug"
                  :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                  v-model="ruleForm.name_ug"
                ></el-input>
              </el-form-item>
            </div>

            <!-- 打印机汉语名称 -->
            <div class="item">
              <el-form-item prop="name_zh">
                <el-input
                  :placeholder="gL('inputPrinterNameTipsZh')"
                  :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                  v-model="ruleForm.name_zh"
                  @keyup.enter.native="nextStep"
                ></el-input>
              </el-form-item>
            </div>
          </el-form>

          <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            v-if="tagIndex == 'cloud'"
          >
            <!-- 输入打印机号码 -->
            <div class="item">
              <el-form-item prop="cloud">
                <el-input
                  :placeholder="gL('inputPrinterNo')"
                  :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                  v-model="ruleForm.cloud"
                >
                  <template slot="prepend">{{ gL("printerNo") }}</template>
                </el-input>
              </el-form-item>
              <div class="test-btn-box">
                <el-button
                  type="warning"
                  @click="() => testConnection('cloud')"
                  >{{ gL("testConnection") }}</el-button
                >
              </div>
            </div>

            <!-- 选择打印机纸大小 -->
            <div class="item">
              <el-form-item prop="paper_width">
                <div class="radio-item">
                  <el-radio-group size="medium" v-model="ruleForm.paper_width">
                    <!-- <el-radio-button size="medium" :label="0">
                      80MM
                    </el-radio-button> -->
                    <el-radio-button size="medium" :label="58">
                      58MM
                    </el-radio-button>
                  </el-radio-group>
                  <span
                    :style="
                      gLang == 1 ? 'padding-left:32px' : 'padding-right-32px'
                    "
                    >{{ gL("printerPaperSize") }}</span
                  >
                </div>
              </el-form-item>
            </div>

            <!-- 打印机维语名称 -->
            <div class="item">
              <el-form-item prop="name_ug">
                <el-input
                  :placeholder="gL('inputPrinterNameTipsUg')"
                  class="input-ug"
                  :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                  v-model="ruleForm.name_ug"
                ></el-input>
              </el-form-item>
            </div>

            <!-- 打印机汉语名称 -->
            <div class="item">
              <el-form-item prop="name_zh">
                <el-input
                  :placeholder="gL('inputPrinterNameTipsZh')"
                  :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                  v-model="ruleForm.name_zh"
                  @keyup.enter.native="nextStep"
                ></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div class="btn" @click="nextStep">{{ gL("nextStep") }}</div>
      </div>
      <div v-if="step">
        <div class="content two-step">
          <el-row class="check-block-box" :gutter="30">
            <el-col class="check-block" :span="12">
              <div class="check-block-tab">
                {{ gL("houtang") }}
              </div>
              <div
                class="check-block-item"
                :class="gLang == 1 ? 'uy-checkbox' : 'zh-checkbox'"
              >
                <el-checkbox
                  v-for="(value, key) in ruleForm.kitchen_config"
                  :label="key"
                  :key="key"
                  v-model="ruleForm.kitchen_config[key]"
                  @change="val => changeCheckboxHandler(key, val)"
                  >{{ gL(`printer_${key}`) }}</el-checkbox
                >
              </div>
            </el-col>
            <div class="check-block-line"></div>
            <el-col class="check-block" :span="12">
              <div class="check-block-tab">{{ gL("bar") }}</div>
              <div
                class="check-block-item"
                :class="gLang == 1 ? 'uy-checkbox' : 'zh-checkbox'"
              >
                <el-checkbox
                  v-for="(value, key) in ruleForm.cashier_config"
                  :label="key"
                  :key="key"
                  v-model="ruleForm.cashier_config[key]"
                  >{{ gL(`printer_${key}`) }}</el-checkbox
                >
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="bar-btns">
          <div class="btn-item" @click="previousStep">
            {{ gL("previousStep") }}
          </div>
          <div class="btn-item back" @click="confirm">
            {{ gL("confirm") }}
          </div>
        </div>
      </div>
    </div>

    <!-- 选择usb 打印机弹出框 -->
    <el-dialog
      :title="gL('chooisePrinter')"
      :visible.sync="usbDialogVisible"
      width="40%"
      :center="true"
      top="40vh"
      :modal="false"
    >
      <div class="printer-list">
        <el-radio-group
          v-model="usbDealogValue"
          class="usb-printer-radio-group"
          @input="changeUsbPrinterList"
        >
          <el-radio
            class="pritner-item"
            :label="item"
            v-for="(item, index) in usbPrinterList"
            :key="index"
            >{{ item }}</el-radio
          >
        </el-radio-group>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmUsbDialog">{{
          gL("confirm")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPrinterCloudTestAPI,
  postPrinterAddAPI,
  putPrinterAPI
} from "./../../api/index.js";
export default {
  inject: ["appData"],
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ""
    },
    boxType: {
      type: String,
      default: "add"
    },
    editData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    show(value) {
      if (value) {
        if (this.boxType == "edit") {
          this.ruleForm = {
            name_ug: this.editData.name_ug ? this.editData.name_ug : "",
            name_zh: this.editData.name_zh ? this.editData.name_zh : "",
            ip_address: this.editData.ip_address,
            port:
              this.editData.connection_type == "ip"
                ? parseInt(this.editData.usb_port)
                : 9100,
            cloud: this.editData.cloud ? this.editData.cloud : "",
            paper_width:
              this.editData.paper_width == undefined
                ? 58
                : this.editData.paper_width,
            print_mode: this.editData.print_mode == "image" ? true : false,
            buzzer: this.editData.buzzer,
            printer_usb:
              this.editData.connection_type == "usb"
                ? this.editData.usb_port
                : "",
            cashier_config:
              this.editData.cashier_config != null
                ? this.editData.cashier_config
                : {
                    pre_ticket: false,
                    statement_ticket: false,
                    recharge_ticket: false,
                    business_ticket: false,
                    delivery_ticket: false
                  },
            kitchen_config:
              this.editData.kitchen_config != null
                ? {
                    order_ticket: this.editData.kitchen_config.split_ticket
                      ? false
                      : true,
                    ...this.editData.kitchen_config
                  }
                : {
                    split_ticket: true,
                    order_ticket: false,
                    back_ticket: false,
                    table_change_ticket: false,
                    order_merge_ticket: false
                  }
          };

          this.tagIndex =
            this.editData.connection_type == "network"
              ? "ip"
              : this.editData.connection_type;
        }
      } else {
        this.emptyData();
      }
    }
  },
  activated() {
    this.gLang = localStorage.getItem("langId");
  },
  data() {
    const checkIpAddress = (rule, value, callback) => {
      const regEXP = /^((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])(?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$/;
      if (regEXP.test(value)) {
        callback();
      } else {
        callback(new Error(this.gL("inputPrinterIPNumberTips")));
      }
    };
    return {
      gLang: 1,
      step: false, //下一步上一步
      ruleForm: {
        name_ug: "",
        name_zh: "",
        ip_address: "",
        port: 9100,
        cloud: "",
        paper_width: 58,
        print_mode: false,
        buzzer: false,
        printer_usb: "",
        cashier_config: {
          pre_ticket: false,
          statement_ticket: false,
          recharge_ticket: false,
          business_ticket: false,
          delivery_ticket: false
        },
        kitchen_config: {
          split_ticket: true,
          order_ticket: false,
          back_ticket: false,
          table_change_ticket: false,
          order_merge_ticket: false
        }
      },
      rules: {
        ip_address: [
          {
            required: true,
            message: this.gL("inputPrinterIPTips"),
            trigger: "blur"
          },
          {
            validator: checkIpAddress,
            trigger: "blur"
          }
        ],
        port: [
          {
            required: true,
            message: this.gL("inputPrinterPortTips"),
            trigger: "blur"
          },
          {
            type: "number",
            message: this.gL("inputPrinterPortNumberTips"),
            trigger: "blur"
          }
        ],
        name_ug: [
          {
            required: true,
            message: this.gL("inputPrinterNameTipsUg"),
            trigger: "blur"
          }
        ],
        name_zh: [
          {
            required: true,
            message: this.gL("inputPrinterNameTipsZh"),
            trigger: "blur"
          }
        ],
        printer_usb: [
          {
            required: true,
            message: this.gL("inputPrinterUSBTips")
          }
        ]
      },
      tagIndex: "ip",
      usbPrinterList: [],
      usbDialogVisible: false,
      usbDealogValue: "",
      printerKey: false
    };
  },
  methods: {
    changeUsbPrinterList(value) {
      this.ruleForm.printer_usb = value;
    },
    // 点击usb输入框
    clickUsbInputHandler() {
      window.electronAPI &&
        window.electronAPI
          .getPrinterList()
          .then(printList => {
            console.log("printList -> ", printList);
            this.usbPrinterList = printList;
            if (printList.length > 0) {
              this.usbDialogVisible = true;
              this.usbDealogValue = this.ruleForm.printer_usb;
            } else {
              this.$message({
                message: this.gL("noUsbPrinter"),
                type: "warning",
                customClass: this.$toastClass(),
                offset: 120
              });
            }
          })
          .catch(err => {
            console.log("getPrinterList err -> ", err);
          });
    },

    // 确定选择usb弹窗框
    confirmUsbDialog() {
      if (this.usbDealogValue == "") {
        this.$message({
          message: this.gL("selectUSBPrinter"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      this.ruleForm.printer_usb = this.usbDealogValue;
      this.usbDialogVisible = false;
      this.usbDealogValue = "";
    },

    //清空表单
    emptyData() {
      this.step = false;
      this.ruleForm = {
        name_ug: "",
        name_zh: "",
        ip_address: "",
        port: 9100,
        cloud: "",
        paper_width: 58,
        print_mode: false,
        buzzer: false,
        printer_usb: "",
        cashier_config: {
          pre_ticket: false,
          statement_ticket: false,
          recharge_ticket: false,
          business_ticket: false,
          delivery_ticket: false
        },
        kitchen_config: {
          split_ticket: true,
          order_ticket: false,
          back_ticket: false,
          table_change_ticket: false,
          order_merge_ticket: false
        }
      };
    },
    // 关闭弹出框
    cancelHandler() {
      this.$emit("cancelAdd");
    },
    //下一步
    nextStep() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.step = true;
        }
      });
    },
    //测试连接
    testConnection(type) {
      if (this.printerKey) {
        this.$message({
          message: this.gL("etcThreeSecond"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      if (type == "cloud") {
        getPrinterCloudTestAPI({
          cloud: this.ruleForm.cloud
        }).then(response => {
          console.log("response", response);
          if (response.status == 200 && response.data.code == 0) {
            this.$message({
              message: this.gL("sendPrinter"),
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
          } else {
            this.$message({
              message: response.data.msg,
              type: "warning",
              customClass: this.$toastClass(),
              offset: 120
            });
          }
        });
        return;
      }
      const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      if (storeInfo == "" || storeInfo == null) {
        this.$message({
          message: self.gL("noUserInfo"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      this.$message({
        message: this.gL("sendPrinter"),
        type: "success",
        customClass: this.$toastClass(),
        offset: 120
      });

      let data = {
        action: "测试",
        merchant_no: storeInfo.merchant_no,
        order: {
          printers: {
            ip_address:
              type == "ip"
                ? this.ruleForm.ip_address + ":" + this.ruleForm.port
                : this.ruleForm.printer_usb,
            beautiful_print: 0
          }
        }
      };
      this.appData.print(data);
      console.log("data -> ", data);

      this.printerKey = true;
      setTimeout(() => {
        this.printerKey = false;
      }, 3000);

      // window.electronAPI && window.electronAPI.customPrint(data);
    },

    changeCheckboxHandler(key, value) {
      if (key == "split_ticket") {
        this.ruleForm.kitchen_config.order_ticket = value ? false : true;
      }
      if (key == "order_ticket") {
        this.ruleForm.kitchen_config.split_ticket = value ? false : true;
      }
    },

    //下一步
    previousStep() {
      this.step = false;
    },
    //提交
    confirm() {
      console.log("--------------- --- ", this.ruleForm);

      const data = {
        ...this.ruleForm,
        print_mode: this.ruleForm.print_mode ? "image" : "text",
        connection_type: this.tagIndex == "ip" ? "network" : this.tagIndex
      };

      if (this.tagIndex == "ip") {
        data.usb_port = this.ruleForm.port.toString();
      } else if (this.tagIndex == "usb") {
        data.usb_port = this.ruleForm.printer_usb;
      }

      const loading = this.$loading();
      if (this.boxType == "add") {
        postPrinterAddAPI(data)
          .then(response => {
            if (response.status >= 200 && response.status < 300) {
              this.$message({
                message: this.gL("successfulOperation"),
                type: "success",
                customClass: this.$toastClass(),
                offset: 120
              });
              this.$emit("confirm");
            }
          })
          .finally(() => {
            loading.close();
          });
      } else {
        putPrinterAPI(this.editData.id, { ...data, id: this.editData.id })
          .then(response => {
            if (response.status >= 200 && response.status < 300) {
              this.$message({
                message: this.gL("successfulOperation"),
                type: "success",
                customClass: this.$toastClass(),
                offset: 120
              });
              this.$emit("confirm");
            }
          })
          .finally(() => {
            loading.close();
          });
      }
    }
  }
};
</script>

<style lang="less">
.add-print-modal {
  .el-input__inner::placeholder {
    font-size: 16px;
  }
  .el-input-group__prepend {
    width: 100px;
    text-align: center;
  }
  .el-form-item__error {
    font-size: 14px;
  }
  .input-ug .el-input__inner::placeholder {
    text-align: right;
  }
  .el-checkbox {
    padding: 15px 0px;
    width: 33.33%;
    margin: 0;
  }
  .box .content .head-radio .el-radio-button .el-radio-button__inner {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .box .content .next-head-radio .el-radio-button .el-radio-button__inner {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .usb-input.el-input.is-disabled .el-input__inner {
    cursor: pointer;
  }

  .printer-list {
    .usb-printer-radio-group {
      display: flex;
      flex-direction: column;
      row-gap: 10px;
      .pritner-item {
        margin: 0px;
      }
    }
  }

  .check-block-box .check-block-item .el-checkbox__label {
    padding-inline: 10px;
    font-size: 20px;
  }

  .check-block-box .check-block-item.uy-checkbox {
    direction: rtl;
  }

  .check-block-box .check-block-item.zh-checkbox {
    direction: ltr;
  }
}
</style>

<style lang="less" scoped>
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .text-rtl {
    text-align: right;
  }
  .box {
    background-color: #ffffff;
    width: 1100px;
    font-size: 26px;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: #666666;
        cursor: pointer;
      }
    }
    .content {
      padding: 30px 270px 0px 270px;
      .item {
        margin-bottom: 25px;
        width: 100%;
      }
      .test-btn-box {
        width: 100%;
        text-align: right;
      }
      .test {
        button {
          font-size: 20px;
        }
      }
      .head-radio {
        width: 100%;
        height: 40px;
      }
      .head-radio .el-radio-button {
        width: 50%;
        height: 100%;
      }
      .next-head-radio {
        width: 100%;
        height: 50px;
      }
      .next-head-radio .el-radio-button {
        width: 33.33%;
        height: 100%;
      }
    }
    .radio-item {
      text-align: center;
      display: flex;
      justify-content: space-between;
      span {
        flex-grow: 0;
        flex-shrink: 0;
        font-size: 18px;
        display: flex;
        align-items: center;
      }
      .select-lang-button {
        display: flex;
        justify-content: flex-start;
        row-gap: 10px;
      }
    }
    .two-step {
      padding: 30px 70px 20px 70px;
      .checks-ug {
        direction: rtl;
      }
    }
    .btn {
      margin: 20px 70px;
      background: #139d59;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
    .bar-btns {
      margin: 0px 70px 20px 70px;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      .btn-item {
        width: 48%;
        padding: 15px 0;
        background: #666666;
      }
      .back {
        background: #139d59;
      }
    }
  }

  .usb-input,
  .usb-input-box {
    cursor: pointer;
  }

  .item-switch-box {
    width: 100%;
    height: 100%;
    border: 1px solid #ccc;
    border-radius: 4px;
    display: flex;
    .item-block-gray {
      background-color: #f5f7fa;
      color: #909399;
      border-inline-end: 1px solid #ccc;
      padding-inline: 20px;
      font-size: 18px;
    }
    .item-switch {
      flex-grow: 1;
      text-align: center;
    }
  }

  .check-block-box {
    display: flex;
    position: relative;
    .check-block-tab {
      background-color: #f5f7fa;
      border: 1px solid #ccc;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .check-block-item {
      position: relative;
      padding: 20px 0px;
      display: flex;
      flex-direction: column;
    }

    .check-block-line {
      width: 1px;
      height: calc(100% - 100px);
      background-color: #ccc;
      position: absolute;
      top: 80px;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>
