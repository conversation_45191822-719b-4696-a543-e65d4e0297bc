<template>
  <div style="height: 100%">
    <div class="wraps">
      <div class="top top-btn">
        <div
          class="clear-btn btn"
          @click="clearPrinterCacheHandler"
          :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
          v-if="isElectron"
        >
          <span>{{ gL("clearPrinterCache") }}({{ printerCacheCount }})</span>
          <i class="el-icon-delete"></i>
        </div>

        <div
          class="btn"
          @click="openAddBox"
          :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
        >
          <span>{{ gL("addPrinter") }}</span>
          <span class="iconfont icon-jia-copy-copy"></span>
        </div>
      </div>
      <div>
        <el-table
          :data="tableData"
          header-cell-class-name="header"
          :header-cell-style="{ background: '#2e3033' }"
          :cell-style="cell"
          row-class-name="row"
          v-loading="tableLoading"
          style="width: 100%"
        >
          <el-table-column
            :label="gL('serialNumber')"
            type="index"
            align="center"
            width="60"
          ></el-table-column>
          <el-table-column align="center" :label="gL('names')">
            <template slot-scope="scope">
              <span class="table-name">{{
                gLang == 1 ? scope.row.name_ug : scope.row.name_zh
              }}</span>
              <span v-if="scope.row.internet_type == 1" class="name-state">{{
                gL("printerStateCloud")
              }}</span>
              <span
                v-if="scope.row.printerState && scope.row.internet_type == 0"
                class="name-state"
                >{{
                  scope.row.printerState == "open"
                    ? gL("printerStateOpen")
                    : gL("printerStateClose")
                }}</span
              >
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" :label="gL('type')">
            <template slot-scope="scope">
              <span v-if="scope.row.type == 2">{{ gL("houtang") }}</span>
              <span v-if="scope.row.type == 1">{{ gL("bar") }}</span>
              <span v-if="scope.row.type == 3">{{ gL("totalPrinter") }}</span>
            </template>
          </el-table-column> -->
          <el-table-column align="center" width="140" :label="gL('food')">
            <template slot-scope="scope">
              <!-- <el-button v-if="scope.row.type==2" @click="openPrintBox(scope.row)" type="success" class="iconfont icon-shiliangzhinengduixiang"  circle></el-button> -->
              <el-button
                type="text"
                icon="iconfont icon-shiliangzhinengduixiang"
                @click="openPrintBox(scope.row)"
                circle
              ></el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" width="140" :label="gL('state')">
            <template slot-scope="scope">
              <el-switch
                active-color="#13ce66"
                inactive-color="#ff4949"
                :active-value="1"
                :inactive-value="0"
                v-model="scope.row.status"
                @change="value => changeSwitch(value, scope.$index, scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column
            :label="gL('operation')"
            prop="dosome"
            align="center"
            width="140"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="iconfont icon-shiliangzhinengduixiang"
                @click="editData(scope.row)"
                circle
              ></el-button>
              <span style="padding-right: 9px; padding-left: 15px">
                <span class="line"></span>
              </span>
              <el-button
                type="text"
                class="danger"
                icon="iconfont icon-qingkong"
                @click="delData(scope.row)"
                circle
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 模态框 -->
      <AddPrinter
        :show="addVipBox"
        :title="settingTitle"
        :boxType="boxType"
        :editData="editDataObj"
        @cancelAdd="cancelAdd"
        @confirm="confirmAddPrinter"
      />

      <!-- 打印的订单 -->
      <div class="mask print" v-if="print_box">
        <div class="box">
          <div class="title">
            <span></span>
            <span :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">{{
              gL("printFood")
            }}</span>
            <span class="iconfont icon-jia-copy" @click="cencelPrintBox"></span>
          </div>
          <div class="content-remark">
            <div class="wrap">
              <div class="top">
                <div class="menu" :class="gLang == 1 ? 'menu-ug' : 'menu-zh'">
                  <el-tabs type="card" class="menu-item" @tab-click="clickMenu">
                    <el-tab-pane
                      v-for="item in categories"
                      :key="item.id"
                      :label="item.name"
                      :id="item.id"
                    ></el-tab-pane>
                  </el-tabs>
                </div>
                <div class="serach"></div>
              </div>
              <!-- 餐桌 -->
              <div class="table">
                <div
                  class="boxs"
                  :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
                  :class="gLang == 1 ? 'boxs_ug' : ''"
                >
                  <el-checkbox
                    :indeterminate="isIndeterminatePrint"
                    v-model="checkPrintAll"
                    @change="handlePrintAllChange"
                    >{{ gL("chooiseAll") }}</el-checkbox
                  >
                  <div style="margin: 15px 0"></div>
                  <el-checkbox-group
                    v-model="checkedPrint"
                    @change="handleCheckedPrintChange"
                  >
                    <el-checkbox
                      v-for="item in categoryFoods"
                      :label="item.id"
                      :key="item.id"
                    >
                      <span>{{
                        gLang == 1 ? item.name_ug : item.name_zh
                      }}</span>
                      <span style="color: #666666" v-if="item.printer_alias"
                        > - {{ item.printer_alias }}</span
                      >
                    </el-checkbox>
                  </el-checkbox-group>
                  <div class="empty" v-if="categoryFoods.length == 0">
                    {{ gL("noMessage") }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="btn" @click="confrimPrint">{{ gL("confirm") }}</div>
        </div>
      </div>
      <modal
        :number_box="modal_box"
        :modal_content="modal_content"
        :modal_title="modal_title"
        @cancel="cancel"
        @confirm="confirmBox"
      ></modal>
    </div>
  </div>
</template>

<script>
import modal from "../components/modal.vue";
import AddPrinter from "./components/AddPrinter.vue";
import {
  getPrinterListAPI,
  putPrinterStateAPI,
  getPrinterFoodsAllAPI,
  postPrinterFoodsAPI,
  deletePrinterAPI
} from "./../api/index.js";
import queqeIndexedDB from "../print/QueqeIndexedDB.vue";
var self;
export default {
  async activated() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.getData();

    this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
    if (this.storeInfo != null) {
      this.printerCacheCount = await queqeIndexedDB.count();
    }
  },
  data() {
    return {
      gLang: 1,
      tableData: [],
      radion_index: 0,
      addVipBox: false,
      isBar: 3, //1后台&0收营台2总打印机
      settingTitle: this.gL("addPrinter"), //模态框标题
      boxType: "add", //是否添加
      id: "",
      categories: [],
      categoryFoods: [], //列表
      print_box: false, //打印
      checkedPrint: [], //已选美食
      checkPrintAll: false,
      isIndeterminatePrint: true,
      modal_box: false,
      modal_content: "",
      modal_title: "",
      uiData: [],
      editDataObj: {},
      tableLoading: false,
      printerCacheCount: 0,
      storeInfo: null,
      isElectron: window.electronAPI && window.electronAPI.isElectron
    };
  },
  mounted: function() {},
  methods: {
    // 清空打印队列缓存
    async clearPrinterCacheHandler() {
      await queqeIndexedDB.clearAll();
      this.printerCacheCount = await queqeIndexedDB.count();

      // if (this.storeInfo == null) {
      //   this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"))
      // }
      // const res = window.electronAPI && await window.electronAPI.clearPrinterCache(this.storeInfo.merchant_no)
      // if (res == "success") {
      //   this.$message({
      //     message: this.gL("successfulOperation"),
      //     type: "success",
      //     customClass: this.$toastClass(),
      //     offset: 120
      //   });
      //   this.printerCacheCount = 0
      // } else {
      //   this.$message({
      //     message: res,
      //     type: "error",
      //     customClass: this.$toastClass(),
      //     offset: 120
      //   });
      // }
    },

    radioCheck(e) {
      console.log(e);
    },
    //获取数据
    getData() {
      this.tableLoading = true;
      getPrinterListAPI()
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.tableData = response.data.data;
            const data = [];
            for (let i = 0; i < response.data.data.length; i++) {
              if (response.data.data[i].internet_type == 0) {
                data.push({
                  id: response.data.data[i].id,
                  keyStr: response.data.data[i].printer_ip
                });
              }
            }
            window.electronAPI &&
              window.electronAPI.getPrinterState(data).then(printerState => {
                if (printerState) {
                  const data = JSON.parse(printerState);
                  for (let i = 0; i < data.length; i++) {
                    for (let j = 0; j < this.tableData.length; j++) {
                      if (data[i].id == this.tableData[j].id) {
                        this.$set(
                          this.tableData[j],
                          "printerState",
                          data[i].enable ? "open" : "close"
                        );
                      }
                    }
                  }
                }
              });
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    //开关
    changeSwitch(value, index, row) {
      putPrinterStateAPI(row.id, { status: value })
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
          }
        })
        .catch(err => {
          this.getData();
        });
    },
    //编辑
    editData(row) {
      this.addVipBox = true;
      this.boxType = "edit";
      this.settingTitle = self.gL("editPrint");
      this.editDataObj = { ...row, printer_size: row.size };
    },
    //删除
    delData(row) {
      self.id = row.id;
      self.modal_box = true;
      if (self.gLang == 1) {
        self.modal_content =
          "《" +
          row.name_ug +
          "》" +
          self.gL("confirs") +
          self.gL("confirmdelete");
      } else {
        self.modal_content =
          self.gL("confirmdelete") +
          "《" +
          row.name_zh +
          "》" +
          self.gL("confirs");
      }
      self.modal_title = self.gL("tips");
    },
    //添加打印机模态框
    openAddBox() {
      // self.getPrinterList();
      this.addVipBox = true;
      this.boxType = "add";
      this.gL("addPrinter");
      this.editDataObj = {};
    },

    // 添加打印机成功
    async confirmAddPrinter() {
      this.addVipBox = false;
      this.getData();
      this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      if (this.storeInfo != null) {
        this.printerCacheCount =
          window.electronAPI &&
          (await window.electronAPI.getPrinterCache(
            this.storeInfo.merchant_no
          ));
      }
    },

    //下一步关掉
    async cancelAdd() {
      this.addVipBox = false;
      this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      if (this.storeInfo != null) {
        this.printerCacheCount =
          window.electronAPI &&
          (await window.electronAPI.getPrinterCache(
            this.storeInfo.merchant_no
          ));
      }
    },

    ///打印美食模态框
    //选择Top菜单
    openPrintBox(e) {
      self.print_box = true;
      self.id = e.id;
      self.getList(e.id);
    },
    cencelPrintBox() {
      self.print_box = false;
      self.checkedPrint = [];
    },
    clickMenu(tab) {
      self.active_menu = tab.$attrs.id;
      self.checkPrintAll = false;
      self.foodList();
    },
    /**
     * 获取区域
     */
    getList(e) {
      getPrinterFoodsAllAPI({
        printer_id: e
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.uiData = response.data.data;
          self.categories = [];
          self.uiData.forEach(element => {
            self.categories.push({
              id: element.id,
              name: this.gLang == 1 ? element.name_ug : element.name_zh
            });

            let categoryFoods = element.foods;
            if (categoryFoods.length != 0) {
              categoryFoods.forEach(item => {
                item.printers.forEach(it => {
                  if (item.printer_alias == null) {
                    item.printer_alias = "";
                  }
                  item.printer_alias +=
                    item.printer_alias == ""
                      ? this.gLang == 1
                        ? it.name_ug
                        : it.name_zh
                      : this.gLang == 1
                      ? " , " + it.name_ug
                      : " , " + it.name_zh;
                  if (it.id == e) {
                    self.checkedPrint.push(item.id);
                  }
                });
              });
            }
          });
          self.active_menu = response.data.data[0].id;
          self.foodList();
        }
      });
    },
    /**
     * 餐桌列表
     */
    foodList() {
      self.uiData.forEach(element => {
        if (self.active_menu == element.id) {
          self.categoryFoods = element.foods;
          if (self.categoryFoods.length != 0) {
            self.categoryFoods.forEach(item => {
              if (item.printer_id == self.id) {
                self.checkedPrint.push(item.id);
              }
            });
          }
          return;
        }
      });
    },
    handlePrintAllChange(val) {
      if (val) {
        self.categoryFoods.forEach(item => {
          self.checkedPrint.push(item.id);
        });
      } else {
        self.checkedPrint = [];
      }
      self.isIndeterminatePrint = false;
    },
    handleCheckedPrintChange(value) {
      let checkedCount = value.length;
      self.checkPrintAll = checkedCount === self.categoryFoods.length;
      self.isIndeterminatePrint =
        checkedCount > 0 && checkedCount < self.categoryFoods.length;
    },
    //提交打印美食
    confrimPrint() {
      postPrinterFoodsAPI({
        printer_id: self.id,
        food_ids: this.checkedPrint
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
          self.cencelPrintBox();
        }
      });
    },
    //模态框
    cancel() {
      self.modal_box = false;
    },
    confirmBox() {
      self.cancel();
      self.delRowData();
    },
    delRowData() {
      deletePrinterAPI(self.id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
          self.getData();
        }
      });
    },
    cell({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 2) {
        return "direction:rtl";
      }
      if (columnIndex == 3) {
        return "direction:ltr";
      }
    }
  },
  components: {
    modal,
    AddPrinter
  }
};
</script>

<style lang="less" scoped>
.name-state {
  padding: 0px 8px;
  font-size: 20px;
  color: #656565;
}
.wraps {
  width: 100%;
  height: 100%;
  .top {
    padding: 0 20px;
    background-color: #2e3033;
    color: #ffffff;
    height: 9%;
    align-items: center;
    border-bottom: 1px solid #666666;
    &.top-btn {
      display: flex;
      justify-content: flex-end;
      column-gap: 20px;
    }
    .btn {
      background-color: #ff9c00;
      width: 250px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 25px;
      height: 80%;
      font-size: 26px;
      cursor: pointer;
      .iconfont {
        font-size: 19px;
      }
    }
    .clear-btn {
      width: auto;
      .el-icon-delete {
        font-size: 19px;
        padding: 0px 10px;
        margin-bottom: 6px;
      }
    }
    .detail-tit {
      font-size: 26px;
    }
  }

  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .box {
      background-color: #ffffff;
      width: 1100px;
      font-size: 26px;
      .title {
        background-color: #e6e6e6;
        color: #1a1a1a;
        padding: 25px 20px;
        position: relative;
        text-align: center;
        .iconfont {
          position: absolute;
          right: 20px;
          font-size: 23px;
          color: #666666;
          cursor: pointer;
        }
      }
      .content {
        padding: 30px 270px 0px 270px;
        .item {
          margin-bottom: 25px;
          width: 100%;
        }
        .test-btn-box {
          width: 100%;
          text-align: right;
        }
        .test {
          button {
            font-size: 20px;
          }
        }
      }
      .radio-item {
        text-align: center;
        display: flex;
        justify-content: space-between;
        span {
          flex-grow: 0;
          flex-shrink: 0;
          font-size: 18px;
          display: flex;
          align-items: center;
        }
      }
      .two-step {
        padding: 30px 70px 20px 70px;
        .checks-ug {
          direction: rtl;
        }
      }
      .btn {
        margin: 20px 70px;
        background: #139d59;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        padding: 15px 0;
        cursor: pointer;
      }
      .bar-btns {
        margin: 20px 70px;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        display: flex;
        justify-content: space-between;
        cursor: pointer;
        .btn-item {
          width: 48%;
          padding: 15px 0;
          background: #666666;
        }
        .back {
          background: #139d59;
        }
      }
    }
  }
  .print {
    height: 100%;
    width: 100%;
    padding-right: 0;
    .box {
      height: 85%;
      .content-remark {
        height: 78%;
        .wrap {
          height: 100%;
          width: 100%;
          padding-right: 0;
        }
      }
    }
    .top {
      height: 50px;
      width: 100%;
      background-color: #333333;
      padding: 5px 10px;
      .menu {
        width: 100%;
        display: flex;
        color: #ffffff;
        height: 100%;
        .menu-item {
          width: 100%;
          height: 100%;
          font-size: 26px;
          cursor: pointer;
        }
        .active {
          background-color: #139d59;
        }
      }
    }
    //餐桌
    .table {
      padding-right: 0;
      height: 89%;
      width: 100%;
      position: relative;
      overflow-y: scroll;
      .boxs {
        padding-top: 30px;
        width: 95%;
        margin: 0 auto;
      }
    }
  }
  .line {
    color: #fff;
    border-left: 1px solid #ccc;
    padding-right: 10px;
  }
  .danger {
    color: #ff5151;
  }
}
@media screen and (max-width: 1366px) {
}
</style>
