<template>
  <el-dialog
    :visible.sync="show"
    width="50%"
    :custom-class="gLang == 1 ? 'ug-dialog' : 'zh-dialog'"
    :before-close="handleClose"
  >
    <template slot="title">
      <div class="title-box">
        <div class="title">{{ gL("refundFoodTitle") }}</div>
        <div class="title-time" :style="{ direction: 'ltr' }">{{ data.updated_at }}</div>
      </div>
    </template>
    <div class="food-list-box">
      <div class="food-item food-header">
        <div class="item-block food-price">{{ gL("prices") }}</div>
        <div class="item-block food-count">{{ gL("count") }}</div>
        <div class="item-block food-name">{{ gL("foodName") }}</div>
      </div>
      <div class="food-item" v-for="food in data.refund_foods">
        <div class="item-block">￥{{ food.total_price }}</div>
        <div class="item-block">X{{ food.foods_count }}</div>
        <div class="item-block">{{ food.food_name }}</div>
      </div>
      <div class="food-item food-footer">
        <div class="item-block">
          <span>{{ gL("prices") }} : </span>
          <span style="display: inline-block;">￥{{ getSumPrice }}</span>
        </div>
        <div class="item-block">
          <span>{{ gL("count") }} : </span>
          <span>{{ getSumCount }}</span>
        </div>
        <div class="item-block"></div>
      </div>
    </div>

    <div class="refund-payment-title">{{ gL("refundType") }}</div>
    <div class="refund-payment-box">
      <div class="food-item" v-for="payment in data.refunds">
        <div
          class="item-block"
          :style="{ direction: 'ltr', textAlign: gLang == 1 ? 'start' : 'end' }"
        >
          {{ payment.refund_at }}
        </div>
        <div class="item-block">￥{{ payment.amount }}</div>
        <div class="item-block">{{ payment.name }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "paymentDialog",
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    getSumCount() {
      if (this.data.refund_foods) {
        return (
          Math.round(
            this.data.refund_foods.reduce((total, item) => {
              return total + item.foods_count;
            }, 0) * 100
          ) / 100
        );
      }
    },
    getSumPrice() {
      if (this.data.refund_foods) {
        return (
          Math.round(
            this.data.refund_foods.reduce((total, item) => {
              return total + item.total_price;
            }, 0) * 100
          ) / 100
        );
      }
    }
  },
  data() {
    return {
      gLang: 1,
      data: {}
    };
  },
  methods: {
    handleClose(done) {
      this.$emit("update:show", false);
    },

    // 显示弹出框
    showDialog(row) {
      this.data = row;
      this.$emit("update:show", true);
    }
  }
};
</script>

<style lang="less" scoped>
.title-box {
  text-align: center;
  position: relative;
  .title {
    font-size: 24px;
  }
  .title-time {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 18px;
    color: #888;
  }
}
.food-list-box {
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  max-height: 360px;
  overflow-y: auto;
}
.rtl {
  direction: rtl;
  text-align: end;
}

.refund-payment-title {
  font-size: 22px;
  border-bottom: 1px solid #666;
  padding-bottom: 10px;
  margin-top: 20px;
}

.refund-payment-box {
  padding: 10px 0;
}

.food-item {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  padding: 10px 0;
  font-size: 18px;

  &.food-header {
    font-weight: bold;
    font-size: 18px;
    color: #888;
    border-bottom: 1px solid #666;
    padding-bottom: 10px;
  }

  &.food-footer {
    font-size: 18px;
    border-top: 1px solid #666;
    padding: 10px 0px;
  }

  .item-block {
    flex: 1;
    &:nth-child(1) {
      text-align: end;
    }
    &:nth-child(2) {
      text-align: center;
    }
  }
}
</style>
