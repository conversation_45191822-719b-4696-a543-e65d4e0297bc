import { direction } from
'html2canvas/dist/types/css/property-descriptors/direction';
<template>
  <div class="mask" v-if="show">
    <div class="box">
      <div class="title">
        <div class="title-left"></div>
        <span
          :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          class="title-table"
          >{{ gL("refundDialogTitle") }}</span
        >
        <span
          class="close iconfont icon-jia-copy"
          @click="cancelModalBox"
        ></span>
      </div>
      <div class="content" :class="gLang == 1 ? 'content-ug' : 'content-zh'">
        <div class="content-item item-flex">
          <div class="food-box">
            <div class="food-title-box">
              <div class="food-check">
                <el-checkbox class="food-check-box" @change="checkAllChange">{{
                  gL("all")
                }}</el-checkbox>
              </div>
              <div class="food-title">{{ gL("refundDialogFoodTitle") }}</div>
            </div>
            <div
              class="food-item"
              v-for="(item, index) in data.bill_details"
              :key="item.id"
              v-if="item.state == 3"
            >
              <div class="food-action" v-if="item.food_format_id == 1">
                <div
                  class="action-btn btn-plus"
                  :class="item.selectCount == item.foods_count ? 'active' : ''"
                  @click="foodPlus(item)"
                >
                  <i class="add iconfont icon-jia1"></i>
                </div>
                <div class="action-count">{{ item.selectCount }}</div>
                <div
                  class="action-btn btn-minus"
                  :class="item.selectCount == 0 ? 'active' : ''"
                  @click="foodMinus(item)"
                >
                  <i class="minus iconfont icon-jian"></i>
                </div>
              </div>
              <div class="food-action" v-if="item.food_format_id == 2">
                <div
                  class="icon-edit"
                  @click.stop="clickFoodNow(item)"
                  :style="{
                    marginLeft: gLang == 2 ? '15px' : '',
                    marginRight: gLang == 1 ? '15px' : ''
                  }"
                >
                  <span class="iconfont icon-shiliangzhinengduixiang"></span>
                </div>
              </div>
              <div class="food-price">￥{{ item.unit_price }}</div>
              <div class="food-name">
                <span class="food-count">x{{ item.foods_count }}</span>
                <span>{{ item.food_name }}</span>
              </div>
            </div>
          </div>
          <div class="footer-box">
            <div class="footer-count">
              <div class="footer-num">X{{ getSumCount }}</div>
              <div class="num-text">{{ gL("count") }}</div>
            </div>
            <div class="footer-price">
              <div class="footer-num">{{ formatNumber(getSumPrice) }}</div>
              <div class="num-text">{{ gL("price") }}</div>
            </div>
            <div class="footer-title">{{ gL("refundDialogFooterTitle") }}</div>
          </div>
        </div>
        <div class="content-item">
          <div class="refund-info-title line-box">
            <div class="refund-total-price">
              <span class="price">{{ formatNumber(getSumPrice) }}</span>
              <span>{{ gL("refundDialogTotalAmount") }}</span>
            </div>
            <div class="info-title">{{ gL("refundDialogInfoTitle") }}</div>
          </div>
          <div class="refund-recharge-box line-box">
            <div class="recharge-price">￥{{ data.ignore_price }}</div>
            <div class="recharge-title">{{ gL("PreferentialAmount") }}</div>
          </div>

          <!-- 支付类型选择 -->
          <div
            class="payment-box"
            ref="scrollContainer"
            @mousedown="startDrag"
            @mousemove="onDrag"
            @mouseup="endDrag"
            @mouseleave="endDrag"
          >
            <div class="scroll-content">
              <div
                class="payment-item"
                :class="{
                  active: paymentList.find(item => item.id == payment.id)
                }"
                v-for="payment in data.payments"
                :key="payment.id"
                @click="paymentClick(payment)"
              >
                <div class="item-top">
                  <div class="item-price">￥{{ payment.amount }}</div>
                  <div class="item-name-box">
                    <div class="item-name">{{ payment.name }}:</div>
                    <el-image
                      style="width: 22px; height: 22px;"
                      :src="payment.icon"
                      fit="contain"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>
                </div>
                <div class="item-bottom">
                  <span>{{ gL("totalVegetables") }}</span>
                  <span>￥{{ payment.refund_amount }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 要退款的金额 -->
          <div class="refund-price-box">
            <div class="refund-price-item" v-for="refund in paymentList">
              <span>{{ refund.name }}:</span>
              <span>￥{{ refund.requiredAmount }}</span>
            </div>
          </div>

          <!-- 退款备注 -->
          <div class="remarks-box">
            <div class="remarks-title">{{ gL("refundRemarks") }}</div>
            <div class="remarks-list-box">
              <div class="remarks-list">
                <div
                  class="remarks-item"
                  :class="item.active ? 'active' : ''"
                  v-for="item in remarks"
                  :key="item.id"
                  @click="clickRemarkItem(item)"
                >
                  {{ item.name }}
                </div>
              </div>
            </div>

            <div
              class="remarks-input"
              :style="{ direction: gLang == 1 ? 'rtl' : 'ltr' }"
            >
              <el-input
                type="text"
                :placeholder="gL('refundRemarks')"
                v-model="textarea"
              >
              </el-input>
            </div>
          </div>

          <!-- 退款按钮 -->
          <div class="btn-box">
            <el-button
              class="refund-btn"
              type="primary"
              @click="refundConfrim"
              >{{ gL("confirm") }}</el-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatNumber } from "./../../utils/utils.js";
export default {
  name: "refundDialog",
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      glang: 1,
      data: {},
      remarks: [],
      isDragging: false,
      startX: 0, // 鼠标按下时的位置
      scrollLeft: 0, // 容器当前滚动位置
      textarea: "",
      paymentList: [],
      isClicking: "clicking", // 是否正在点击
      formatNumber: formatNumber
    };
  },
  computed: {
    getSumCount() {
      return this.data.bill_details.reduce(
        (total, item) => total + item.selectCount,
        0
      );
    },
    // getSumPrice() {
    //   return this.data.bill_details.reduce(
    //     (total, item) => total + item.selectCount * item.unit_price,
    //     0
    //   );
    // }
    getSumPrice() {
      let sum = 0;
      for (const item of this.data.bill_details) {
        // 将价格转换为分计算，避免小数问题
        const priceInCents = Math.round(item.unit_price * 100);
        const totalInCents = item.selectCount * priceInCents;
        sum += totalInCents;
      }
      return sum / 100; // 转换回元
    }
  },
  methods: {
    // 显示称重美食弹出框
    clickFoodNow(e) {
      this.$emit("weightFood", e);
    },

    clickRemarkItem(item) {
      item.active = true;
      this.textarea +=
        this.textarea == ""
          ? item.name
          : this.gLang == 1
          ? "،" + item.name
          : "," + item.name;
    },
    paymentClick(payment) {
      if (this.isClicking == "draging") return;
      if (payment.amount == payment.refund_amount) return;

      if (this.getSumCount == 0) {
        this.$message({
          message: this.gL("refundDialogPaymentTips"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      if (this.paymentList.find(item => item.id == payment.id)) {
        this.paymentList = this.paymentList.filter(
          item => item.id != payment.id
        );
      } else {
        const totalPrice = this.paymentList.reduce(
          (total, item) => total + (item.amount - item.refund_amount),
          0
        );
        if (totalPrice < this.getSumPrice) {
          let amount = payment.amount - payment.refund_amount;
          this.paymentList.push({
            ...payment,
            requiredAmount:
              this.getSumPrice - totalPrice >= amount
                ? amount
                : this.getSumPrice - totalPrice
          });
        }
      }
    },
    refundConfrim() {
      if (this.paymentList.length == 0) {
        this.$message({
          message: this.gL("refundDialogPayment"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      const totalPrice = this.paymentList.reduce(
        (total, item) => total + item.amount,
        0
      );
      if (totalPrice < this.getSumPrice) {
        this.$message({
          message: this.gL("refundAmountDifferent"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      if (this.textarea == "") {
        this.$message({
          message: this.gL("chooiseRetire"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      const data = {
        payments: {},
        order_details: []
      };

      for (let i = 0; i < this.paymentList.length; i++) {
        data.payments[this.paymentList[i].id] = this.paymentList[
          i
        ].requiredAmount;
      }

      for (let i = 0; i < this.data.bill_details.length; i++) {
        if (this.data.bill_details[i].selectCount > 0) {
          data.order_details.push({
            id: this.data.bill_details[i].id,
            foods_count: this.data.bill_details[i].selectCount,
            remarks: this.textarea
          });
        }
      }

      this.$emit("refundConfrim", data);
    },
    startDrag(e) {
      this.isClicking = "started";
      this.isDragging = true;
      this.startX = e.pageX;
      this.scrollLeft = this.$refs.scrollContainer.scrollLeft;
    },
    onDrag(e) {
      this.isClicking = "draging";
      if (!this.isDragging) return;
      e.preventDefault(); // 防止选中文本等默认行为
      const x = e.pageX;
      const walk = x - this.startX; // 拖动的距离
      this.$refs.scrollContainer.scrollLeft = this.scrollLeft - walk;
    },
    endDrag() {
      if (this.isClicking == "started") {
        this.isClicking = "clicking";
      }
      this.isDragging = false;
    },
    checkAllChange(value) {
      this.paymentList = [];
      for (let i = 0; i < this.data.bill_details.length; i++) {
        if (this.data.bill_details[i].state == 3) {
          this.data.bill_details[i].selectCount = value
            ? this.data.bill_details[i].foods_count
            : 0;
        }
      }

      if (value) {
        // 自动选择全部支付类型
        this.paymentList = [];
        this.data.payments.forEach(item => {
          let amount = item.amount - item.refund_amount;
          this.paymentList.push({
            ...item,
            requiredAmount: amount
          })
        })
      } else {
        this.paymentList = [];
      }
    },
    foodMinus(item) {
      if (item.selectCount > 0) {
        this.paymentList = [];
        item.selectCount--;
      }
    },
    foodPlus(item) {
      if (item.selectCount < item.foods_count) {
        this.paymentList = [];
        item.selectCount++;
      }
    },
    foodChange(number, id) {
      this.data.bill_details.forEach(item => {
        if (item.id == id) {
          item.selectCount = parseFloat(number);
        }
      });
    },
    cancelModalBox(done) {
      this.$emit("update:show", false);
    },
    showDialog(data, remarks) {
      this.gLang = localStorage.getItem("langId");
      this.paymentList = [];
      this.textarea = "";
      this.$emit("update:show", true);
      for (let i = 0; i < data.bill_details.length; i++) {
        this.$set(data.bill_details[i], "selectCount", 0);
      }
      this.data = data;
      console.log("-------- remarks ------", remarks);
      this.remarks = remarks;
    }
  }
};
</script>

<style>
.food-check-box .el-checkbox__label {
  font-size: 20px;
}
</style>

<style lang="less" scoped>
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 80vw;
    min-width: 876px;
    font-size: 30px;
    padding-inline: 32px;
    .title {
      color: #1a1a1a;
      padding: 32px 32px 15px;
      position: relative;
      text-align: center;
      display: flex;
      justify-content: center;
      border-bottom: 1px solid #666666;
      .title-table {
        display: inline-block;
        flex: 2;
      }
      .title-left {
        flex: 1;
      }
      .close {
        flex: 1;
        text-align: right;
        font-size: 26px;
        color: #666666;
        cursor: pointer;
      }
    }
    .content {
      font-size: 18px;
      display: flex;
      &.content-ug {
        flex-direction: row;
        .content-item:first-child {
          border-inline-end: 1px solid #666666;
          padding-inline-end: 20px;
        }
        .content-item:last-child {
          padding-inline-start: 20px;
        }
        .food-title-box {
          flex-direction: row;
        }
        .food-box .food-item {
          flex-direction: row;
        }
        .food-box .food-item .food-name {
          flex-direction: row;
        }
        .footer-box {
          flex-direction: row;
        }
        .line-box {
          flex-direction: row;
        }
        .payment-box {
          flex-direction: row-reverse;
        }
        .payment-box .item-top {
          direction: rtl;
        }
        .item-bottom {
          direction: rtl;
        }
        .refund-price-box {
          flex-direction: row-reverse;
        }
        .remarks-box .remarks-title {
          text-align: end;
        }
        .remarks-box .remarks-list {
          flex-direction: row;
        }
      }
      &.content-zh {
        flex-direction: row-reverse;
        .content-item:first-child {
          border-inline-start: 1px solid #666666;
          padding-inline-start: 20px;
        }
        .content-item:last-child {
          padding-inline-end: 20px;
        }
        .food-title-box {
          flex-direction: row-reverse;
        }
        .food-box .food-item {
          flex-direction: row-reverse;
        }
        .food-box .food-item .food-name {
          flex-direction: row-reverse;
        }
        .footer-box {
          flex-direction: row-reverse;
        }
        .line-box {
          flex-direction: row-reverse;
        }
        .payment-box {
          flex-direction: row;
        }
        .payment-box .item-top {
          direction: ltr;
        }
        .item-bottom {
          direction: ltr;
        }
        .refund-price-box {
          flex-direction: row;
        }
        .remarks-box .remarks-title {
          text-align: start;
        }
        .remarks-box .remarks-list {
          flex-direction: row-reverse;
        }
      }
      .content-item {
        flex-grow: 1;
        flex-shrink: 0;
        flex-basis: 50%;
        width: 50%;
        min-height: 680px;
        box-sizing: border-box;
        padding: 20px 0px;
      }

      .food-title-box {
        font-size: 22px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
      }

      .food-box {
        height: 580px;
        overflow-y: auto;
        .food-item {
          display: flex;
          align-items: center;
          padding: 12px 0px;
          border-top: 1px dotted #666666;
          .food-action {
            display: flex;
            width: 90px;
            justify-content: space-between;
            align-items: center;
            color: #139d59;
            cursor: pointer;
            .icon-edit .iconfont {
              font-size: 22px;
            }
            .action-btn .iconfont {
              font-size: 24px;
            }
            .action-btn.active {
              color: #bbb;
              cursor: not-allowed;
            }
          }
          .food-price {
            text-align: center;
            width: 120px;
          }
          .food-name {
            flex: 1;
            display: flex;
            justify-content: flex-end;
            .food-count {
              padding: 0px 10px;
            }
          }
        }
      }

      .footer-box {
        padding: 10px 0px;
        border-top: 1px solid #666;
        height: 100px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .footer-num {
          font-size: 28px;
          color: #139d59;
          margin-bottom: 6px;
        }
        .num-text {
          color: #999;
        }
        .footer-count,
        .footer-price {
          padding: 0px 10px;
          text-align: center;
        }
      }

      .line-box {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px dotted #666;
        padding-bottom: 10px;
      }
      .info-title {
        font-size: 22px;
      }
      .price {
        color: #139d59;
      }
      .refund-recharge-box {
        padding: 16px 0px;
        .recharge-price {
          color: red;
        }
      }

      .payment-box {
        width: 100%;
        padding: 16px 0px;
        border-bottom: 1px dotted #666;
        display: flex;
        overflow-x: auto;
        white-space: nowrap;
        cursor: grab;
        position: relative;
        .payment-item {
          border: 1px solid #666;
          border-radius: 4px;
          padding: 8px 8px;
          margin-right: 10px;
          &.active {
            border-color: #139d59;
            background-color: rgba(19, 157, 89, 0.2);
          }
          &:last-child {
            margin-right: 0px;
          }
          .image-slot .el-icon-picture-outline {
            font-size: 24px;
          }
          .item-top {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
            column-gap: 20px;
            .item-name-box {
              display: flex;
              align-items: center;
              flex-direction: row-reverse;
              font-size: 20px;
            }
            .item-name {
              padding: 0px 6px;
              color: #999;
            }
          }
          .item-bottom {
            font-size: 16px;
            color: #999;
            margin-top: 8px;
            text-align: end;
          }
        }

        .scroll-content {
          padding: 0px 10px;
          display: inline-flex;
        }
      }

      .refund-price-box {
        padding-top: 20px;
        padding-bottom: 10px;
        display: flex;
        flex-wrap: wrap;
        .refund-price-item {
          padding: 0px 10px;
        }
      }

      .remarks-box {
        .remarks-title {
          padding: 20px 0px;
          font-size: 22px;
        }
        .remarks-list-box {
          height: 250px;
          overflow-y: auto;
        }
        .remarks-list {
          display: flex;
          column-gap: 10px;
          row-gap: 10px;
          flex-wrap: wrap;
          .remarks-item {
            height: 60px;
            width: calc(50% - 5px);
            background-color: #eee;
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            border: 1px solid transparent;
            &.active {
              border-color: #139d59;
              color: #139d59;
            }
          }
        }
        .remarks-input {
          margin-top: 20px;
        }
      }

      .refund-btn {
        width: 100%;
        margin-top: 20px;
        font-size: 22px;
      }
    }
  }
}
</style>
