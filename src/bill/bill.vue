<template>
  <div class="wraps staff">
    <div class="top">
      <div class="search">
        <el-date-picker
          v-model="picker"
          prefix-icon="el-icon-date"
          type="datetimerange"
          :editable="false"
          range-separator="~"
          value-format="yyyy-MM-dd HH:mm:ss"
          :start-placeholder="gL('startTime')"
          :end-placeholder="gL('endTime')"
          :picker-options="pickerBeginDateBefore"
          :popper-class="gLang == 1 ? 'uy-date' : 'zh-date'"
          :default-time="['00:00:00', '23:59:59']"
          @change="serach"
        >
        </el-date-picker>
        <div class="btn-search" @click="serach">{{ gL("serach") }}</div>
      </div>
      <div class="prices" :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">
        <div>
          <span>{{ gL("amountCollected") }}: </span>
          <span class="num">￥{{ $toMoney(amount) }}</span>
        </div>
        <div>
          <span>{{ gL("businessSales") }}: </span>
          <span class="num">￥{{ $toMoney(sales) }}</span>
        </div>
      </div>
    </div>
    <div class="top">
      <div class="search" style="justify-content: space-between">
        <div class="serach">
          <div class="input">
            <span class="iconfont icon-search searchs"></span>
            <input
              type="text"
              maxlength="8"
              :placeholder="gL('tableName')"
              v-model="serachText"
              v-on:input="inputSearch"
            />
          </div>
          <div class="del" v-if="delete_text" @click="removeSerach">
            <span class="iconfont icon-jia-copy"></span>
          </div>
        </div>
        <!-- <div class="select">
          <el-select
            v-model="pay_id"
            clearable
            filterable
            :placeholder="gL('paymentMethod')"
            @change="changeSlecet"
          >
            <el-option
              v-for="item in payList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div> -->
        <div class="select">
          <el-select
            v-model="user_id"
            clearable
            filterable
            :placeholder="gL('staff')"
            @change="changeSlecet"
          >
            <el-option
              v-for="item in userList"
              v-if="item.id != 1"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="day-box">
        <div
          class="day-item"
          :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          :class="activeDay == item.id ? 'active' : ''"
          v-for="(item, index) in day_list"
          @click="() => activeDay != item.id && clickDay(item)"
          :key="index"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        header-cell-class-name="headers"
        :header-cell-style="{ background: '#e6e6e6' }"
        row-class-name="row"
        :cell-style="cell"
        @row-click="rowClick"
        v-loading="loading"
        style="width: 100%"
        height="100%"
      >
        <el-table-column
          :label="gL('tableName')"
          prop="table_name"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="order_no"
          align="center"
          width="220"
          :label="gL('ordeNum')"
        >
        </el-table-column>
        <el-table-column
          prop="original_price"
          align="center"
          :label="gL('total_price')"
        >
        </el-table-column>
        <el-table-column
          prop="price"
          align="center"
          :label="gL('amountCollected')"
        >
        </el-table-column>
        <!-- <el-table-column
          prop="payment_type"
          align="center"
          :label="gL('paymentMethod')"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.mix_pay == 1">{{ gL("blendPay") }}</span>
            <span v-else-if="scope.row.payment_type == null">{{
              gL("refunded")
            }}</span>
            <span v-else>{{ scope.row.payment_type }}</span>
          </template>
        </el-table-column> -->
        <el-table-column
          prop="cashier_name"
          align="center"
          :label="gL('cashier')"
        >
        </el-table-column>
        <el-table-column
          prop="total_refund_amount"
          align="center"
          :label="gL('drawback')"
        >
        </el-table-column>
        <el-table-column
          prop="cancel_amount"
          align="center"
          :label="gL('cancelFoodCol')"
        >
        </el-table-column>
        <el-table-column
          prop="paid_at"
          align="center"
          width="230"
          :label="gL('checkOutTime')"
        >
        </el-table-column>
      </el-table>
      <div class="paging">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
          layout="total, prev, pager, next"
          :page-size="pageSize"
          :total="totalCount"
        >
        </el-pagination>
      </div>
    </div>
    <div class="mask" v-if="modal_box">
      <div class="box">
        <div class="title">
          <div class="title-left">
            <!-- <span
              class="print-icon iconfont icon-dayinji-"
              @click="invoice"
            ></span> -->
            <span
              @click="showRetireBox"
              class="btn-refund"
              v-show="current_bill_available_count > 0"
              >{{ gL("refund") }}</span
            >
          </div>
          <span
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            class="title-table"
            >{{ modal_title }}: {{ row_data.table_name }}</span
          >
          <span
            class="close iconfont icon-jia-copy"
            @click="cancelModalBox"
          ></span>
        </div>
        <div class="content">
          <div class="info" :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">
            <div class="info-item">
              <div class="info-column">
                <div class="column-item">{{ gL("pepoleCount") }}:</div>
                <div class="column-item">{{ gL("ordeNum") }}:</div>
              </div>
              <div class="info-column">
                <div class="column-item">{{ row_data.customers_count }}</div>
                <div class="column-item">{{ row_data.order_no }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-column">
                <div class="column-item">{{ gL("openingTime") }}:</div>
                <div class="column-item">{{ gL("orderingClerk") }}:</div>
              </div>
              <div class="info-column no-flex">
                <div class="column-item ltr">{{ row_data.created_at }}</div>
                <div class="column-item">{{ row_data.creator_name }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-column">
                <div class="column-item">{{ gL("checkOutTime") }}:</div>
                <div class="column-item">{{ gL("cashier") }}:</div>
              </div>
              <div class="info-column no-flex">
                <div class="column-item ltr">{{ row_data.paid_at }}</div>
                <div class="column-item">{{ row_data.cashier_name }}</div>
              </div>
            </div>
          </div>
          <div class="detail-box">
            <div
              class="detail detail-refund"
              :class="gLang == 1 ? 'detail-ug' : 'detail-zh'"
            >
              <template>
                <el-table
                  :data="row_data.refund_batches"
                  :header-cell-style="{ background: '#ffffff' }"
                  :row-style="{ background: '#ffffff' }"
                  style="width: 100%"
                >
                  <el-table-column
                    :align="gLang == 1 ? 'left' : 'right'"
                    width="230px"
                  >
                    <template slot="header">
                      <span>{{ gL("refundCount") }} : </span>
                      <span>{{
                        row_data.refund_batches &&
                          row_data.refund_batches.length
                      }}</span>
                    </template>
                    <template slot-scope="scope">
                      <el-button
                        class="detail-btn"
                        type="text"
                        @click="showRefundPaymentDetail(scope.row)"
                        >{{ gL("detail") }}</el-button
                      >
                      <div class="updated-at">
                        <span>{{ scope.row.updated_at }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :align="gLang == 2 ? 'left' : 'right'"
                    :label="gL('refundFoodTitle')"
                  >
                    <template slot-scope="scope">
                      <div class="food-title">{{ gL("orderRefund") }}</div>
                      <div class="food-price">
                        <span>{{ gL("prices") }} : </span>
                        <span style="display: inline-block;"
                          >￥{{ scope.row.total_amount.toFixed(2) }}</span
                        >
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </div>
            <div
              class="detail detail-food"
              :class="gLang == 1 ? 'detail-ug' : 'detail-zh'"
            >
              <template>
                <el-table
                  :data="row_data.bill_details"
                  :header-cell-style="{ background: '#ffffff' }"
                  :row-style="{ background: '#ffffff' }"
                  style="width: 100%"
                >
                  <el-table-column align="center" width="210px">
                    <template slot="header">
                      <div :style="{direction: gLang == 1 ? 'rtl' : 'ltr'}">
                        <span>{{ gL("prices") }} : </span>
                        <span style="display: inline-block;"
                          >￥{{ row_data.price }}</span
                        >
                      </div>
                    </template>
                    <template slot-scope="scope">
                      <span style="margin-left: 10px"
                        >￥{{ scope.row.total_price.toFixed(2) }}</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    :label="gL('unitPrice')"
                    width="130px"
                  >
                    <template slot-scope="scope">
                      <span style="margin-left: 10px"
                        >￥{{ Number(scope.row.unit_price) }}</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column align="center" width="130px">
                    <template slot="header">
                      <div style="direction: ltr;">
                        <span>{{ gL("count") }} : </span>
                        <span>{{ row_data.foods_count }}</span>
                      </div>
                    </template>
                    <template slot-scope="scope">
                      <span style="margin-left: 10px"
                        >X{{ Number(scope.row.foods_count) }}</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    :align="gLang == 2 ? 'left' : 'right'"
                    :label="gL('food')"
                  >
                    <template slot-scope="scope">
                      <div class="food-name">
                        <span
                          style="margin-left: 10px;font-size:18px;color:#f40;background:#f1f2f3; padding:5px 10px;"
                          v-if="scope.row.state == 6 || scope.row.state == 4"
                          >{{ gL("refunded") }}</span
                        >
                        <span style="margin-left: 10px">{{
                          scope.row.food_name
                        }}</span>
                      </div>
                      <div
                        class="food-remarks"
                        v-if="scope.row.state == 6 || scope.row.state == 4"
                      >
                        <span>{{ gL("refundRemarks") }} : </span>
                        <span>{{ scope.row.remarks }}</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </div>
          </div>
          <div class="prices">
            <div class="bill-payment" :style="{direction: gLang == 1 ? 'rtl' : 'ltr' }">
              <div class="bill-padding">
                <div class="bill-payment-detail">
                  <div class="amount-received">{{ gL("paymentMethod") }}</div>
                  <div
                    class="mix-pay-detail-line"
                    :style="
                      gLang == 1
                        ? 'flex-direction: row;'
                        : 'flex-direction: row-reverse;'
                    "
                  >
                    <div
                      v-for="(payItem, i) in row_data.payments"
                      :key="payItem.id"
                      class="pay-detail-item"
                      :style="{direction: gLang == 1 ? 'rtl' : 'ltr'}"
                    >
                      <span>{{ payItem.name }} : </span>
                      <span style="display: inline-block;"
                        >￥{{ payItem.amount }}</span
                      >
                    </div>
                  </div>
                </div>
                <div class="pay-detail" v-if="row_data.payment_type_id == 3">
                  <div>
                    <span>{{ gL("billGiveChangeDetail") }}: </span>
                    <span>￥{{ row_data.give_change }}</span>
                  </div>
                  <div>
                    <span>{{ gL("billCashDetail") }}: </span>
                    <span>￥{{ row_data.collected_amount }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="bill-statis">
              <div class="bill-padding">
                <div
                  class="row"
                  :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
                >
                  <span>￥{{ row_data.original_price }}</span>
                  <span>{{ gL("total_price") }}</span>
                </div>
                <div
                  class="row"
                  :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
                >
                  <span>￥{{ row_data.ignore_price }}</span>
                  <span>{{ gL("PreferentialAmount") }}</span>
                </div>
                <div
                  class="row"
                  :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
                >
                  <span>￥{{ row_data.total_refund_amount }}</span>
                  <span>{{ gL("drawback") }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="retire-mask" v-if="retire_box">
      <div class="retire-box">
        <div class="retire-title">
          <span
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            class="title-table"
            >{{ row_data.table_name }}</span
          >
          <span
            class="close iconfont icon-jia-copy"
            @click="cancelRetireBox"
          ></span>
        </div>
        <div class="retire-content">
          <div class="content-left">
            <span style="display: none">{{ temp }}</span>
            <div
              class="food-info"
              v-for="(item, index) in refundFoodsList"
              :key="item.id"
            >
              <div class="food-name" @click="showRemarkbox(item.id, index)">
                {{ item.food_name }}
                <span style="margin-left: 20px; color: #666"
                  >×{{ item.foods_count }}</span
                >
              </div>
              <div class="price-row">
                <div class="row-left" @click="showRemarkbox(item.id, index)">
                  <span>￥{{ item.unit_price }}</span>
                  <div class="remark-info">
                    {{ item.reasonInfo }}
                  </div>
                  <div
                    class="remark-info"
                    style="color:#666"
                    v-show="
                      item.reasonInfo == undefined || item.reasonInfo == ''
                    "
                  >
                    {{ gL("causeNotes") }}
                  </div>
                </div>
                <div class="row-right">
                  <span
                    class="minus iconfont icon-jian"
                    @click="foodMinus(item, index)"
                  ></span>
                  <span class="num">{{ item.refundFoodsCount }}</span>
                  <span
                    class="add iconfont icon-jia1"
                    @click="foodPlus(item, index)"
                  ></span>
                </div>
              </div>
            </div>
          </div>
          <div class="content-right">
            <div class="pay-list">
              <div
                class="pay-item"
                v-for="(item, index) in pay_list"
                :key="index"
                :class="item.id == pay_state ? 'active' : ''"
                @click="payState(item)"
              >
                <img :src="item.icon" alt width="30px" />
                <span v-if="gLang === '2'">{{ item.name_zh }}</span>
                <span v-else>{{ item.name_ug }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="content-bottom">
          <div class="bottom-left">{{ gL("pleaseSettingCount") }}</div>
          <div class="bottom-right">{{ gL("pleaseChoise") }}</div>
        </div>
        <div class="box-bottom" @click="retireConfrim">
          {{ gL("confirm") }}
        </div>
      </div>
    </div> -->
    <!--退菜密码弹出框-->
    <div class="pass-mask" v-if="password_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">{{
            gL("passBack")
          }}</span>
          <span
            class="close iconfont icon-jia-copy"
            @click="cancelPasswordBox"
          ></span>
        </div>
        <div
          class="content-password"
          :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
        >
          <input
            type="password"
            :placeholder="gL('inputPassword')"
            class="pass-input"
            v-model="order_refund_password"
          />
        </div>
        <div class="btn" @click="passwordClick">{{ gL("confirm") }}</div>
      </div>
    </div>
    <!-- 备注 -->
    <div class="remark-mask" v-if="remark_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">{{
            gL("retire")
          }}</span>
          <span class="iconfont icon-jia-copy" @click="cancelRemarkBox"></span>
        </div>
        <div
          class="content-remark"
          :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
        >
          <div class="remark">{{ gL("reason") }}</div>
          <div class="remark-list">
            <div
              class="remark-item"
              v-for="(item, index) in remarks_list"
              :key="index"
              :identifier="'r' + item.id"
              @click="remarkItem(item.name, item.id)"
            >
              {{ item.name }}
            </div>
          </div>
          <div class="input">
            <textarea
              cols="30"
              :placeholder="gL('otherRemark')"
              rows="2"
              v-model="remark_input"
            ></textarea>
          </div>
        </div>
        <div class="btn" @click="remarkClick">{{ gL("confirm") }}</div>
      </div>
    </div>

    <!-- 退款方式列表弹出框 -->
    <PaymentDialog ref="paymentDialog" :show.sync="showRefundPaymentDialog" />

    <!-- 退款弹出框 -->
    <refundDialog
      ref="refundDialog"
      :show.sync="retire_box"
      @refundConfrim="refundConfrim"
      @weightFood="weightFoodHandler"
    />

    <!-- 称重美食 -->
     <kiloBox ref="mychild" :number_box="kilo_box" @cancel="cancel"></kiloBox>
  </div>
</template>

<script>
import moment from "moment";
import { debounce } from "./../utils/utils.js";
import PaymentDialog from "./components/PaymentDialog.vue";
import refundDialog from "./components/RefundDialog.vue";
import kiloBox from "./../components/kiloBox.vue";
import {
  postOrderRefundAPI,
  getRemarksAPI,
  getBillListAPI,
  getBillUserAPI,
  getBillDetailAPI,
  getReprintOrderAPI,
} from "./../api/index.js";
var self;
export default {
  components: {
    PaymentDialog,
    refundDialog,
    kiloBox
  },
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.serachText = ""; //搜索内容
    self.delete_text = false; //搜索框删除按钮
    // self.pay_id = "";
    self.currentPage = 1;
    self.user_id = "";
    self.dateList(0);
    self.activeDay = 1;
    // self.getData();
    // self.getPayTypeList();
    self.getUserList();
    // self.getPayList();
  },
  data() {
    return {
      gLang: 1,
      tableData: [],
      pickerBeginDateBefore: {
        disabledDate(time) {
          return (
            time.getTime() >=
            moment()
              .hour(23)
              .minute(59)
              .second(59)
              .millisecond(999)
              .valueOf()
          );
        }
      },
      modal_title: this.gL("tableName"),
      // payList: [],
      // pay_id: "",
      userList: [],
      user_id: "",
      picker: [],
      row_data: [],
      modal_box: false,
      serachText: "", //搜索内容
      delete_text: false, //搜索框删除按钮
      day_list: [
        { name: this.gL("today"), id: 1, day: 0 },
        { name: this.gL("sevenDay"), id: 2, day: -7 },
        { name: this.gL("fifteen"), id: 3, day: -15 },
        { name: this.gL("thirty"), id: 4, day: -30 }
      ],
      activeDay: 1,
      amount: 0,
      sales: 0,
      order_id: "",
      loading: false,
      print_time: true,
      loading: true,
      totalCount: 0,
      pageSize: 100,
      currentPage: 1,
      retire_box: false,
      // pay_list: [],
      // pay_state: 0,
      itemPay: "",
      remark_box: false, //退菜备注
      remark_title: "", //退菜备注框标题
      remark_input: "", //退菜备注内容
      remarks_list: "", //退菜备列表
      remarks_count: "", //退菜
      state: 1,
      reasonInfo: "",
      index: 0,
      refundFoodsCount: 0,
      temp: true,
      foodsCount: 0,
      password_box: false,
      order_refund_password: "",
      current_bill_available_count: 0,
      refundFoodsList: [],
      showRefundPaymentDialog: false,
      refundData: {},
      storeInfo: {},
      kilo_box: false,
    };
  },
  methods: {
    // 显示称重美食弹出框
    weightFoodHandler(e) {
      this.kilo_box = true;
      self.$refs.mychild.openBox(e);
      // self.priceCount();
    },
    cancel(e, s, item) {
      if (item.foods_count < s) {
        self.$message({
          message: self.gL("yitixmaydu"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      }
      self.kilo_box = false;
      if (e == 4) {
        this.$refs.refundDialog.foodChange(s, item.id)
      }
    },

    // 确认退款
    refundConfrim(data) {
    this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      if (!this.storeInfo.operation_password) {
        this.$bus.$emit("changeCancelPassword");
        return;
      }
      this.password_box = true;
      this.refundData = data;
    },
    // 显示退菜退款类型弹出框
    showRefundPaymentDetail(row) {
      this.$refs.paymentDialog.showDialog(row);
    },

    cancelModalBox() {
      self.modal_box = false;
      self.row_data.bill_details = [];
    },
    cancelPasswordBox() {
      self.password_box = false;
      self.order_refund_password = "";
    },
    passwordClick() {
      self.requestConfrim();
      self.password_box = false;
      self.order_refund_password = "";
    },
    retireConfrim() {
      let cancelPassword = this.store.operation_password;
      let count = 0;
      let remarkChk = true;
      let billDetails = JSON.parse(JSON.stringify(self.refundFoodsList));
      console.log(billDetails);
      for (let i = 0; i < billDetails.length; i++) {
        const element = billDetails[i];
        console.log(element);
        count += element.refundFoodsCount;
        if (
          (element.reasonInfo == "" || element.reasonInfo == undefined) &&
          element.refundFoodsCount > 0
        ) {
          remarkChk = false;
          break;
        }
      }
      console.log(count);
      console.log(remarkChk);
      if (count === 0) {
        self.$message({
          message: self.gL("inputFoodsCount"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      }
      if (remarkChk == false) {
        self.$message({
          message: self.gL("inputRemarks"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      }
      if (cancelPassword == true) {
        self.password_box = true;
      } else {
        self.requestConfrim();
      }
    },
    requestConfrim() {
      postOrderRefundAPI(self.order_id, {
        ...this.refundData,
        password: this.order_refund_password
      })
        .then(response => {
          console.log(response);
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            self.getData();
            self.modal_box = false;
            self.remark_box = false;
            self.retire_box = false;
          }
        })
        .catch(err => {
          self.order_refund_password = "";
        });
    },
    foodMinus(e, v) {
      if (e.food_format_id == 2 && e.refundFoodsCount > 0) {
        e.refundFoodsCount = 0;
        self.temp = !self.temp;
        return;
      }
      if (e.refundFoodsCount > 0) {
        e.refundFoodsCount--;
      }
      self.temp = !self.temp;
      console.log(e.refundFoodsCount);
      console.log(e.foods_count);
    },
    foodPlus(e, v) {
      if (e.food_format_id == 2) {
        e.refundFoodsCount = e.foods_count;
        self.temp = !self.temp;
        return;
      }
      if (e.refundFoodsCount < e.foods_count) {
        e.refundFoodsCount++;
      }
      self.temp = !self.temp;
      console.log(e.refundFoodsCount);
      console.log(e.foods_count);
    },
    showRemarkbox(e, v) {
      self.index = v;
      self.remark_box = true;
      self.getRemarkList();
    },
    /**
     * 备注
     */
    remarkItem(e, v) {
      let remarks = document.querySelectorAll(".remark-item");
      remarks.forEach(element => {
        element.classList.remove("changeColor");
      });
      let remark = document.querySelector(
        ".remark-item[identifier=r" + v + "]"
      );
      remark.classList.add("changeColor");
      if (self.gLang == 1) {
        if (self.remark_input != "") {
          self.remark_input += "،" + e;
        } else {
          self.remark_input += e;
        }
      } else {
        if (self.remark_input != "") {
          self.remark_input += "，" + e;
        } else {
          self.remark_input += e;
        }
      }
    },
    /**
     * 备注完成
     */
    remarkClick() {
      console.log(self.remark_input);
      if (self.remark_input.length != "") {
        self.refundFoodsList[self.index].reasonInfo = self.remark_input;
        self.remark_box = false;
        self.remark_input = "";
      } else {
        self.$message({
          message: self.gL("chooiseRetire"),
          type: "error",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    /**
     * 备注列表
     */
    getRemarkList(fn) {
      getRemarksAPI({
        tag: "orderCancel"
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          fn(response.data.data);
          // self.remarks_list = response.data.data;
        }
      });
    },
    /**
     * 备注关闭
     */
    cancelRemarkBox() {
      self.remark_box = false;
      self.remark_input = "";
      self.state = 1;
    },
    /**
     * 支付类型
     */
    // getPayList() {
    //   getPaymentTypesPayableListAPI().then(response => {
    //     if (response.status >= 200 && response.status < 300) {
    //       console.log(response.data.data);
    //       self.pay_list = response.data.data;
    //       self.pay_state = response.data.data[0].id;
    //       self.itemPay = response.data.data[0];
    //     }
    //   });
    // },
    /**
     * 点击支付类型
     */
    // payState(e) {
    //   console.log(e);
    //   self.pay_state = e.id;
    //   self.itemPay = e;
    // },
    showRetireBox() {
      self.modal_box = false;
      const loading = this.$loading();
      self.getRemarkList(res => {
        loading.close();
        this.$refs.refundDialog.showDialog(this.row_data, res);
      });
    },
    cancelRetireBox() {
      self.retire_box = false;
      self.modal_box = true;
    },
    cell({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        if (self.gLang == 1) {
          return "direction: rtl";
        }
      }
    },
    // 分页
    handleSizeChange(val) {
      self.CurrentChange = val;
      self.getData();
    },
    handleCurrentChange(val) {
      self.currentPage = val;
      self.getData();
    },
    //搜索输入框输入时候
    inputSearch() {
      if (self.serachText.length != 0) {
        self.delete_text = true;
      } else {
        self.delete_text = false;
      }
      self.currentPage = 1;
      debounce(this.getData, 1000);
    },
    //删除搜索内容
    removeSerach() {
      self.serachText = "";
      self.delete_text = false;
      self.getData();
    },
    //获取今天，最近3天，最近7天
    dateList(day) {
      this.picker = [
        moment()
          .add(day, "days")
          .format("YYYY-MM-DD") + " 00:00:00",
        moment().format("YYYY-MM-DD HH:mm:ss")
      ];
      setTimeout(() => {
        self.getData();
      }, 300);
    },
    //获取数据
    getData() {
      self.loading = true;
      var data = {
        begin_at: self.picker[0],
        end_at: self.picker[1]
      };
      if (self.user_id != "") {
        data.cashier_id = self.user_id;
      }
      // if (self.pay_id != "") {
      //   data.payment_type_id = self.pay_id;
      // }
      if (self.serachText != "") {
        data.table_name = self.serachText;
      }
      data.limit = this.pageSize;
      data.page = self.currentPage;
      getBillListAPI(data)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.tableData = response.data.data;
            self.totalCount = response.data.meta.total;
            this.pageSize = response.data.meta.per_page;
            this.currentPage = response.data.meta.current_page;
            self.amount = response.data.top.real_amount;
            self.sales = response.data.top.total_amount;
          }
        })
        .finally(() => {
          self.loading = false;
        });
    },
    //获取数据
    // getPayTypeList() {
    //   getBillPaymentTypesListAPI().then(response => {
    //     if (response.status >= 200 && response.status < 300) {
    //       self.payList = response.data.data;
    //     }
    //   });
    // },
    //获取数据
    getUserList() {
      getBillUserAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.userList = response.data.data;
        }
      });
    },
    //点击天
    clickDay(e) {
      self.activeDay = e.id;
      self.dateList(e.day);
    },
    //切换列表
    changeSlecet() {
      self.getData();
    },
    //切换列表
    serach() {
      if (self.picker != null) {
        self.getData();
      } else {
        self.$message({
          message: self.gL("pleaceChooiseTime"),
          type: "error",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    //订单详情
    rowClick(row) {
      self.modal_box = true;
      self.order_id = row.order_id;
      getBillDetailAPI(row.order_id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.row_data = response.data.data;
          self.row_data.bill_details.forEach(element => {
            element.refundFoodsCount = 0;
          });

          let current_bill_available_count = 0;
          self.row_data.bill_details.forEach(element => {
            current_bill_available_count += element.foods_count;
          });
          self.current_bill_available_count = current_bill_available_count;

          let refundFoodsList = [];
          self.row_data.bill_details.forEach(element => {
            if (element.foods_count > 0) {
              refundFoodsList.push({
                id: element.id,
                order_id: element.order_id,
                food_id: element.food_id,
                food_name: element.food_name,
                foods_count: element.foods_count,
                food_format_id: element.food_format_id,
                total_price: element.total_price,
                unit_price: element.unit_price,
                refundFoodsCount: element.refundFoodsCount
              });
              self.refundFoodsList = refundFoodsList;
            }
          });
          console.log(self.refundFoodsList);
        }
      });
    },
    makeUpBill() {
      if (self.print_time) {
        self.print_time = false;
        setTimeout(() => {
          self.print_time = true;
        }, 30000);
      }
    },
    invoice() {
      let disablePrint = localStorage.getItem("disablePrint") == "true";
      if (disablePrint) {
        this.$message({
          message: this.gL("disablePrinter"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      getReprintOrderAPI(this.order_id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.changeColor {
  border: 1px solid #139d59;
  color: #139d59;
}

.ltr {
  direction: ltr;
}

.paging {
  padding-top: 10px;
  position: fixed;
  bottom: 10px;
  right: 0;
}
.wraps {
  width: 100%;
  height: 100%;
  z-index: 1;
  .top {
    display: flex;
    padding: 10px 20px;
    background-color: #2e3033;
    color: #ffffff;
    align-items: center;
    border-bottom: 1px solid #666666;
    justify-content: space-between;
    font-size: 22px;
    .search {
      display: flex;
      .btn-search {
        padding: 0 20px;
        background: #139d59;
        display: flex;
        align-items: center;
        margin-left: 15px;
        font-size: 22px;
        cursor: pointer;
        border-radius: 4px;
      }
    }
    .prices {
      display: flex;
      width: 46%;
      justify-content: space-between;
      .num {
        display: inline-block;
        color: #ff9c00;
      }
    }
    .serach {
      width: 280px;
      background: #4d4d4d;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 4px;
      .input {
        //  width: 70%;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        width: 70%;
        background: #4d4d4d;
        &::placeholder {
          color: #cccccc;
        }
      }
      .searchs {
        color: #cccccc;
        font-size: 24px;
        padding: 0 10px;
        vertical-align: -2px;
      }
      .del {
        width: 20%;
        height: 100%;
        color: #cccccc;
        justify-content: center;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 22px;
        }
      }
    }
    .select {
      margin-left: 15px;
      width: 200px;
    }
    .btn {
      background-color: #ff9c00;
      width: 150px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25px;
      height: 70%;
      font-size: 26px;
      cursor: pointer;
      .iconfont {
        font-size: 19px;
      }
    }
    .day-box {
      display: flex;
      justify-content: space-between;
      width: 470px;
      .day-item {
        padding: 5px 25px;
        background: #4d4d4d;
        border: 3px solid #4d4d4d;
        cursor: pointer;
      }
      .active {
        border-color: #139d59;
      }
    }
  }
  .table {
    height: 80%;
    overflow-y: scroll;
  }
  //s提示框
  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .box {
      background-color: #ffffff;
      width: 80vw;
      min-width: 876px;
      font-size: 30px;
      padding-inline: 32px;
      .title {
        color: #1a1a1a;
        padding: 32px 32px 15px;
        margin-bottom: 26px;
        position: relative;
        text-align: center;
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #666666;
        .title-table {
          display: inline-block;
          flex: 2;
        }
        .title-left {
          flex: 1;
          text-align: start;
        }
        .close {
          flex: 1;
          text-align: right;
          font-size: 26px;
          color: #666666;
          cursor: pointer;
        }
        .print-icon {
          float: left;
          font-size: 40px;
          margin-right: 40px;
        }
        .btn-refund {
          font-size: 22px;
          padding: 6px 20px;
          background: #9d1313;
          color: #fff;
          border-radius: 4px;
          cursor: pointer;
        }
      }
      .content {
        // padding: 0 32px;
        font-size: 18px;
        .info {
          border-bottom: 1px solid #666666;
          display: flex;
          font-size: 22px;
          .info-item {
            display: flex;
            flex: 1;
            text-align: start;
            .info-column {
              .column-item {
                padding-bottom: 18px;
              }
            }
            .no-flex {
              flex: none;
            }
          }
        }
        @media screen and (max-width: 1666px) {
          .info {
            font-size: 20px;
          }
        }
        @media screen and (max-width: 1450px) {
          .info {
            font-size: 18px;
          }
        }
        .detail-box {
          display: flex;
          .detail-food {
            flex: 6;
            border-inline-start: 1px solid #666;
            .food-remarks {
              font-size: 16px;
              color: #666;
              padding-top: 10px;
            }
          }
          .detail-refund {
            flex: 4;
            .updated-at {
              font-size: 18px;
            }
            .detail-btn {
              font-size: 22px;
            }
            .food-price {
              font-size: 20px;
              margin-top: 10px;
              direction: rtl;
            }
          }
        }
        .prices {
          display: flex;
          .bill-statis {
            flex: 6;
            padding: 20px 0px;
            border-top: 1px solid #666;
            border-inline-start: 1px solid #666;
          }
          .bill-payment {
            flex: 4;
            padding: 20px 0px;
            border-top: 1px solid #666;
          }
          .bill-padding {
            padding-inline: 10px;
          }
          .bill-payment-detail {
            text-align: start;
          }
          .row {
            display: flex;
            justify-content: space-between;
            padding-bottom: 10px;
          }
        }
        .btns {
          display: flex;
          font-size: 20px;
          justify-content: space-between;
          .btn-item {
            background: #666666;
            width: 48%;
            padding: 15px 0;
            color: #ffffff;
            text-align: center;
            cursor: pointer;
          }
        }
        .detail {
          height: 400px;
          overflow-y: scroll;
        }
      }
    }
  }
  .remark-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .box {
      background-color: #ffffff;
      width: 600px;
      font-size: 30px;
      -webkit-animation: fadelogIn 0.4s;
      animation: fadelogIn 0.4s;
      .title {
        background-color: #e6e6e6;
        color: #1a1a1a;
        padding: 25px 20px;
        position: relative;
        text-align: center;
        .iconfont {
          position: absolute;
          right: 20px;
          font-size: 23px;
          color: @grayColor;
          cursor: pointer;
        }
      }
      .content-remark {
        padding: 30px;
        padding-bottom: 0;
        font-size: 26px;
        .remark {
          padding-bottom: 30px;
          color: @grayColor;
        }
        .remark-list {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          .remark-item {
            width: 48%;
            background-color: #f5f5f5;
            padding: 15px 0;
            text-align: center;
            margin-bottom: 20px;
            overflow: hidden;
            cursor: pointer;
          }
        }
        .count {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-bottom: 30px;
          .num {
            display: flex;
            align-items: center;
            .iconfont {
              font-size: 40px;
              color: @graphiteColor;
              cursor: pointer;
            }
            .green {
              color: @greenColor;
            }
            .number {
              width: 70px;
              text-align: center;
              border-bottom: 2px solid @textColor;
              margin: 0 15px;
              font-weight: bold;
            }
            .icon-edit {
              .iconfont {
                font-size: 26px;
                color: @greenColor;
              }
            }
          }
        }
      }
      .content {
        text-align: center;
        padding: 30px;
        .price-count {
          font-size: 30px;
          color: #ff9c00;
          font-weight: bold;
          padding-bottom: 20px;
        }
      }
      .content-input {
        padding: 40px 100px 20px 100px;
        .in {
          border: 1px solid #cccccc;
          text-align: center;
          padding: 10px 0;
          margin-bottom: 20px;
          position: relative;
          &:last-child {
            margin-bottom: 0;
          }
          input {
            width: 93%;
            outline: none;
            font-size: 22px;
          }
          .vip-list {
            position: absolute;
            background: #e6e6e6;
            width: 100%;
            z-index: 9;
            top: 52px;
            // padding-top:10px;
            div {
              padding: 15px 0;
              cursor: pointer;
              border-bottom: 1px solid #cccccc;
            }
          }
        }
      }
      .content-input-ug {
        div {
          input::-webkit-input-placeholder {
            text-align: right;
          }
        }
      }
      .input {
        border: 1px solid #e6e6e6;
        padding: 15px 20px 10px 20px;
        textarea {
          outline: none;
          color: #666666;
          width: 100%;
          resize: none;
          font-family: "Alp Ekran";
          border: none;
          font-size: 26px;
        }
      }
      .btn {
        margin: 20px 30px;
        background: @greenColor;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        padding: 15px 0;
        cursor: pointer;
      }
    }
  }
  .retire-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .retire-box {
      background-color: #ffffff;
      width: 876px;
      font-size: 30px;
      .retire-title {
        color: #1a1a1a;
        padding: 32px;
        position: relative;
        text-align: center;
        display: flex;
        .title-table {
          display: inline-block;
          flex: 1;
        }
        .close {
          text-align: right;
          font-size: 26px;
          color: #666666;
          cursor: pointer;
        }
      }
      .retire-content {
        font-size: 22px;
        display: flex;
        .content-left {
          flex: 1;
          display: flex;
          flex-direction: column;
          padding: 0 32px 32px;
          overflow-y: scroll;
          height: 346px;
          .food-info {
            padding: 16px 0;
            border-bottom: 1px dashed #ccc;
            .food-name {
              padding-bottom: 16px;
              font-weight: bold;
            }
            .price-row {
              display: flex;
              .row-left {
                flex: 1;
                padding: 0 8px;
                display: flex;
                .remark-info {
                  padding-left: 16px;
                  font-size: 18px;
                  color: #139d59;
                }
              }
              .row-right {
                .add {
                  font-size: 30px;
                  color: #139d59;
                }
                .num {
                  padding: 0 10px;
                  font-size: 25px;
                }
                .minus {
                  font-size: 30px;
                  color: #139d59;
                }
              }
            }
          }
        }
        .content-right {
          flex: 1;
          padding: 16px 32px 32px;
          background: #2e3033;
          .pay-list {
            display: flex;
            flex-wrap: wrap;
            .pay-item {
              text-align: center;
              font-size: 22px;
              padding: 25px 20px;
              margin-right: 19px;
              margin-bottom: 20px;
              cursor: pointer;
              display: flex;
              flex: 1;
              align-items: center;
              justify-content: center;
              background: #ffffff;
              img {
                margin-right: 15px;
              }
            }
            .active {
              background: @greenColor;
              color: #ffffff;
            }
          }
        }
      }
      .content-bottom {
        display: flex;
        font-size: 22px;
        padding: 16px 32px;
        color: #f40;
        .bottom-left {
          flex: 1;
        }
        .bottom-right {
          flex: 1;
        }
      }
      .box-bottom {
        padding: 20px;
        text-align: center;
        background: #139d59;
        width: 90%;
        margin: 0 auto 20px auto;
        color: white;
      }
    }
  }
}
.pass-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 678px;
    font-size: 30px;
    .title {
      color: #1a1a1a;
      padding: 32px;
      position: relative;
      text-align: center;
      .close {
        text-align: right;
        font-size: 26px;
        color: #666666;
        cursor: pointer;
        position: absolute;
        top: 32px;
        right: 32px;
      }
    }
    .content-password {
      padding: 30px;
      font-size: 26px;
      text-align: center;
      .pass-input {
        border: 1px solid #ccc;
        width: 500px;
        padding: 15px;
        outline: none;
      }
    }
    .btn {
      margin: 20px 30px;
      background: @greenColor;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
  }
}
@media screen and (max-width: 1366px) {
  .wraps {
    .top {
      .day-box {
        width: 380px;
        .day-item {
          padding: 5px 15px;
        }
      }
    }
    .mask {
      .box {
        .content {
          .detail {
            height: 350px;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1366px) {
  .wraps {
    .top {
      .day-box {
        width: 380px;
        .day-item {
          padding: 5px 15px;
        }
      }
    }
    .retire-mask {
      .retire-box {
        .retire-content {
          .detail {
            height: 350px;
          }
        }
      }
    }
  }
}

.mix-pay-detail-line {
  margin-top: 15px;
  display: flex;
  justify-content: start;
  column-gap: 20px;
  flex-wrap: wrap;
  .pyment-type-name {
    flex-grow: 0;
    flex-shrink: 0;
    padding: 0px 20px;
  }
}
.amount-received {
  flex-grow: 0;
  flex-shrink: 0;
  font-size: 22px;
}

.pay-detail {
  display: flex;
  justify-content: flex-start;
  font-size: 22px;
  margin-top: 5px;
  column-gap: 20px;
}
</style>
