<template>
  <div id="app">
    <div v-if="openLockScreen">
      <el-header :class="{ 'el-header-zh': gLang == 2 }">
        <div class="head-left">
          <div class="edition">
            <span
              class="fullScreen"
              id="fullScreen"
              @click="fullScreen"
              v-if="!isDesktopRuntime"
              >{{
                isFullScreen == false ? gL("fullScreen") : gL("exitFullScreen")
              }}</span
            >
            <span
              class="fullScreen"
              @click="minimizeWindow"
              v-if="isDesktopRuntime"
              >{{ gL("exitFullScreen") }}</span
            >
            <span class="edition-no">{{ version }}</span>
            <!-- <span
              :style="{ color: isConnectMqtt ? '#139d59' : '#f94545' }"
              @click="mqttReconnect()"
              class="edition-line iconfont icon-radio-button-line"
            ></span> -->
          </div>
          <div class="lock" @click="clickLockScreen">
            <span class="lock-icon iconfont icon-icon2"></span>
          </div>
          <div class="title-box">
            <!-- <span class='iconfont icon-icon2 lock'></span> -->
            <div class="name" @click="clickOperation">
              <label for="icon"
                ><span>{{ merchantInfo.userName }}</span></label
              >
              <span id="icon" class="iconfont icon-icon-dwon"></span>
            </div>
          </div>
        </div>
        <div class="time">
          <span>{{ year }}</span>
          <span class="day">{{ week }}</span>
          <span>{{ hour }}</span>
        </div>
        <!-- <p class="name">{{$merchant_name}}</p> -->
        <p class="name">
          {{
            gLang == 1
              ? merchantInfo.merchant_name_ug
              : merchantInfo.merchant_name_zh
          }}
        </p>
        <div class="operation" v-if="operation">
          <div
            class="operation-item"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            @click="changePswdBox"
          >
            <span class="iconfont icon-xiugaimima"></span>
            <span>{{ gL("changePass") }}</span>
          </div>
          <div
            class="operation-item"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            @click="changeUser"
          >
            <span class="iconfont icon-qiehuanzhanghao"></span>
            <span>{{ gL("exit") }}</span>
          </div>
          <div
            class="operation-item"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            @click="changeLanguage"
          >
            <el-image class="lang-image" :src="langImage"></el-image>
            <span>{{ gL("switchLanguage") }}</span>
          </div>
          <div
            class="operation-item"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
            @click="exitSystem"
            v-if="isElectron"
          >
            <span class="iconfont icon-tuichu1"></span>
            <span>{{ gL("exit") }}</span>
          </div>
        </div>

        <!-- 模态框 -->
        <!--切换用户提示框-->
        <div class="mask" v-if="switchingUserModal">
          <div class="box" id="box">
            <div class="title">
              <span>{{ gL("exit") }}</span>
              <span
                class="iconfont icon-jia-copy"
                @click="cancelswitchingUserModal"
              ></span>
            </div>
            <div class="content">
              <span>{{ gL("exitModal") }}</span>
            </div>
            <div class="btn" @click="logout">
              {{ gL("confirm") }}
            </div>
          </div>
        </div>
        <!--退出提示框-->
        <div class="mask" v-if="exitModal">
          <div class="box" id="box">
            <div class="title">
              <span>{{ gL("exit") }}</span>
              <span
                class="iconfont icon-jia-copy"
                @click="cancelExitModal"
              ></span>
            </div>
            <div class="content">
              <span>{{ gL("exitModal") }}</span>
            </div>
            <div class="btn" @click="closeWindow">
              {{ gL("confirm") }}
            </div>
          </div>
        </div>
        <!-- <div  v-if="number_box"> -->
        <!-- <moal :number_box="modal_box"
                 @cancel="cancel" @confirm="confirm"></moal> -->
      </el-header>
      <!-- <startJob :number_box="job_box"  @startBox="startBox"></startJob> -->
      <el-container>
        <div class="aside" @click="cancel">
          <router-link
            class="submenu"
            v-for="(item, index) in menus"
            :key="index"
            v-if="item.isCloud ? merchantInfo.mode == 2 : true"
            :to="item.path"
            :style="{ fontSize: item.fontSize }"
          >
            <!-- <el-image v-if="item.iconType == 'image'" :src="item.icon" /> -->
            <!-- <img v-if="item.iconType == 'image'" :src="item.icon" /> -->
            <div v-if="item.iconType == 'svg'" class="svg-box" v-html="item.icon"></div>
            <span v-else class="iconfont" :class="item.icon"></span>
            <span>{{ item.name }}</span>
          </router-link>
          <div style="flex-grow: 1" />
          <!-- <button
            v-if="isDesktopRuntime"
            class="submenu"
            style="background-color: red; color: white"
            @click="closeWindow"
          >
            {{ gL("exit") }}
          </button> -->
        </div>
        <el-container>
          <el-main>
            <div class="wr" @click="cancel">
              <keep-alive>
                <router-view></router-view>
              </keep-alive>
            </div>
          </el-main>
        </el-container>
      </el-container>
    </div>
    <lock-screen
      v-if="lockScreen"
      :lockScreen="lockScreen"
      :openLockScreen="openLockScreen"
      @closeLockScreen="closeLockScreen"
      @back="backLogin"
    ></lock-screen>

    <!--修改密码提示框-->
    <div class="mask" v-if="changePassBox">
      <div class="box" id="box">
        <div class="title">
          <span>{{
            passwordRadio == "login"
              ? gL("loginPasswordRadio")
              : gL("refundPasswordRadio")
          }}</span>
          <span
            class="iconfont icon-jia-copy"
            @click="cancelChangePswdBox"
          ></span>
        </div>
        <div class="radio-box">
          <el-radio-group v-model="passwordRadio">
            <el-radio-button label="refund">{{
              gL("refundPasswordRadio")
            }}</el-radio-button>
            <el-radio-button label="login">{{
              gL("loginPasswordRadio")
            }}</el-radio-button>
          </el-radio-group>
        </div>
        <div class="content-input" :class="{ 'content-input-ug': gLang == 1 }">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
            <el-form-item prop="code">
              <div class="input-item ">
                <el-input
                  v-model="ruleForm.code"
                  prefix-icon=""
                  type="number"
                  :placeholder="gL('codePlaceholder')"
                ></el-input>
                <el-button
                  class="send-btn"
                  type="primary"
                  @click="sendCodeHandler"
                  >{{
                    codeTimeout == 0 ? gL("sendBtn") : codeTimeout + " s"
                  }}</el-button
                >
              </div>
            </el-form-item>
            <el-form-item prop="password">
              <div class="input-item">
                <el-input
                  v-model="ruleForm.password"
                  show-password
                  :placeholder="gL('newPasswordPlaceholder')"
                ></el-input>
              </div>
            </el-form-item>
            <el-form-item prop="confirm_passwords">
              <div class="input-item">
                <el-input
                  v-model="ruleForm.confirm_passwords"
                  show-password
                  :placeholder="gL('newPasswordPlaceholder')"
                ></el-input>
              </div>
            </el-form-item>
          </el-form>

          <!-- <div class="old">
                <input
                  type="password"
                  maxlength="16"
                  minlength="6"
                  v-model="ruleForm.old_pass"
                  :placeholder="gL('oldPass')"
                />
              </div>
              <div class="new">
                <input
                  type="password"
                  maxlength="16"
                  v-model="ruleForm.new_pass"
                  :placeholder="gL('newPass')"
                />
              </div>
              <div class="repas">
                <input
                  type="password"
                  maxlength="16"
                  v-model="ruleForm.re_pass"
                  :placeholder="gL('newPass')"
                  @keyup.enter="changePass"
                />
              </div> -->
        </div>
        <div class="btn" @click="changePass">
          {{ gL("confirm") }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.svg-box {
  padding: 0 6px 0 20px;
  display: inline-block;
  vertical-align: middle;
}
.lang-image {
  width: 26px;
  height: 26px;
  margin-left: 10px;
}
.el-header {
  background-color: #ffffff;
  color: #1a1a1a;
  // line-height: 60px;
  position: fixed;
  top: 0;
  right: 0;
  left: 180px;
  z-index: 100;
  // overflow: hidden;
  display: flex;
  display: -webkit-flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  min-width: 1186px;
  font-size: 26px;
  .head-left {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
  }
  .title-box {
    display: flex;
    display: -webkit-flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    .lock {
      font-size: 30px;
      color: #666666;
      padding-right: 50px;
    }
    .name {
      cursor: pointer;
      label {
        cursor: pointer;
      }
      .iconfont {
        color: #666666;
        padding-left: 10px;
        vertical-align: 2px;
      }
    }
  }
  .edition {
    font-size: 22px;
    margin-right: 40px;
    display: flex;
    align-items: center;
    .fullScreen {
      cursor: pointer;
      padding: 12px 24px;
      background: #eee;
      border-radius: 32px;
      box-shadow: 0px 0px 5px #999;
    }
    .edition-no {
      padding: 0 15px;
      font-family: sans-serif;
    }
    .edition-line {
      font-size: 32px;
    }
  }
  .lock {
    line-height: 60px;
    margin-right: 40px;
    .lock-icon {
      font-size: 24px;
    }
  }
  .time {
    font-size: 22px;
    line-height: 60px;
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    .day {
      padding: 0 15px;
    }
  }
  .name {
    line-height: 60px;
    padding-right: 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    direction: rtl;
  }
}
.aside {
  background-color: #2e3033;
  color: #ffffff;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  .submenu {
    font-size: 24px;
    padding: 25px 0;
    width: 175px;
    border-bottom: 1px solid #666666;
    cursor: pointer;
    display: block;
    color: #ffffff;
    .iconfont {
      font-size: 26px;
      padding: 0 6px 0 20px;
    }
    // &:nth-child(2) {
    //   font-size: 22px;
    // }
    // &:nth-child(3) {
    //   font-size: 18px;
    //   .iconfont {
    //     padding: 0 5px 0 20px;
    //   }
    // }
    // &:nth-child(4) {
    //   font-size: 18px;
    //   .iconfont {
    //     padding: 0 5px 0 20px;
    //   }
    // }
    // &:nth-child(5) {
    //   font-size: 18px;
    //   .iconfont {
    //     padding: 0 5px 0 20px;
    //   }
    // }
  }
  .router-link-exact-active, .active-link {
    background: #139d59;
  }
}
.aside-ug {
  left: initial;
  right: 0;
  direction: rtl;
}
.el-main {
  color: #333;
  background-color: #fff;
  min-height: 768px;
  padding-left: 200px;
  padding-right: 0;
  padding-bottom: 30px;
  padding-top: 60px;
  min-width: 1366px;
  position: absolute;
  bottom: 0;
  top: 0;
  right: 0;
  left: 0;
  overflow: hidden;
  .wr {
    height: 100%;
    width: 100%;
  }
}
.operation {
  position: absolute;
  top: 60px;
  z-index: 999;
  background: #2e3033;
  color: #ffffff;
  right: 0;
  font-size: 22px;
  border: 1px solid #666666;
  border-bottom: none;
  .operation-item {
    align-items: center;
    padding: 20px;
    display: flex;
    border-bottom: 1px solid #666666;
    cursor: pointer;
    .iconfont {
      font-size: 30px;
      padding-left: 10px;
    }
  }
}
//数字键盘提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 768px;
    font-size: 30px;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .title {
      background-color: #e6e6e6;
      text-align: center;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 26px;
        color: #666666;
        cursor: pointer;
      }
    }
    .content {
      text-align: center;
      padding: 50px 30px 50px 30px;
    }
    .content-input {
      padding: 40px 100px 20px 100px;
      .input-item {
        margin-bottom: 10px;
        display: flex;
        .send-btn {
          margin-inline-start: 20px;
          width: 200px;
        }
      }
      // div {
      //   border: 1px solid #cccccc;
      //   text-align: center;
      //   padding: 10px 0;
      //   margin-bottom: 20px;
      //   &:last-child {
      //     margin-bottom: 0;
      //   }
      //   input {
      //     width: 93%;
      //     outline: none;
      //     font-size: 22px;
      //   }
      // }
    }
    .content-input-ug {
      div {
        input::-webkit-input-placeholder {
          text-align: right;
        }
      }
    }
    .btn {
      margin: 20px 100px;
      background: #139d59;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
  }
}
/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

// @-webkit-keyframes fadelogIn {
//   0% {
//     -webkit-transform: translate3d(0, 100%, 0);
//   }
//   100% {
//     -webkit-transform: none;
//   }
// }
/*弹层动画（从上往下）*/
@keyframes fadeInDown {
  0% {
    -webkit-transform: translate3d(0, -20%, 0);
    -webkit-transform: translate3d(0, -20%, 0);
    transform: translate3d(0, -20%, 0);
    transform: translate3d(0, -20%, 0);
    opacity: 0;
  }
  100% {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}

// @-webkit-keyframes fadeInDown {
//   0% {
//     -webkit-transform: translate3d(0, -20%, 0);
//     opacity: 0;
//   }
//   100% {
//     -webkit-transform: none;
//     opacity: 1;
//   }
// }
.slip-down {
  -webkit-animation: fadeInDown 0.5s;
  animation: fadeInDown 0.5s;
}
@media screen and (max-width: 1366px) {
  .el-main {
    div {
      // height: 99%;
    }
  }
}

.radio-box {
  margin-top: 10px;
  text-align: center;
}
</style>

<script>
import LockScreen from "./components/lockScreen.vue";
// import startJob from '../components/startJob.vue'
import langImage from "./assets/images/language.png";
import {
  deleteAuthorizationsAPI,
  postResetPasswordAPI,
  postResetPasswordSendCodeAPI,
  postOperationPasswordSendCodeAPI,
  postOperationPasswordAPI,
  getAuthInfoAPI
} from "./api/index.js";
import mqttService from "./mqtt/MqttService";
import event from "./mqtt/Event";
import slsLog from "./print/SlsLog";
let _ipcRenderer;
var self;
let sendCodeTimer = null;
export default {
  name: "app",
  created() {
    self = this;
    this.isDesktopRuntime = DesktopRuntime != null;
    this.$bus.$on("changeCancelPassword", this.changePswdBox);
  },
  async activated() {
    self.gLang = localStorage.getItem("langId");
    document.getElementsByTagName("html")[0].setAttribute("lang", self.gLang == 1 ? "ug" : "zh");
    this.merchantInfo = JSON.parse(localStorage.getItem("merchant_info"));
    self.gettime();
    if (localStorage.getItem("lockScreen") == "isLockScreen") {
      self.clickLockScreen();
    }
    const appsData = window.electronAPI && (await window.electronAPI.getApps());
    self.version = appsData ? "v" + appsData.version : "v1.1.2";

    window.electronAPI &&
      window.electronAPI.isConnectMqtt((event, value) => {
        this.isConnectMqtt = value;
      });
  },
  data() {
    const checkPassword = (rule, value, callback) => {
      if (this.ruleForm.password != this.ruleForm.confirm_passwords) {
        callback(new Error(this.gL("twoPassDifference")));
      } else {
        callback();
      }
    };
    return {
      langImage: langImage,
      isElectron: window.electronAPI && window.electronAPI.isElectron,
      isConnectMqtt: false,
      isDesktopRuntime: false,
      msg: "Welcome to Your Vue.js App",
      gLang: 1, //语言
      merchantInfo: {},
      active_menu: 1, //选择的菜单
      operation: false,
      version: "",
      menus: [
        //菜单
        {
          id: 1,
          name: this.gL("table"),
          icon: "icon-zhuozi",
          path: "/table",
          fontSize: "24px",
          isCloud: true,
        },
        {
          id: 3,
          name: this.gL("bill"),
          icon: "icon-RMB",
          path: "/bill",
          fontSize: "24px"
        },
        {
          id: 4,
          name: this.gL("guQing"),
          icon: "icon-dingcan",
          path: "/limit",
          fontSize: "24px"
        },
        {
          id: 5,
          name: this.gL("overWork"),
          icon: "icon-icon_jiaoban",
          path: "/overWork",
          fontSize: "24px",
          isCloud: true,
        },
        {
          id: 6,
          name: this.gL("member"),
          icon: "icon-huiyuan",
          path: "/member",
          fontSize: "24px"
        },
        {
          id: 13,
          name: this.gL("credit"),
          icon: `<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1747814233430" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5053" xmlns:xlink="http://www.w3.org/1999/xlink" width="26" height="26"><path d="M0 194.133863m36.693272 0l145.919757 0q36.693272 0 36.693272 36.693272l0-0.426666q0 36.693272-36.693272 36.693273l-145.919757 0q-36.693272 0-36.693272-36.693273l0 0.426666q0-36.693272 36.693272-36.693272Z" fill="#ffffff" p-id="5054"></path><path d="M0 474.880062m36.693272 0l145.919757 0q36.693272 0 36.693272 36.693272l0-0.426666q0 36.693272-36.693272 36.693272l-145.919757 0q-36.693272 0-36.693272-36.693272l0 0.426666q0-36.693272 36.693272-36.693272Z" fill="#ffffff" p-id="5055"></path><path d="M0 755.626261m36.693272 0l145.919757 0q36.693272 0 36.693272 36.693272l0-0.426666q0 36.693272-36.693272 36.693272l-145.919757 0q-36.693272 0-36.693272-36.693272l0 0.426666q0-36.693272 36.693272-36.693272Z" fill="#ffffff" p-id="5056"></path><path d="M926.29179 0.000853H194.986342a97.279838 97.279838 0 0 0-97.279838 97.706504v45.226591h85.333191a87.893187 87.893187 0 0 1 0 175.786374h-85.333191v104.959825h85.333191a87.893187 87.893187 0 0 1 0 175.786374h-85.333191v104.959825h85.333191a87.893187 87.893187 0 0 1 0 175.359708h-85.333191v46.506589A97.279838 97.279838 0 0 0 194.986342 1023.999147h731.305448A97.279838 97.279838 0 0 0 1023.998293 926.292643V97.707357A97.279838 97.279838 0 0 0 926.29179 0.000853z m-132.266447 551.679081a29.013285 29.013285 0 0 1 29.013285 29.439951 28.586619 28.586619 0 0 1-29.013285 29.013285h-116.906472V725.332978a28.586619 28.586619 0 0 1-29.013285 29.013285 29.013285 29.013285 0 0 1-29.43995-29.013285v-115.199808h-116.479806a29.013285 29.013285 0 0 1-29.439951-29.013285 29.439951 29.439951 0 0 1 29.439951-29.439951h116.479806V512h-116.479806a29.439951 29.439951 0 1 1 0-58.453236h116.479806v-14.506642l-122.879796-121.173132a29.013285 29.013285 0 0 1 42.666596-42.666595l110.933148 110.506482 110.506483-110.506482a29.013285 29.013285 0 0 1 42.666595 0 28.586619 28.586619 0 0 1 0 42.666595l-122.879795 122.879795v14.506643h116.906472a29.439951 29.439951 0 0 1 0 58.453236h-119.466468v37.97327z" fill="#ffffff" p-id="5057"></path></svg>`,
          iconType: "svg",
          path: "/credit",
          fontSize: "24px"
        },
        {
          main_id: 7,
          name: this.gL("printer"),
          icon: "icon-dayinji-",
          path: "/printer",
          fontSize: "24px"
        },
        {
          id: 8,
          name: this.gL("setting"),
          icon: "icon-shezhi",
          path: "/setting",
          fontSize: "24px"
        },
        {
          id: 9,
          name: this.gL("statistics"),
          icon: "icon-tongji",
          path: "/statis",
          fontSize: "24px"
        },
        {
          id: 10,
          name: this.gL("foodManage"),
          icon: "icon-dingcan",
          path: "/foodManage",
          fontSize: "17px"
        },
        {
          id: 11,
          name: this.gL("tableManage"),
          icon: "icon-zhuozi",
          path: "/tableManage",
          fontSize: "17px"
        }
        // {
        //   id: 12,
        //   name: this.gL("storesPage"),
        //   icon: "icon-waibu",
        //   path: "/stores",
        //   fontSize: "17px"
        // }
      ],
      date: new Date(),
      year: "",
      hour: "",
      week: "",
      ruleForm: {
        code: "",
        password: "",
        confirm_passwords: ""
      },
      rules: {
        code: [
          {
            required: true,
            message: this.gL("codePlaceholder"),
            trigger: "blur"
          }
        ],
        password: [
          {
            required: true,
            message: this.gL("newPasswordPlaceholder"),
            trigger: "blur"
          }
        ],
        confirm_passwords: [
          {
            required: true,
            message: this.gL("newPasswordPlaceholder"),
            trigger: "blur"
          },
          {
            validator: checkPassword,
            trigger: "blur"
          }
        ]
      },
      changePassBox: false,
      modal_box: false,
      job_box: true,
      switchingUserModal: false, //切换用户提示框
      exitModal: false,
      lockScreen: false,
      openLockScreen: true,
      isFullScreen: false,
      eventMqttStatus: null,
      passwordRadio: "login",
      codeTimeout: 0,
      sendCode: ""
    };
  },
  watch: {
    isFullScreen(newValue, oldValue) {
      if (newValue == true) {
        if (document.documentElement.RequestFullScreen) {
          document.documentElement.RequestFullScreen();
        }
        //兼容火狐
        if (document.documentElement.mozRequestFullScreen) {
          document.documentElement.mozRequestFullScreen();
        }
        //兼容谷歌等可以webkitRequestFullScreen也可以webkitRequestFullscreen
        if (document.documentElement.webkitRequestFullScreen) {
          document.documentElement.webkitRequestFullScreen();
        }
        //兼容IE,只能写msRequestFullscreen
        if (document.documentElement.msRequestFullscreen) {
          document.documentElement.msRequestFullscreen();
        }
        return;
      }
      self.screenTitle = self.gL("exitFullScreen");
      if (document.exitFullScreen) {
        document.exitFullscreen();
      }
      //兼容火狐
      if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      }
      //兼容谷歌等
      if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      }
      //兼容IE
      if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
  },
  methods: {
    // 发送验证码
    sendCodeHandler() {
      // 重置密码
      if (this.codeTimeout != 0) return;
      if (this.passwordRadio == "login") {
        postResetPasswordSendCodeAPI({ phone: this.merchantInfo.user })
          .then(res => {
            this.startSendCodeTimer();
            this.sendCode = res.data.data.batchID;
          })
          .catch(err => {
            console.log("----------- err ---- ", err);
          });
      } else if (this.passwordRadio == "refund") {
        postOperationPasswordSendCodeAPI({ phone: this.merchantInfo.user })
          .then(res => {
            this.startSendCodeTimer();
            this.sendCode = res.data.data.batchID;
          })
          .catch(err => {
            console.log("----------- err ---- ", err);
          });
      }
    },

    // 更新用户数据
    updateAuthInfo() {
      getAuthInfoAPI()
        .then(res => {
          localStorage.setItem(
            "merchant_info",
            JSON.stringify({
              user: res.data.data.user.phone,
              userName: res.data.data.user.name,
              id: res.data.data.user.id,
              merchant_name_zh: res.data.data.merchant.name_zh,
              merchant_name_ug: res.data.data.merchant.name_ug,
              merchant_address_ug: res.data.data.merchant.address_ug,
              merchant_address_zh: res.data.data.merchant.address_zh,
              operation_password: res.data.data.user.operation_password,
              isHandOver: res.data.data.user.is_handover,
              merchant_no: res.data.data.merchant.no,
              merchant_phone: res.data.data.merchant.phone,
              mode: res.data.data.merchant.mode,
            })
          );
        })
        .catch(err => {
          console.log("----------- err ---- ", err);
        });
    },

    // 启动验证码定时器
    startSendCodeTimer() {
      this.codeTimeout = 60;
      clearInterval(sendCodeTimer);
      sendCodeTimer = setInterval(() => {
        if (this.codeTimeout == 0) {
          clearInterval(sendCodeTimer);
          return;
        }
        this.codeTimeout--;
      }, 1000);
    },

    mqttReconnect() {
      // slsLog.info("重连mqtt")
      // if (mqttService.status()){
      //   mqttService.close()
      // }else{
      //   mqttService.connect()
      // }
    },
    backLogin() {
      this.closeLockScreen();
      this.logout();
    },
    fullScreen() {
      this.isFullScreen = !this.isFullScreen;
    },
    minimizeWindow() {
      if (DesktopRuntime === undefined) return;
      DesktopRuntime.minimizeWindow();
    },
    //锁屏
    clickLockScreen() {
      self.lockScreen = true;
      self.openLockScreen = false;
      localStorage.setItem("lockScreen", "isLockScreen");
    },
    closeLockScreen() {
      self.lockScreen = false;
      self.openLockScreen = true;
      localStorage.removeItem("lockScreen");
    },
    // 切换语言
    changeLanguage() {
      let lang = 0;
      if (this.gLang == 1) {
        lang = 2;
      } else {
        lang = 1;
      }
      localStorage.setItem("langId", lang);
      window.location.reload();
      this.$switchLang();
    },
    //切换用户操作
    changeUser() {
      self.switchingUserModal = true;
      self.clickOperation();
    },
    cancelswitchingUserModal() {
      self.switchingUserModal = false;
      let down = document.querySelector("#box");
      down.classList.add("slip-down");
    },
    //退出操作
    exitSystem() {
      self.exitModal = true;
      self.clickOperation();
    },
    cancelExitModal() {
      self.exitModal = false;
    },
    async closeWindow() {
      if (this.isElectron) {
        deleteAuthorizationsAPI().then(res => {
          if (res.status == 204) {
            localStorage.removeItem("merchant_info");
            localStorage.removeItem("token");
            this.$router.replace("/login");
            window.electronAPI && window.electronAPI.quit();
          }
        });
      }
    },
    //点击菜单
    clickMenu(e) {
      self.active_menu = e.id;
      self.$router.push({ path: e.path });
      self.operation = false;
    },
    //点击操作
    clickOperation() {
      self.operation = !self.operation;
    },
    //点击操作
    tableHome() {
      self.operation = !self.operation;
    },
    //点击其他地方
    cancel() {
      self.operation = false;
    },
    //点击其他地方
    confirm() {
      self.operation = false;
    },
    //修改密码对话框
    changePswdBox() {
      self.changePassBox = true;
      self.clickOperation();
    },
    //关闭修改密码对话框
    cancelChangePswdBox() {
      self.ruleForm = {
        old_pass: "",
        new_pass: "",
        re_pass: ""
      };
      self.changePassBox = false;
    },
    //登出
    logout() {
      self.operation = false;
      self.switchingUserModal = false;
      self.exitModal = false;
      // self.$remove('authorizations/').then((response) => {
      //   if(response.status >= 200 && response.status < 300){
      self.$message({
        message: self.gL("successfulOperation"),
        type: "success",
        customClass: self.$toastClass(),
        offset: 120
      });
      localStorage.removeItem("token"); //删除密码到cookie
      localStorage.removeItem("merchant_info");
      localStorage.setItem("shouldRefresh", true);
      self.$router.push({ path: "/login" });
      // }
      // }).catch(err => {
      //   if(err.response.status>=400){
      //     if(err.response.status==401){
      //       self.$router.push({ path: "/login" });
      //       self.$message({
      //         message:err.response.data.message,
      //         type: "error",
      //         customClass:self.$toastClass()
      //       });
      //     }else{
      //       self.$message({
      //         message:err.response.data.message,
      //         type: "error",
      //         customClass:self.$toastClass()
      //       });
      //     }
      //   }
      // });
    },
    /**
     * 日期
     */
    gettime() {
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth() + 1; //js中是从0开始所以要加1
      var day = date.getDate();
      var hour = date.getHours();
      var minute = date.getMinutes();
      var week = date.getDay();
      var weekday = [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六"
      ];
      var weekdayUy = [
        "يەكشەنبە",
        "دۈشەنبە",
        "سەيشەنبە",
        "چارشەنبە",
        "پەيشەنبە",
        "جۈمە",
        "شەنبە"
      ];
      self.year =
        year + "-" + self.doHandleMonth(month) + "-" + self.doHandleMonth(day);
      self.hour = self.doHandleMonth(hour) + ":" + self.doHandleMonth(minute);
      if (self.gLang == 1) {
        self.week = weekdayUy[week];
      } else {
        self.week = weekday[week];
      }
    },
    doHandleMonth(month) {
      var m = month;
      if (month.toString().length == 1) {
        m = "0" + month;
      }
      return m;
    },
    /**
     * 修改密码
     */
    changePass() {
      this.$refs.ruleForm.validate(valid => {
        console.log("------- valid ------- ", valid, "------ ", this.sendCode);
        if (valid) {
          if (this.passwordRadio == "login") {
            postResetPasswordAPI({
              batch_id: this.sendCode,
              code: this.ruleForm.code,
              password: this.ruleForm.password,
              phone: this.merchantInfo.user
            }).then(response => {
              if (response.status >= 200 && response.status < 300) {
                self.$message({
                  message: self.gL("successfulOperation"),
                  type: "success",
                  customClass: self.$toastClass(),
                  offset: 120
                });
                self.ruleForm = {
                  code: "",
                  password: "",
                  confirm_passwords: ""
                };
                self.changePassBox = false;
                this.updateAuthInfo();
              }
            });
          } else if (this.passwordRadio == "refund") {
            postOperationPasswordAPI({
              batch_id: this.sendCode,
              code: this.ruleForm.code,
              password: this.ruleForm.password,
              confirm_password: this.ruleForm.confirm_passwords
            }).then(response => {
              if (response.status >= 200 && response.status < 300) {
                self.$message({
                  message: self.gL("successfulOperation"),
                  type: "success",
                  customClass: self.$toastClass(),
                  offset: 120
                });
                self.ruleForm = {
                  code: "",
                  password: "",
                  confirm_passwords: ""
                };
                self.changePassBox = false;
                this.updateAuthInfo();
              }
            });
          }
        }
      });
    }
  },
  mounted() {
    var _this = this; //声明一个变量指向vue实例this,保证作用域一致
    this.timer = setInterval(function() {
      _this.gettime(); //重新调用
    }, 1000);
    this.eventMqttStatus = event.subscribe("mqtt-status", res => {
      this.isConnectMqtt = res.value;
    });
  },
  destroyed() {
    if (this.eventMqttStatus) {
      this.eventMqttStatus.unSubscribe();
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer); //在vue实例销毁钱，清除我们的定时器
    }
  },
  components: {
    LockScreen
    // startJob
  }
};
</script>
<style>
.el-select {
  width: 100%;
}
html[lang="ug"] {
  .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
    content: "";
  }
  .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:after {
    content: "*";
    color: #f56c6c;
    margin-right: 4px;
  }
  [class*="el-col-"] {
    float: right;
  }
  .el-form-item__label {
    float: right;
  }
  .el-message-box {
    .el-message-box__content {
      .el-message-box__status {
        right: 24px;
      }
      .el-message-box__message {
        padding-left: 12px;
        padding-right: 36px;
        text-align: right;
      }
    }
  }
  .el-input--suffix .el-input__inner {
    padding-left: 30px;
    padding-right: 15px;
  }
  .el-input__suffix {
    left: 5px;
     right: auto;
  }
  .el-select-dropdown__item {
    text-align: right;
  }
  .el-input-group__append, .el-input-group__prepend {
    border-radius: 4px;
    border: 1px solid #cccccc;
  }
  .el-input-group__append {
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .el-input-group--append .el-input__inner {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
