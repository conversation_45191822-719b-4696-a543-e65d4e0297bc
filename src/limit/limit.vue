<template>
  <div class="wrapper">
    <div class="food-list">
      <!-- 购物车 -->
      <div class="lists">
        <div class="list-wrap">
          <div
            class="tit"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
          >
            <div
              class="table-customer"
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            >
              <span class="table-num">{{ gL("setLimit") }}:</span>
              <span class="customer">{{ remainingFoodsCount }}</span>
            </div>
            <div
              class="icon"
              v-if="remaining_foods_list"
              v-show="remaining_foods_list.length != 0"
              @click="remove(2)"
            >
              <span class="iconfont icon-qingkong"></span>
            </div>
          </div>
          <div
            class="time-name"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
          >
            <div class="name">{{ gL("food") }}</div>
            <div
              class="time-day"
              :style="
                gLang == 1
                  ? 'justify-content:flex-start;flex-direction:row-reverse'
                  : ''
              "
            >
              <span
                class="time"
                style="width: 40%; display: inline-block"
                :style="gLang == 1 ? 'text-align:right' : ''"
                >{{ gL("limit") }}</span
              >
              <span class="day">{{ gL("surplus") }}</span>
            </div>
          </div>
          <div class="food_lists">
            <div
              class="food-item"
              :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
              v-for="(item, index) in remaining_foods_list"
              :key="index"
            >
              <div
                class="food-name"
                :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
              >
                {{ item.name }}
              </div>
              <div
                class="counts"
                :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
              >
                <div
                  class="iconfont icon-qingkong"
                  @click="remove(1, item)"
                ></div>
                <div class="num">{{ item.remaining_count }}</div>
                <div class="num">{{ item.sell_clear_count }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="wave">
          <img src="../../static/images/wave.png" alt="" />
        </div>
      </div>
    </div>
    <div class="wrap">
      <!-- 菜单和搜索框 -->
      <div class="top">
        <div class="menu-wrap">
          <div class="menu">
            <el-tabs
              v-model="activeName"
              type="card"
              class="menu-item"
              @tab-click="clickMenu"
            >
              <el-tab-pane
                v-for="(item, index) in tables"
                :key="item.id"
                :label="gLang == 1 ? item.name_ug : item.name_zh"
                :name="item.item"
                :id="item.id"
              >
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <div class="serach">
          <div class="input">
            <span class="iconfont icon-search search"></span>
            <input
              type="text"
              :placeholder="gL('shortcut') + '/' + gL('names')"
              v-model="serachText"
              maxlength="8"
              v-on:input="inputSearch"
            />
          </div>
          <div class="del" v-if="delete_text" @click="removeSerach">
            <span class="iconfont icon-jia-copy"></span>
          </div>
        </div>
      </div>
      <!-- 美食 -->
      <div class="table">
        <div class="box">
          <div
            class="food-item"
            v-for="(item, index) in foodList"
            :key="item.id"
            @click="clickFood(item)"
            :class="[
              item.active ? 'active-foods' : '',
              (item.cell_clear_state == 1) & (item.remaining_count == 0)
                ? 'over'
                : ''
            ]"
          >
            <div class="img">
              <img :src="item.image + '?x-oss-process=style/w20'" alt="" />
            </div>
            <div
              class="name"
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            >
              {{ gLang == 1 ? item.name_ug : item.name_zh }}
            </div>
            <div
              class="bottom"
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            >
              <span class="price">￥{{ item.price }}</span>
              <div
                class="remain"
                v-show="item.cell_clear_state == 1"
                :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
              >
                <span>{{ gL("surplus") }}:</span>
                <span class="num" v-if="!item.count">{{
                  item.remaining_count
                }}</span>
                <span class="num" v-if="item.count">{{ fiexdNum(item) }}</span>
              </div>
            </div>
            <div class="maskk">
              <span>{{ gL("soldOut") }}</span>
            </div>
          </div>
        </div>
      </div>
      <setLimitBox
        ref="mychild"
        :number_box="number_box"
        :food_id="food_id"
        @cancel="cancel"
      ></setLimitBox>
      <modal
        :number_box="confirm_box"
        :modal_content="modal_content"
        :modal_title="modal_title"
        @cancel="cancels"
        @confirm="confirmBox"
      ></modal>
    </div>
  </div>
</template>

<script>
import {
  getFoodCategoriesAPI,
  getSellClearFoodsAPI,
  getSpecFoodsAPI,
  postSellClearAPI,
  postSellClearDeleteAPI
} from "./../api/index.js";
var self;
import setLimitBox from "../components/setLimitBox.vue";
import modal from "../components/modal.vue";
export default {
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    //获取区域
    self.active_menu = "";
    (self.activeName = "first"), (self.delete_text = false);
    (self.serachText = ""), self.getList();
    self.getCartList();
    self.getFoodList();
  },
  data() {
    return {
      gLang: 1,
      tables: [],
      delete_text: false, //删除搜索框内容按钮
      serachText: "", //搜索内容
      active_menu: "", //选中的区或楼
      activeName: "first",
      foodList: [],
      cart_list: [], //购物车列表
      order_id: "", //订单ID
      query_order_id: "", //url订单ID
      food_id: "", //美食ID
      number_box: false, //设置沽清模态框
      confirm_box: false, //设置沽清模态框
      modal_content: "", //取消沽清模态框内容
      modal_title: this.gL("tips"), //取消沽清模态框标题
      row: "", //取消沽清模态框标题,
      odd: "", //是否全部删除
      remainingFoodsCount: 0,
      remaining_foods_list: []
    };
  },
  methods: {
    fiexdNum(e) {
      var num = e.remaining_count - e.count;
      return num.toFixed(2);
    },
    //搜索输入框输入时候
    inputSearch() {
      self.getFoodList();
      if (self.serachText.length != 0) {
        self.delete_text = true;
      } else {
        self.delete_text = false;
      }
    },
    //删除搜索内容
    removeSerach() {
      self.serachText = "";
      self.delete_text = false;
      self.getFoodList();
    },
    //选择Top菜单
    clickMenu(tab, event) {
      self.active_menu = tab.$attrs.id;
      self.getFoodList();
    },
    /**
     * 获取区域
     */
    getList() {
      getFoodCategoriesAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          response.data.data.unshift({
            name_ug: self.gL("all"),
            name_zh: self.gL("all"),
            item: "first",
            id: 0
          });
          self.tables = response.data.data;
        }
      });
    },
    /**
     * 沽清列表
     */
    getCartList() {
      getSellClearFoodsAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          let cart_list = response.data.data;
          self.remainingFoodsCount = response.data.data.remainingFoodsCount;
          self.remaining_foods_list = [];
          let tmpArr = [];
          let remaining_count = 0;
          let sell_clear_count = 0;
          cart_list.remaining_foods_list.forEach(element => {
            tmpArr = element.remaining_count.split(".");
            if (parseInt(tmpArr[1]) != 0) {
              remaining_count = parseFloat(element.remaining_count).toFixed(2);
            } else {
              remaining_count = parseInt(element.remaining_count);
            }

            tmpArr = element.sell_clear_count.split(".");
            if (parseInt(tmpArr[1]) != 0) {
              sell_clear_count = parseFloat(element.sell_clear_count).toFixed(
                2
              );
            } else {
              sell_clear_count = parseInt(element.sell_clear_count);
            }

            self.remaining_foods_list.push({
              id: element.id,
              name: element.name,
              remaining_count: remaining_count,
              sell_clear_count: sell_clear_count
            });
          });
        }
      });
    },
    /**
     * 获取美食
     */
    getFoodList() {
      var data = {};
      if (self.active_menu != "") {
        data.category_id = self.active_menu;
      }
      if (self.serachText != "") {
        data.keyword = self.serachText;
      }
      getSpecFoodsAPI(data).then(response => {
        console.log(`response`, response);
        if (response.status >= 200 && response.status < 300) {
          response.data.data.forEach(item => {
            if (item.food_format_id == 1) {
              item.remaining_count = Number(item.remaining_count);
              item.sell_clear_count = Number(item.sell_clear_count);
            }
            self.$set(item, "active", false);
            self.$set(item, "count", 0);
            if (self.cart_list.length != 0) {
              self.cart_list.remaining_foods_list.forEach(items => {
                if (item.food_format_id == 1) {
                  items.remaining_count = Number(items.remaining_count);
                  items.sell_clear_count = Number(items.sell_clear_count);
                }
                if (items.id == item.id) {
                  item.active = true;
                }
              });
            }
          });
          self.foodList = response.data.data;
        }
      });
    },
    /**
     * 点击美食
     */
    clickFood(e) {
      console.log(e);
      var name = "";
      if (self.gLang == 1) {
        name = e.name_ug;
      } else {
        name = e.name_zh;
      }
      self.food_id = e.id;
      self.number_box = true;
      this.$refs.mychild.openBox(e.remaining_count, name, e);
    },
    /**
     * 删除全部设置沽清的美食
     */
    removeAll() {
      postSellClearAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
          self.getFoodList();
          self.getCartList();
          self.cancels();
        }
      });
    },
    /**
     * 删除设置沽清的美食
     */
    removeOdd() {
      postSellClearDeleteAPI({
        food_id: self.row.id
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
          self.getFoodList();
          self.getCartList();
          self.cancels();
        }
      });
    },
    /**
     * 删除点击的美食
     */
    remove(e, s) {
      self.odd = e;
      if (e == 1) {
        if (self.gLang == 1) {
          self.modal_content =
            "《" + s.name + "》" + self.gL("confirimFoodOdd");
        } else {
          self.modal_content =
            self.gL("confirimFoodOdd") +
            "《" +
            s.name +
            "》" +
            self.gL("confirs");
        }
        self.row = s;
      } else {
        self.modal_content = self.gL("confirimFood");
      }
      self.confirm_box = true;
    },
    /**
     * 关闭设置沽清模态框
     */
    cancel() {
      self.number_box = false;
      self.getCartList();
      self.getFoodList();
    },
    cancels() {
      self.confirm_box = false;
    },
    confirmBox() {
      if (self.odd == 1) {
        self.removeOdd();
      } else {
        self.removeAll();
      }
    }
  },
  components: {
    setLimitBox,
    modal
  }
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
}
.food-list {
  width: 30%;
  height: 100%;
  .lists {
    width: 100%;
    height: 96%;
    background-color: #f2f2f2;
    position: relative;
    .list-wrap {
      padding: 0 10px;
      height: 100%;
      .tit {
        display: flex;
        justify-content: space-between;
        padding: 12px 10px 20px 10px;
        align-items: center;
        .table-customer {
          color: @textColor;
          font-size: 26px;
        }
        .icon {
          .iconfont {
            font-size: 26px;
            color: @grayColor;
            cursor: pointer;
          }
        }
      }
      .time-name {
        display: flex;
        justify-content: space-between;
        font-size: 22px;
        color: @grayColor;
        border-bottom: 1px solid @graphiteColor;
        padding: 0 10px 5px 10px;
        .time-day {
          display: flex;
          width: 43%;
        }
      }
      .food_lists {
        height: 88%;
        overflow-y: scroll;
        padding: 0 10px;
        .food-item {
          padding-top: 15px;
          border-bottom: 1px dashed @graphiteColor;
          padding-bottom: 10px;
          display: flex;
          flex-direction: row-reverse;
          justify-content: space-between;
          cursor: default;
          align-items: center;
          font-size: 22px;
          .food-name {
            width: 60%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding-top: 2px;
          }
          .counts {
            display: flex;
            width: 40%;
            justify-content: space-between;
            .iconfont {
              font-size: 20px;
              cursor: pointer;
            }
          }
        }
      }
    }
    .wave {
      position: absolute;
      bottom: -20px;
      width: 100%;
      img {
        width: 100%;
      }
    }
  }
}
.wrap {
  height: 100%;
  width: 68%;
  //菜单和输入框
  .top {
    background-color: @bgColor;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    .menu-wrap {
      width: 74%;
      display: flex;
      .menu {
        width: 100%;
        display: flex;
        color: #ffffff;
        height: 100%;
        .menu-item {
          height: 100%;
          width: 100%;
          // padding: 0 10px;
          line-height: 40px;
          font-size: 22px;
          cursor: pointer;
        }
        .active {
          background-color: @greenColor;
        }
      }
      .btns {
        // width: 20%;
        // height: 100%;
      }
    }
    .serach {
      width: 23%;
      background: #ffffff;
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      .input {
        width: 100%;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #666666;
        width: 70%;
      }
      .search {
        color: #666666;
        font-size: 24px;
        padding-left: 10px;
        vertical-align: -2px;
      }
      .del {
        width: 20%;
        height: 100%;
        position: absolute;
        right: 0;
        background-color: #e6e6e6;
        text-align: center;
        line-height: 40px;
      }
    }
  }
  //餐桌
  .table {
    padding: 22px;
    padding-right: 0;
    background: #e5e5e5;
    height: 93%;
    width: 100%;
    position: relative;
    overflow-y: scroll;
    .box {
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      .food-item {
        width: 17%;
        min-width: 15%;
        margin-bottom: calc(12% / 4);
        position: relative;
        background-color: #ffffff;
        padding: 6px;
        cursor: pointer;
        .img {
          height: 112px;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name {
          font-size: 22px;
          margin: 5px 0;
          height: 54px;
          line-height: 1.3;
          overflow: hidden;
          text-overflow: ellipsis;
          padding: 0 5px;
        }
        .bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .price {
            color: #ff5151;
            font-size: 18px;
            padding: 0 5px;
            font-weight: bold;
          }
          .remain {
            .num {
              color: #ff5151;
            }
          }
        }
        .maskk {
          width: 100%;
          height: 58%;
          background-color: rgba(0, 0, 0, 0.7);
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 22px;
          visibility: hidden;
        }
      }
      .food-item:not(:nth-child(5n)) {
        margin-right: calc(13% / 4);
      }
      .over {
        background-color: #333333;
        color: #ffffff;
        .bottom {
          .price {
            color: #ffffff;
          }
          .remain {
            display: none;
          }
        }
        .maskk {
          visibility: visible;
        }
      }
      .active-foods {
        background-color: #333333;
        color: #ffffff;
        .bottom {
          .price {
            color: #ffffff;
          }
          .remain {
            color: #ffffff;
            .num {
              //   color:#ffffff;
            }
          }
        }
      }
    }
    .checkout {
      font-size: 22px;
      color: #1a1a1a;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
