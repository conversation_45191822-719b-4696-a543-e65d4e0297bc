<template>
  <div id="app">
    <!-- 自定义窗口 -->
    <!-- <div class="toolbar">
      <div class="tool-item" @click="closeWindow">
        <i class="icons icons-close"></i>
      </div>
      <div class="tool-item" @click="fullScreenWindow" v-if="!isFullScreen">
        <i class="icons icons-max-window-1"></i>
      </div>
      <div class="tool-item" @click="fullScreenWindow" v-else>
        <i class="icons icons-max-window-2"></i>
      </div>
      <div class="tool-item" @click="miniScreenWindow">
        <i class="icons icons-mini-window"></i>
      </div>
    </div> -->
    <keep-alive>
      <router-view></router-view>
    </keep-alive>
    <print-control-view ref="printControlView" />

    <el-dialog
      :title="updateTitle"
      :visible.sync="showUpdateToast"
      top="40vh"
      width="560px"
      :custom-class="gLang == 1 ? 'ug-dialog' : 'zh-dialog'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :destroy-on-close="true"
      :before-close="closeUpdateToastHandler"
    >
      <div
        class="progress-box"
        v-if="updateProgress > 0 && updateProgress < 1000"
      >
        <el-progress
          :stroke-width="26"
          :percentage="updateProgress"
          :text-inside="true"
          text-color="#fff"
          class="progress-item"
        ></el-progress>
        <div class="progress-delta">{{ updateDelta }}MB/S</div>
      </div>
      <div v-if="updateProgress == 0" class="toast-content">
        {{ gL("appUpdateToastContent") }}
      </div>
      <span v-if="updateProgress == 0" slot="footer" class="dialog-footer">
        <el-button @click="closeUpdateToastHandler">{{
          gL("cancel")
        }}</el-button>
        <el-button
          type="primary"
          @click="() => confrimUpdateHandler('download')"
          >{{ gL("appUpdateToastConfirm") }}</el-button
        >
      </span>
      <div v-if="updateProgress == 1000" class="toast-content">
        {{ gL("appUpdateToastSuccess") }}
      </div>
      <div v-if="updateProgress == 1000" class="toast-content">
        <el-button
          type="primary"
          @click="() => confrimUpdateHandler('install')"
          >{{ gL("appUpdateToastSuccessBtn") }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<style lang="less">
@import url("./assets/css/icons.css");
@import url("./assets/fonts/iconfont.css");
html {
  user-select: none;
}
img {
  -webkit-user-drag: none;
}

.el-image {
  img.el-image__inner {
    width: 100%;
    height: 100%;
  }
}

.toast-content {
  text-align: center;
  padding: 20px;
  font-size: 26px;
}

.progress-box {
  padding: 20px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .progress-item {
    width: 380px;
  }
  .progress-delta {
    padding: 0px 10px;
    color: #666;
    font-size: 16px;
  }
}

.ug-dialog {
  direction: rtl;
  .progress-box {
    flex-direction: row-reverse;
  }
  .progress-item {
    direction: ltr;
  }
  .el-button {
    font-size: 22px;
  }
}

.zh-dialog {
  direction: ltr;
  .el-button {
    font-size: 22px;
  }
}

// 自定义窗口
// #app {
//   padding-top: 40px;
// }

// .toolbar {
//   width: 100%;
//   height: 40px;
//   background-color: #333333;
//   position: absolute;
//   top: 0;
//   left: 0;
//   right: 0;
//   z-index: 999999;
//   display: flex;
//   align-items: center;
//   justify-content: flex-start;
//   flex-direction: row-reverse;
//   padding: 0px 20px;
//   box-sizing: border-box;
//   column-gap: 10px;
//   .tool-item {
//     width: 30px;
//     height: 30px;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     cursor: pointer;
//     -webkit-app-region: drag;
//     .icons {
//       font-size: 16px;
//       font-weight: bold;
//       color: #b2b2b2;
//     }
//   }

//   .tool-item:hover {
//     .icons {
//       color: #fff;
//     }
//   }
// }
</style>
<script>
import { v4 as uuidV4 } from "uuid";
import mqtt from "./mqtt/MqttService";
import event from "./mqtt/Event";
import PrintControlView from "./print/PrintControlView.vue";

export default {
  // 组件注册
  components: { PrintControlView },
  // 数据属性定义
  data() {
    return {
      showUpdateToast: false,
      updateProgress: 0,
      updateDelta: 0
    };
  },
  provide() {
    return {
      appData: this
    };
  },
  computed: {
    // 计算更新对话框标题
    updateTitle() {
      if (this.updateProgress == 0) {
        return this.gL("appUpdateToastTitle");
      } else if (this.updateProgress < 100) {
        return this.gL("appUpdateToastTitle2");
      } else if (this.updateProgress == 1000) {
        return "";
      }
    }
  },
  methods: {
    // 关闭窗口方法 - Electron窗口控制
    closeWindow() {
      app.quit();
    },

    // 连接MQTT消息服务
    connectMqtt() {
      return new Promise((resolve, reject) => {
        const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
        const token = localStorage.getItem("token");

        if ((storeInfo != "" || storeInfo != null) && token != null) {
          let uu = JSON.parse(localStorage.getItem("mqtt_uuid"));
          if (!uu || uu == null || uu.merchant_no != storeInfo.merchant_no) {
            uu = { uuid: uuidV4(), merchant_no: storeInfo.merchant_no };
            localStorage.setItem("mqtt_uuid", JSON.stringify(uu));
          }
          console.log("uuid", uu.uuid);
          const disablePrint = localStorage.getItem("disablePrint");

          this.gLang = localStorage.getItem("langId");
          const url = localStorage.getItem("ip_adr");

          window.electronAPI &&
            window.electronAPI.connectMqtt &&
            window.electronAPI.connectMqtt({
              uuid: uu.uuid,
              merchantNo: storeInfo.merchant_no,
              lang: this.gLang,
              disablePrint,
              url
            });
        }
      });
    },

    // 检查应用更新
    checkAppUpdate() {
      window.electronAPI &&
        window.electronAPI.checkUpdate &&
        window.electronAPI.checkUpdate();
    },

    // 处理更新取消操作
    closeUpdateToastHandler() {
      this.showUpdateToast = false;
    },

    // 处理更新确认操作
    confrimUpdateHandler(type) {
      if (type == "download") {
        this.updateProgress = 0.1;
      }
      window.electronAPI &&
        window.electronAPI.updateDownload &&
        window.electronAPI.updateDownload(type);
    },
    // 打印控制方法
    async print(message) {
      if (message.action == "已结账" && (message.order && message.order.printers && message.order.printers.statement_ticket == 0)) return;
      await this.$refs.printControlView.print(message);
    }
  },
  // 组件挂载生命周期钩子
  async mounted() {
    const appsData =
      window.electronAPI &&
      window.electronAPI.getApps &&
      (await window.electronAPI.getApps());
    if (appsData) {
      // mqtt.connect();
      // event.clear("mqtt-message");
      // event.subscribe("mqtt-message", message => {
      //   this.$refs.printControlView.print(message);
      // });
      window.electronAPI &&
        window.electronAPI.mqttMessage &&
        window.electronAPI.mqttMessage((event, message) => {
          this.print(message);
        });

      // 1.连接MQTT  2.检查更新
      setTimeout(() => {
        this.connectMqtt();
        this.checkAppUpdate();
      }, 2000);

      // 重连MQTT
      window.electronAPI &&
        window.electronAPI.reConnectMqtt &&
        window.electronAPI.reConnectMqtt((event, result) => {
          setTimeout(() => {
            this.connectMqtt();
          }, 2000);
        });

      // 监听更新有关的回调
      window.electronAPI &&
        window.electronAPI.updateToast &&
        window.electronAPI.updateToast((ecent, type, res) => {
          if (type == "download-progress") {
            this.updateProgress = Math.ceil(res.percent * 10) / 10;
            this.updateDelta = Math.floor((res.delta / 1024 / 1024) * 10) / 10;
          } else if (type == "update-available") {
            this.showUpdateToast = true;
          } else if (type == "downloaded") {
            this.updateProgress = 1000;
          }
        });

      // 打印回调
      window.electronAPI &&
        window.electronAPI.printMessage &&
        window.electronAPI.printMessage((event, result) => {
          console.log("printMessage -> ", result);
          if (result == "resolve") {
            this.$message({
              message: this.gL("successfulOperation"),
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
          } else if (result == "rePrinter") {
            this.$message({
              message: this.gL("rePrinterText"),
              type: "warning",
              customClass: this.$toastClass(),
              offset: 120
            });
          }
        });
    }
  }
};
</script>
