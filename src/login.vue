<template>
  <div id="app">
    <div class="wrap">
      <!-- 顶部 -->
      <div class="top">
        <div
          class="ip-btn"
          @click="ipBox"
          :class="gLang == 2 ? 'ip-btn-zh' : ''"
        >
          {{ gL("server") }}
        </div>
        <span class="title">{{ gL("systemName") }}</span>
        <div class="lng">
          <span
            class="zh"
            :class="gLang == 2 ? 'active' : ''"
            @click="changeLang()"
            >简体中文</span
          >
          <span class="lines"></span>
          <span
            class="uy"
            :class="gLang == 1 ? 'active' : ''"
            @click="changeLang()"
            >ئۇيغۇرچە</span
          >
        </div>
      </div>
      <!-- 输入框 -->
      <div class="input-box">
        <div class="inputs">
          <div class="name input">
            <label for="name">
              <span class="iconfont icon-wode"></span>
            </label>
            <input
              id="name"
              type="number"
              oninput="if(value.length>11)value=value.slice(0,11)"
              v-model="name"
              :placeholder="gL('accountName')"
            />
          </div>
          <div class="pass input">
            <label for="pass">
              <span class="iconfont icon-icon2"></span>
            </label>
            <input
              id="pass"
              :type="!easyPassword ? 'password' : 'text'"
              v-model="pass"
              maxlength="16"
              :placeholder="gL('password')"
              @keyup.enter="clickLogin"
            />
            <label
              for="pass"
              class="eye-icon"
              @click="() => (easyPassword = !easyPassword)"
            >
              <span
                class="icons"
                :class="
                  !easyPassword ? 'icons-eye-fill' : 'icons-eye-slash-fill'
                "
              ></span>
            </label>
          </div>
          <!-- <div class="ip input">
            <label for="ip">
              <span class="iconfont icon-lianjie"></span>
            </label>
            <input id="ip" type="text" maxlength="15" v-model="ip" oninput="this.value=value.replace(/[^\d|.]/g,'');if(this.value=='')(this.value='');" :placeholder="gL('ipNumber')">
          </div> -->
        </div>
        <!-- 记住密码 -->
        <!-- <div class="check" :style="{
          flexDirection:gLang == 2 ? 'row' : 'row-reverse',

          }">
          <el-checkbox id="rem" v-model="iSremember">{{gL("remember")}}</el-checkbox>
          <input id="rem" type="checkbox" v-model="iSremember">
          <label for="rem">{{gL("remember")}}</label>
        </div> -->
        <!-- 登录按钮 -->
        <div class="btn">
          <!-- <span>{{gL('login')}}</span> -->
          <el-button
            class="button"
            type="primary"
            @click="clickLogin"
            :loading="load"
            >{{ gL("login") }}</el-button
          >
        </div>
        <div
          class="user-agreement"
          :class="gLang == 1 ? 'ug-agreement' : 'zh-agreement'"
        >
          <el-checkbox v-model="userAgreement"></el-checkbox>
          <div class="user-agreement-txt">
            <a
              href="javascript:void(0);"
              class="privace-link"
              @click="toUserAgreement"
              >{{ gL("privacText") }}</a
            >
            <span @click="() => changeCheckUserAgreement(!userAgreement)">{{
              gL("privac")
            }}</span>
          </div>
        </div>

        <div class="forgot-password" @click="toForgotPassword">{{ gL("forgotPassword") }}</div>
      </div>
      <!-- <div class="text-center">
        <el-checkbox class="print-switch" v-model="disablePrint">{{gL('disablePrint')}}</el-checkbox>
      </div> -->
      <div
        class="info"
        :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
      >
        <div>{{ gL("companyName") }}</div>
        <div>&nbsp;&nbsp;|&nbsp;&nbsp;</div>
        <div :style="{ direction: gLang == 2 ? '' : 'rtl' }">
          {{ gL("hotLine") }}:
          <span class="num">400-800-9872</span>
        </div>
      </div>
    </div>
    <!-- 餐厅信息初始化 -->
    <!-- <shopInfo></shopInfo> -->
    <div class="mask" v-if="modal_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ gL("server") }}</span>
          <span
            class="iconfont icon-jia-copy"
            @click="modal_box = false"
          ></span>
        </div>
        <div class="content" :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">
          <div class="i-line">
            <div class="check" :class="gLang == 1 ? 'fixed' : ''">
              <input
                type="radio"
                id="serverTypeCloud"
                name="serverType"
                value="0"
                v-model="settingData.linkingType"
              />
              <label for="serverTypeCloud">{{ gL("ipAddress") }}</label>
            </div>
            <div class="input">
              <div class="inputs">
                <input
                  type="text"
                  ref="one"
                  v-model="ips.one"
                  @input="testing(1)"
                  maxlength="3"
                />
                <span class="dit">.</span>
                <input
                  type="text"
                  ref="two"
                  v-model="ips.two"
                  @input="testing(2)"
                  maxlength="3"
                />
                <span class="dit">.</span>
                <input
                  type="text"
                  ref="three"
                  v-model="ips.three"
                  @input="testing(3)"
                  maxlength="3"
                />
                <span class="dit">.</span>
                <input
                  type="text"
                  ref="four"
                  v-model="ips.four"
                  @input="testing(4)"
                  maxlength="3"
                />
              </div>
            </div>
          </div>

          <div class="i-line">
            <div class="check" :class="gLang == 1 ? 'fixed' : ''">
              <input
                type="radio"
                id="serverTypeDomain"
                name="serverType"
                value="1"
                v-model="settingData.linkingType"
              />
              <label for="serverTypeDomain">{{ gL("domainName") }}</label>
            </div>
            <div class="input">
              <input
                class="domain"
                style="direction:ltr;"
                type="text"
                name="domain"
                v-model="settingData.domain"
              />
            </div>
          </div>

          <div class="i-line">
            <div class="check" :class="gLang == 1 ? 'fixed' : ''">
              <input
                type="radio"
                id="serverTypeLocal"
                name="serverType"
                value="2"
                v-model="settingData.linkingType"
              />
              <label for="serverTypeLocal">{{ gL("localAddress") }}</label>
            </div>
            <div class="input">
              <div class="inputs">
                <input
                  type="text"
                  ref="five"
                  v-model="ips.five"
                  @input="testing(5)"
                  maxlength="3"
                />
                <span class="dit">.</span>
                <input
                  type="text"
                  ref="six"
                  v-model="ips.six"
                  @input="testing(6)"
                  maxlength="3"
                />
                <span class="dit">.</span>
                <input
                  type="text"
                  ref="seven"
                  v-model="ips.seven"
                  @input="testing(7)"
                  maxlength="3"
                />
                <span class="dit">.</span>
                <!-- <input type="text" ref="eight" v-model="ips.eight" @input="testing(4)" maxlength="3" @keyup.enter="confirm"> -->
                <input
                  type="text"
                  ref="eight"
                  v-model="ips.eight"
                  @input="testing(8)"
                  maxlength="3"
                />
              </div>
            </div>
          </div>
          <div class="btn-area" style="direction:ltr;">
            <div class="btn sec" @click="reset">{{ gL("reset") }}</div>
            <div class="btn" @click="confirm">{{ gL("confirm") }}</div>
          </div>
        </div>
        <!-- <div class="content" :style="{direction:gLang==2?'ltr':'rtl'}">
          <div class="inputs">
            <input type="text" ref="one" v-model="ips.one" @input="testing(1)" maxlength="3">
            <span>.</span>
            <input type="text" ref="two" v-model="ips.two" @input="testing(2)" maxlength="3">
            <span>.</span>
            <input type="text" ref="three" v-model="ips.three" @input="testing(3)" maxlength="3">
            <span>.</span>
            <input
              type="text"
              ref="four"
              v-model="ips.four"
              @input="testing(4)"
              maxlength="3"
              @keyup.enter="confirm"
            >
          </div>
        </div>
        <div class="btn" @click="confirm">{{gL('confirm')}}</div> -->
      </div>
    </div>

    <div class="user-modal">
      <el-dialog
        :title="gL('userAgreementTitle')"
        :visible.sync="showUserAgreement"
        width="50%"
        custom-class="user-modal-dialog"
        center
      >
        <iframe
          class="iframe"
          :src="
            `https://${this.ip}/${
              gLang == 1 ? 'ug' : 'zh'
            }/agreement/windows-user-agreement`
          "
          frameborder="0"
        ></iframe>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="() => confirmUserAgreement(true)">{{
            gL("agree")
          }}</el-button>
          <el-button @click="() => confirmUserAgreement(false)">{{
            gL("disagree")
          }}</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 选择餐厅弹出框 -->
    <el-dialog
      :title="gL('merchatDialogTitle')"
      :visible.sync="showMerchantDialog"
      width="36%"
      top="25vh"
      custom-class="merchant-dialog-box"
      :center="true"
      :destroy-on-close="true"
    >
      <div class="merchant-list">
        <el-card
          shadow="always"
          class="merchant-item"
          :class="gLang == 1 ? 'ug-item' : 'zh-item'"
          v-for="item in merchatList"
          :key="item.id"
        >
          <div class="item-box" @click="login(item.no)">
            <!-- <div class="merchant-item-logo">
              <img :src="item.logo" alt="" />
            </div> -->
            <el-image
              style="width: 60px; height: 60px"
              :src="item.logo"
              fit="cover"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <div class="merchant-item-name">
              <span class="merchant-name">{{
                gLang == 1 ? item.name_ug : item.name_zh
              }}</span>
              <span class="merchant-no">{{ item.no }}</span>
            </div>
          </div>
          <div class="merchant-btn">
            <el-button type="primary" @click="login(item.no)">{{
              gL("login")
            }}</el-button>
          </div>
        </el-card>
      </div>
    </el-dialog>

    <!-- 修改密码弹出框 -->
     <ForgotPassword ref="forgotPassword" />
  </div>
</template>

<style lang="less">
.print-switch .el-checkbox__label {
  font-size: 20px;
}
.user-modal {
  .user-modal-dialog {
    .el-dialog__body {
      height: 520px;
    }
  }
}
.merchant-list .merchant-item .el-card__body {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.merchant-list .merchant-item.ug-item .el-card__body {
  direction: rtl;
}
.merchant-list .merchant-item.zh-item .el-card__body {
  direction: ltr;
}
.merchant-list .merchant-item .el-image {
  margin-inline-end: 20px;
  .image-slot {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .el-icon-picture-outline {
    font-size: 40px;
  }
}
.merchant-list .merchant-item .merchant-item-name {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  font-size: 18px;
}
</style>

<style lang="less" scoped>
@bgColor: #333333;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@orangeColor: #ff9c00;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.forgot-password {
  margin-top: 12px;
  text-align: center;
  color: @greenColor;
  cursor: pointer;
}
.wrap {
  width: 100%;
  background-color: @bgColor;
  height: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  min-width: @minWidth;
  min-height: @minHeight;
  padding: 60px 40px 20px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  // 顶部
  .top {
    width: 100%;
    text-align: center;
    position: relative;
    padding-bottom: 50px;
    .ip-btn {
      position: absolute;
      left: 0;
      font-size: 26px;
      color: @greenColor;
      top: 7px;
      cursor: pointer;
    }
    .ip-btn-zh {
      font-size: 22px;
    }
    .title {
      font-size: 40px;
      color: #ffffff;
    }
    .lng {
      position: absolute;
      right: 0;
      top: 10px;
      display: flex;
      justify-content: space-between;
      .uy {
        font-size: 26px;
        color: #b2b2b2;
        cursor: pointer;
      }
      .zh {
        font-size: 22px;
        color: #b2b2b2;
        cursor: pointer;
      }
      .active {
        color: @greenColor;
      }
      .lines {
        display: inline-block;
        width: 2px;
        height: 20px;
        background: @graphiteColor;
        margin: 0 20px;
      }
    }
  }
  .input-box {
    margin-bottom: auto;
  }
  // 输入框
  .inputs {
    background-color: #ffffff;
    font-size: 26px;
    width: 432px;
    margin: 0px auto;
    .input {
      padding: 20px;
      border-bottom: 1px solid @graphiteColor;
      &:last-child {
        border-bottom: none;
      }
      .iconfont {
        font-size: 26px;
        color: #b3b3b3;
      }
      input {
        outline: none;
        padding-left: 25px;
        width: 320px;
        color: #1a1a1a;
      }
    }
  }
  //记住密码
  .check {
    width: 432px;

    display: flex;
    align-items: center;
    font-size: 22px;
    color: #f2f2f2;
    margin: 0 auto;
    padding-top: 20px;
    input {
      cursor: pointer;
      width: 25px;
      height: 25px;
    }
    label {
      cursor: pointer;
      padding: 0 20px;
      margin-bottom: -6px;
    }
  }
  // 登录按钮
  .btn {
    width: 435px;
    margin: 0px auto;
    // background: @greenColor;
    font-size: 26px;
    color: #ffffff;
    // padding: 20px 0;
    text-align: center;
    margin-top: 40px;
    cursor: pointer;
    button {
      width: 100%;
      padding: 20px 0;
      font-size: 26px;
    }
  }

  .print-switch {
    color: #cccccc;
    margin-bottom: 40px;
    font-size: 22px;
  }
  .info {
    display: flex;
    font-size: 22px;
    color: @graphiteColor;
    position: relative;
    .num {
      direction: ltr;
      display: inline-block;
    }
  }
}
//s提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  // align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    input {
      font-family: Arial, Helvetica, sans-serif;
    }
    .dit {
      line-height: 18px;
    }
    background-color: #ffffff;
    width: 660px;
    font-size: 30px;
    height: 420px;
    margin-top: 150px;
    .title {
      align-items: center;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      background-color: #e6e6e6;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content {
      text-align: center;
      padding: 50px 75px 30px 75px;
      .inputs {
        border: 1px solid #cccccc;
        display: flex;
        align-items: flex-end;
        padding-top: 10px;
        padding-bottom: 7px;
        direction: ltr;
        input {
          width: 30%;
          text-align: center;
          outline: none;
        }
      }
    }
    .btn {
      margin: 20px 75px 20px 75px;
      background: @greenColor;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
    .btn.sec {
      background: @orangeColor;
    }
    .content {
      padding: 40px 60px;
      .i-line {
        display: flex;
        margin-bottom: 20px;
        .check {
          line-height: 50px;
          width: 150px;
          margin-right: 20px;
          input {
            vertical-align: middle;
            outline: none;
          }
          label {
            vertical-align: middle;
            color: #666;
          }
        }
        .check.fixed {
          text-align: right;
          margin-left: 20px;
          margin-right: 0;
        }
        .input {
          flex: 1;
          input.domain {
            width: 100%;
            padding: 10px 4% 7px;
            border: #cccccc 1px solid;
            outline: none;
          }
        }
      }
      .btn-area {
        display: flex;
        .btn {
          flex: 1;
          margin: 0 20px;
        }
        :first-child {
          margin-left: 0;
        }
        :last-child {
          margin-right: 0;
        }
      }
    }
  }
}
.eye-icon {
  cursor: pointer;
  display: inline-block;
  width: 30px;
  text-align: center;
  .icons {
    font-size: 24px;
  }
}

.ug-agreement {
  direction: rtl;
  .user-agreement-txt {
    display: flex;
    flex-direction: row;
  }
}

.zh-agreement {
  direction: ltr;
  .user-agreement-txt {
    display: flex;
    flex-direction: row-reverse;
  }
}

.user-agreement {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0px;
  margin-top: 30px;
  color: #fff;
  .user-agreement-txt {
    margin: 0px 6px -2px;
    font-size: 20px;
  }
  a {
    color: #139d59;
    font-size: 20px;
    font-weight: 400;
  }
}

.user-modal {
  .iframe {
    width: 100%;
    height: 100%;
  }
}

.merchant-list {
  .merchant-item {
    cursor: pointer;
    margin-bottom: 20px;

    .item-box {
      height: 100%;
      display: flex;
    }
  }
}
</style>

<script>
// import shopInfo from "./components/shopInfo.vue";
import axios from "axios";
import { v4 as uuidV4 } from "uuid";
import { postAuthorizationsAPI, postAuthMerchantsAPI } from "./api/index.js";
import ForgotPassword from "./components/ForgotPassword.vue";

var self;
export default {
  name: "app",
  activated() {
    window.electronAPI && window.electronAPI.closeMqtt();
    // mqtt.close()
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.ip = localStorage.getItem("ip_adr");
    this.merchantInfo = JSON.parse(localStorage.getItem("merchant_info")) || {};
    if (!this.ip) {
      this.ip = "ros-api-v2.mulazim.com";
      localStorage.setItem("ip_adr", this.ip);
      axios.defaults.baseURL = "https://" + this.ip;
    }

    const userAgreementStorage = localStorage.getItem("userAgreement");
    if (userAgreementStorage == "true") {
      self.userAgreement = true;
    }

    self.getCompany();
    //如果本地保存过IP
    if (localStorage.getItem("settings")) {
      self.reset();
    } else {
      self.settingData.domain = "ros-api-v2.mulazim.com";
      self.settingData.local = "**************";
      self.settingData.cloud = "**************";
      self.settingData.linkingType = 1;
      localStorage.setItem("settings", JSON.stringify(self.settingData));
      this.reset();
    }
    /*
    if (localStorage.getItem("ip_adr")) {
      self.ip = localStorage.getItem("ip_adr");
    }
    */
    self.pass = "";
    self.load = false;
    //如果是记住获取Cookie里的数据
    if (this.merchantInfo.user != "") {
      self.name = this.merchantInfo.user;
    } else {
      self.name = "";
      self.pass = "";
    }
    // 如果之前登录过并没有退出，则直接登录
    if (localStorage.getItem("token") != null) {
      self.directLogin();
    }
    if (localStorage.getItem("disablePrint") == null) {
      localStorage.setItem("disablePrint", false);
    } else {
      this.disablePrint =
        localStorage.getItem("disablePrint") == "true" ? true : false;
    }

    if (localStorage.getItem("shouldRefresh") == "true") {
      localStorage.removeItem("shouldRefresh");
      console.log("#############");
      console.log("reload");
      location.reload();
    }

    // const pwd = localStorage.getItem('pwd')
    // console.log("pwd", pwd);
    // if (pwd != null) {

    //   // this.iSremember = true;
    //   this.pass = pwd;
    // }
  },
  data() {
    return {
      msg: "login",
      gLang: 1,
      name: "",
      pass: "",
      ip: "",
      settingData: {
        cloud: "",
        local: "",
        domain: "",
        linkingType: 0
      },
      // iSremember: false,
      modal_box: false,
      disablePrint: false,
      ips: {
        one: "",
        two: "",
        three: "",
        four: "",
        five: "",
        six: "",
        seven: "",
        eight: ""
      },
      load: false,
      easyPassword: false,
      userAgreement: false,
      showUserAgreement: false,
      showMerchantDialog: false,
      merchatList: [],
      merchantInfo: {}
    };
  },
  watch: {
    disablePrint(val) {
      localStorage.setItem("disablePrint", val);
    }
  },
  components: {
    ForgotPassword
  },
  methods: {
    // 显示忘记密码弹出框
    toForgotPassword() {
      this.$refs.forgotPassword.showDialog();
    },

    // 打开隐私协议页面
    toUserAgreement() {
      this.showUserAgreement = true;
    },

    // 确认隐私协议
    confirmUserAgreement(check) {
      this.changeCheckUserAgreement(check);
      this.showUserAgreement = false;
    },

    // 点击用户隐私协议
    changeCheckUserAgreement(check) {
      this.userAgreement = check;
      localStorage.setItem("userAgreement", check);
    },
    getCompany() {
      // axios.get('https://info.almas.biz/api/v1/company_info?id=1')
      //   .then(response => {
      //     console.log(response);
      //   })
      //   .catch(err => {
      //     console.log(err);
      //   })
    },
    ipBox() {
      self.modal_box = true;
      setTimeout(() => {
        self.$refs["one"].focus();
      }, 500);
    },
    find(str, cha, num) {
      var x = str.indexOf(cha);
      for (var i = 0; i < num; i++) {
        x = str.indexOf(cha, x + 1);
      }
      return x;
    },
    //Ip验证
    testing(e) {
      switch (e) {
        case 1:
          self.ips.one = self.testingNumber(self.ips.one);
          if (self.ips.one.length == 3) {
            self.$refs["two"].focus();
          }
          break;
        case 2:
          self.ips.two = self.testingNumber(self.ips.two);
          if (self.ips.two.length == 3) {
            self.$refs["three"].focus();
          }
          if (self.ips.two.length == 0) {
            self.$refs["one"].focus();
          }
          break;
        case 3:
          self.ips.three = self.testingNumber(self.ips.three);
          if (self.ips.three.length == 3) {
            self.$refs["four"].focus();
          }
          if (self.ips.three.length == 0) {
            self.$refs["two"].focus();
          }
          break;
        case 4:
          self.ips.four = self.testingNumber(self.ips.four);
          if (self.ips.four.length == 0) {
            self.$refs["three"].focus();
          }
          break;

        case 5:
          self.ips.five = self.testingNumber(self.ips.five);
          if (self.ips.five.length == 3) {
            self.$refs["six"].focus();
          }
          if (self.ips.five.length == 0) {
            self.$refs["four"].focus();
          }
          break;

        case 6:
          self.ips.six = self.testingNumber(self.ips.six);
          if (self.ips.six.length == 3) {
            self.$refs["seven"].focus();
          }
          if (self.ips.six.length == 0) {
            self.$refs["five"].focus();
          }
          break;

        case 7:
          self.ips.seven = self.testingNumber(self.ips.seven);
          if (self.ips.seven.length == 3) {
            self.$refs["eight"].focus();
          }
          if (self.ips.seven.length == 0) {
            self.$refs["six"].focus();
          }
          break;

        case 8:
          self.ips.eight = self.testingNumber(self.ips.eight);
          if (self.ips.eight.length == 0) {
            self.$refs["seven"].focus();
          }
          break;
      }
    },
    testingNumber(e) {
      e = e.replace(/[^\d.]/g, ""); //先把非数字的都替换掉，除了数字和.
      e = e.replace(/\./g, ""); //必须保证第一个为数字而不是.
      return e;
    },
    reset() {
      self.settingData = JSON.parse(localStorage.getItem("settings"));
      if (self.settingData.cloud != "") {
        let cloudIps = self.settingData.cloud.split(".");
        self.ips.one = cloudIps[0];
        self.ips.two = cloudIps[1];
        self.ips.three = cloudIps[2];
        self.ips.four = cloudIps[3];
      }
      if (self.settingData.local != "") {
        let localIps = self.settingData.local.split(".");
        self.ips.five = localIps[0];
        self.ips.six = localIps[1];
        self.ips.seven = localIps[2];
        self.ips.eight = localIps[3];
      }

      switch (parseInt(self.settingData.linkingType)) {
        case 0:
          self.ip = self.settingData.cloud;
          break;
        case 1:
          self.ip = self.settingData.domain;
          break;
        case 2:
          self.ip = self.settingData.local;
          break;
        default:
      }
    },
    //连接IP
    confirm() {
      let settings = {};
      // if (localStorage.getItem('settings') === null) {
      //   settings = {
      //     cloud: '',
      //     local: '',
      //     domain: '',
      //     linkingType: 1
      //   }
      // } else {
      //   settings = JSON.parse(localStorage.getItem('settings'))
      // }

      self.settingData.cloud =
        self.ips.one +
        "." +
        self.ips.two +
        "." +
        self.ips.three +
        "." +
        self.ips.four;
      self.settingData.local =
        self.ips.five +
        "." +
        self.ips.six +
        "." +
        self.ips.seven +
        "." +
        self.ips.eight;
      // 验证云端IP
      if (self.$isValidIP(self.settingData.cloud)) {
        settings.cloud = self.settingData.cloud;
      } else {
        self.settingData.cloud = settings.cloud;
        this.$message({
          message: self.gL("ipReg"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
      // 验证本地IP
      if (self.$isValidIP(self.settingData.local)) {
        settings.local = self.settingData.local;
      } else {
        self.settingData.local = settings.local;
        this.$message({
          message: self.gL("ipReg"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
      // 验证域名
      if (self.$isValidDomain(self.settingData.domain)) {
        settings.domain = self.settingData.domain;
      } else {
        self.settingData.domain = settings.domain;
        this.$message({
          message: self.gL("domainNameReg"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
      // 获取连接方式
      settings.linkingType = self.settingData.linkingType;
      // 列出保存失败原因
      let reason1 = settings.linkingType === 0 && settings.cloud == "";
      let reason2 = settings.linkingType === 1 && settings.domain == "";
      let reason3 = settings.linkingType === 2 && settings.local == "";
      if (reason1 || reason2 || reason3) {
        this.$message({
          message: self.gL("inputIp"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      } else {
        // localStorage.setItem('settings', JSON.stringify(settings))
        console.log(settings);
        switch (parseInt(settings.linkingType)) {
          case 0:
            self.ip = settings.cloud;
            break;
          case 1:
            self.ip = settings.domain;
            break;
          case 2:
            self.ip = settings.local;
            break;
          default:
        }

        localStorage.setItem("settings", JSON.stringify(settings));

        localStorage.setItem("ip_adr", self.ip);
        // localStorage.setItem("settings_id", settings.domain);
        // console.log('self_ip',self.ip)
        // console.log('localStorage',localStorage);
        if (localStorage.getItem("ip_adr") == settings.domain) {
          axios.defaults.baseURL = "https://" + localStorage.getItem("ip_adr");
        } else {
          axios.defaults.baseURL = "http://" + localStorage.getItem("ip_adr");
        }
        // self.clickLogin();
        self.modal_box = false;
      }
    },
    // 切换语言
    changeLang() {
      if (self.gLang == 1) {
        self.gLang = 2;
        this.$i18n.locale = "zh-cn";
      } else {
        self.gLang = 1;
        this.$i18n.locale = "ug";
      }
      localStorage.setItem("langId", self.gLang);
      self.$switchLang();
      if (DesktopRuntime != null) {
        DesktopRuntime.setCurrentLanguage(parseInt(self.gLang));
      }
      // window.history.go(0);
    },
    // 登录按钮
    clickLogin() {
      if (!this.userAgreement) {
        this.$message({
          message: this.gL("privacTips"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      if (self.ip != "") {
        if ((self.name.length > 2) & (self.pass.length > 5)) {
          self.load = true;

          postAuthMerchantsAPI({
            username: self.name,
            password: self.pass
          })
            .then(res => {
              this.showMerchantDialog = true;
              this.merchatList = res.data.data;
              this.load = false;
            })
            .catch(err => {
              this.load = false;
              console.log("--- 获取餐厅信息失败 -- ", err);
            });
        } else {
          if (self.name == "") {
            this.$message({
              message: self.gL("inputAccount"),
              type: "warning",
              customClass: self.$toastClass(),
              offset: 120
            });
          } else if (self.pass == "") {
            this.$message({
              message: self.gL("inputPassword"),
              type: "warning",
              customClass: self.$toastClass(),
              offset: 120
            });
          } else if (self.name.length < 3) {
            this.$message({
              message: self.gL("accountLength"),
              type: "warning",
              customClass: self.$toastClass(),
              offset: 120
            });
          } else if (self.pass.length < 6) {
            this.$message({
              message: self.gL("passLength"),
              type: "warning",
              customClass: self.$toastClass(),
              offset: 120
            });
          }
        }
      } else {
        self.ipBox();
      }
    },

    // 登录
    login(merchantNo) {
      postAuthorizationsAPI({
        username: this.name,
        password: this.pass,
        merchant_no: merchantNo
      })
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            localStorage.setItem(
              "merchant_info",
              JSON.stringify({
                user: this.name,
                userName: response.data.data.user.name,
                id: response.data.data.user.id,
                merchant_name_zh: response.data.data.merchant.name_zh,
                merchant_name_ug: response.data.data.merchant.name_ug,
                merchant_address_ug: response.data.data.merchant.address_ug,
                merchant_address_zh: response.data.data.merchant.address_zh,
                operation_password: response.data.data.user.operation_password,
                isHandOver: response.data.data.user.is_handover,
                merchant_no: response.data.data.merchant.no,
                merchant_phone: response.data.data.merchant.phone,
                mode: response.data.data.merchant.mode,
              })
            );

            localStorage.setItem(
              "token",
              response.data.data.token.access_token
            );

            this.showMerchantDialog = false;

            self.$message({
              // message: self.gL("successLogin"),
              message: response.data.message,
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            let uu = JSON.parse(localStorage.getItem("mqtt_uuid"));
            if (
              !uu ||
              uu == null ||
              uu.merchant_no != response.data.data.merchant_no
            ) {
              uu = {
                uuid: uuidV4(),
                merchant_no: response.data.data.merchant_no
              };
              localStorage.setItem("mqtt_uuid", JSON.stringify(uu));
            }
            // mqtt.connect()
            console.log("uuid", uu.uuid);
            const disablePrint = localStorage.getItem("disablePrint");
            const url = localStorage.getItem("ip_adr");
            window.electronAPI &&
              window.electronAPI.connectMqtt &&
              window.electronAPI.connectMqtt({
                uuid: uu.uuid,
                merchantNo: response.data.data.merchant_no,
                lang: this.gLang,
                disablePrint,
                url
              });
            if (response.data.data.isHandOver) {
              if (response.data.data.merchant.mode == 2) {
                self.$router.push({ path: "/table", query: { is: 1 } });
              } else {
                self.$router.push({ path: "/bill", query: { is: 1 } });
              }
            } else {
              if (response.data.data.merchant.mode == 2) {
                self.$router.push({ path: "/table" });
              } else {
                self.$router.push({ path: "/bill" });
              }
            }
            self.load = false;
          }
        })
        .finally(() => {
          this.load = false;
        });
    },

    // 直接登录
    directLogin() {
      if (this.merchantInfo.isHandOver) {
        if (this.merchantInfo.mode == 2) {
          self.$router.push({ path: "/table", query: { is: 1 } });
        } else {
          self.$router.push({ path: "/bill", query: { is: 1 } });
        }
      } else {
        if (this.merchantInfo.mode == 2) {
          self.$router.push({ path: "/table" });
        } else {
          self.$router.push({ path: "/bill" });
        }
      }
      self.load = false;
    }
  }
};
</script>
