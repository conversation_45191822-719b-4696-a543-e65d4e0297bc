<template>
    <BaseButton backgroundColor="#FF9C00" @click="click">

        <svg viewBox="0 0 33.9707 33.9512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
            fill="none" customFrame="#000000">
            <g id="组合 1988">
                <path id="矢量 13"
                    d="M16.9867 24.1056C13.1362 24.1056 10.0039 20.9075 10.0039 16.9762C10.0039 13.045 13.1362 9.84473 16.9888 9.84473C20.8414 9.84473 23.9715 13.0429 23.9715 16.9762C23.9715 20.9075 20.8393 24.1056 16.9888 24.1056L16.9867 24.1056ZM16.9867 13.0556C14.8702 13.0556 13.1489 14.8151 13.1489 16.9762C13.1489 19.1374 14.8702 20.8947 16.9867 20.8947C19.1032 20.8947 20.8244 19.1374 20.8244 16.9762C20.8244 14.8151 19.1032 13.0556 16.9867 13.0556L16.9867 13.0556Z"
                    fill="rgb(255,255,255)" fill-rule="nonzero" />
                <path id="矢量 14"
                    d="M14.64 33.951C14.4909 33.9503 14.3448 33.9288 14.2018 33.8865C14.0588 33.8442 13.9246 33.7828 13.7991 33.7023C13.6736 33.6218 13.5618 33.5253 13.4638 33.4129C13.3658 33.3006 13.2854 33.1767 13.2226 33.0415L11.767 29.9539C11.1238 29.6903 10.504 29.3801 9.90761 29.0231L6.77323 29.7881C6.62751 29.8234 6.47977 29.8372 6.33003 29.8297C6.18029 29.8221 6.03471 29.7934 5.89328 29.7436C5.75185 29.6938 5.62041 29.625 5.49894 29.5371C5.37747 29.4492 5.27099 29.3459 5.17948 29.2271L2.24698 25.4722C2.15407 25.3521 2.07936 25.2215 2.02287 25.0804C1.96637 24.9394 1.93024 24.7934 1.91448 24.6423C1.89871 24.4912 1.90391 24.3408 1.93008 24.1912C1.95624 24.0416 2.00238 23.8984 2.06848 23.7616L3.55598 20.6889C3.37896 20.0639 3.24438 19.4299 3.15223 18.787L0.608608 16.7682C0.49005 16.6733 0.386851 16.5638 0.29901 16.4399C0.211168 16.316 0.142079 16.1824 0.0917415 16.0391C0.0414039 15.8958 0.0117626 15.7483 0.00281756 15.5967C-0.00612746 15.445 0.00596936 15.2951 0.039108 15.1468L1.07611 10.4633C1.10795 10.3159 1.15973 10.1758 1.23147 10.043C1.3032 9.91031 1.39203 9.79023 1.49796 9.6828C1.60389 9.57537 1.72271 9.48485 1.85441 9.41125C1.98611 9.33765 2.12547 9.28389 2.27248 9.24997L5.55348 8.5126C5.91261 7.99622 6.30148 7.50322 6.72223 7.03997L6.66698 3.74622C6.66389 3.59487 6.68178 3.44605 6.72065 3.29974C6.75953 3.15344 6.81786 3.01536 6.89566 2.8855C6.97346 2.75564 7.0677 2.63907 7.17837 2.53578C7.28904 2.43249 7.41182 2.34652 7.54673 2.27785L11.767 0.163476C11.9009 0.0964061 12.0417 0.0498071 12.1892 0.0236788C12.3367 -0.00244958 12.4849 -0.00702463 12.6337 0.00995361C12.7826 0.0269319 12.9259 0.0647599 13.0637 0.123438C13.2016 0.182115 13.3282 0.259211 13.4436 0.354726L16.0637 2.49885C16.7331 2.45423 17.3706 2.45635 18.0485 2.5116L20.5432 0.424851C20.6577 0.328687 20.7835 0.250763 20.9206 0.191078C21.0577 0.131393 21.2005 0.0924044 21.3489 0.0741131C21.4973 0.0558218 21.6452 0.0589805 21.7927 0.0835892C21.9402 0.108198 22.0812 0.153243 22.2156 0.218726L26.4592 2.28635C26.5948 2.35371 26.7183 2.43851 26.8299 2.54075C26.9415 2.64298 27.0367 2.75866 27.1157 2.88778C27.1946 3.0169 27.2542 3.15442 27.2943 3.30035C27.3344 3.44627 27.3536 3.5949 27.3517 3.74623L27.3305 7.17598C27.7427 7.64348 28.1231 8.13648 28.4716 8.65285L31.6272 9.3201C32.2244 9.4476 32.6961 9.9151 32.8385 10.5207L33.9265 15.1894C33.9615 15.3371 33.9755 15.4868 33.9684 15.6384C33.9614 15.79 33.9337 15.9378 33.8852 16.0817C33.8368 16.2255 33.7695 16.36 33.6833 16.4849C33.5972 16.6099 33.4955 16.7206 33.3782 16.8171L30.7539 18.9549C30.6587 19.5669 30.5248 20.1704 30.3522 20.7654L31.8312 23.6872C31.8998 23.8229 31.9486 23.9654 31.9774 24.1147C32.0062 24.264 32.0141 24.4144 32.001 24.5658C31.9878 24.7173 31.9542 24.8641 31.9001 25.0062C31.846 25.1483 31.7734 25.2803 31.6825 25.4021L28.818 29.208C28.7287 29.3288 28.6241 29.4343 28.5042 29.5247C28.3842 29.615 28.2538 29.6864 28.1131 29.7389C27.9724 29.7913 27.8271 29.8226 27.6773 29.8328C27.5274 29.843 27.3793 29.8316 27.2327 29.7987L23.9539 29.0614C23.3618 29.412 22.747 29.7166 22.1094 29.9751L20.7706 32.9671C20.7098 33.1047 20.631 33.231 20.5339 33.346C20.4369 33.461 20.3257 33.56 20.2003 33.643C20.0749 33.7261 19.9403 33.7898 19.7966 33.8342C19.6528 33.8787 19.5058 33.902 19.3554 33.9042L14.6549 33.951L14.64 33.951L14.64 33.951ZM10.1796 25.6996C10.4835 25.6996 10.7831 25.791 11.0424 25.9631C11.7861 26.4604 12.5872 26.862 13.4224 27.1595C13.6216 27.2304 13.7995 27.3369 13.9561 27.4791C14.1127 27.6213 14.2358 27.7882 14.3255 27.9797L15.6217 30.7295L18.3247 30.704L19.5211 28.0329C19.6096 27.8328 19.7342 27.6585 19.8949 27.5101C20.0555 27.3616 20.2391 27.2511 20.4455 27.1786C21.2856 26.8873 22.0768 26.4971 22.8191 26.0077C22.9944 25.892 23.1853 25.8151 23.3918 25.7771C23.5984 25.7391 23.8041 25.743 24.0091 25.7889L26.9331 26.4434L28.58 24.2546L27.2582 21.6451C27.1594 21.4483 27.1027 21.2401 27.0879 21.0204C27.0732 20.8007 27.1016 20.5868 27.1732 20.3786C27.4452 19.5839 27.628 18.7594 27.7151 17.9285C27.7371 17.7164 27.7987 17.5164 27.9 17.3287C28.0012 17.141 28.1344 16.9797 28.2995 16.8447L30.637 14.9429L30.0101 12.2569L27.1924 11.6619C26.9792 11.6162 26.784 11.53 26.6067 11.4033C26.4294 11.2766 26.2846 11.1198 26.1724 10.933C25.7371 10.2099 25.2257 9.54551 24.6381 8.93973C24.4901 8.78683 24.3766 8.61161 24.2975 8.41407C24.2184 8.21653 24.1797 8.01138 24.1812 7.7986L24.2025 4.74498L21.763 3.55498L19.536 5.4186C19.3701 5.55786 19.1833 5.65816 18.9756 5.71951C18.7679 5.78086 18.5566 5.79814 18.3417 5.77135C17.4778 5.65473 16.6129 5.64765 15.7471 5.7501C15.5388 5.77309 15.3342 5.75473 15.1333 5.69501C14.9324 5.63529 14.7509 5.53891 14.589 5.40585L12.2557 3.49548L9.82899 4.71098L9.87786 7.65198C9.88176 7.87164 9.84279 8.08341 9.76094 8.28729C9.67909 8.49117 9.56082 8.67111 9.40611 8.8271C8.81111 9.4221 8.28199 10.0851 7.83361 10.8034C7.723 10.9827 7.58202 11.1338 7.41068 11.2565C7.23933 11.3792 7.05094 11.464 6.84549 11.511L3.92361 12.1655L3.32861 14.8579L5.59811 16.6641C5.77016 16.8013 5.90839 16.9668 6.01281 17.1605C6.11722 17.3542 6.17945 17.5606 6.19949 17.7797C6.27811 18.634 6.45661 19.4819 6.73074 20.3C6.79896 20.5025 6.82646 20.7104 6.81322 20.9237C6.79998 21.137 6.74699 21.3399 6.65424 21.5325L5.33036 24.2695L7.01549 26.4264L9.81411 25.7464C9.93406 25.7161 10.0559 25.7005 10.1796 25.6996L10.1796 25.6996Z"
                    fill="rgb(255,255,255)" fill-rule="nonzero" />
            </g>
        </svg>

    </BaseButton>
</template>
<script>
import BaseButton from './BaseButton.vue'
export default {
    name: 'SettingButton',
    components: {
        BaseButton
    },
    methods: {
        click() {
            this.$emit('click')
        }
    }
}
</script>
<style scoped>
svg {
    width: 20px;
    height: 20px;
}
</style>