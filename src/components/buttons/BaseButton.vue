<template>
    <button :style="`background-color: ${backgroundColor}`" @click="click">
       <slot></slot>
    </button>
</template>
<script>
    export default {
        name: 'BaseButton',
        props: {
            backgroundColor: {
                type: String,
                required: true,
                default: ''
            }
        },
        methods: {
            click() {
                this.$emit('click')
            }
        }
    }
</script>
<style scoped>
button {
    width: 40px;
    height: 40px;
    display: inline-block;
    line-height: 40px;
    text-align: center;
    color: #fff;
    /* margin: 0 2px 0 0; */
    vertical-align: middle;
    svg {
        vertical-align: middle;
    }
    
}
</style>