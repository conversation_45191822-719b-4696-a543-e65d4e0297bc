<template>
  <!-- 模态框 -->
  <div class="mask" v-if="number_box" @click="market_box = false">
    <div class="box">
      <div class="title">
        <span></span>
        <span>{{ gL("openJob") }}</span>
        <span class="iconfont icon-jia-copy" @click.stop="startBox"></span>
      </div>
      <div class="content-input" :class="{ 'content-input-ug': gLang == 1 }">
        <div class="wrap">
          <div class="market" @click.stop="showMarketBox">
            <div class="text">{{ market_name }}</div>
            <div class="icon">
              <span class="iconfont icon-icon-dwon"></span>
            </div>
          </div>
          <div
            class="list"
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            v-if="market_box"
          >
            <div
              v-for="(item, index) in market"
              :key="item.id"
              @click.stop="clickItem(item)"
              :class="market_id == item.id ? 'active' : ''"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="old input">
          <input
            type="number"
            v-model="ruleForm.balance"
            oninput="if(value.length>6)value=value.slice(0,6)"
            :placeholder="gL('foreuneBalance')"
          />
        </div>
        <div class="new input">
          <input
            type="number"
            v-model="ruleForm.cash"
            oninput="if(value.length>6)value=value.slice(0,6)"
            @keyup.enter="confirm"
            :placeholder="gL('reserveFund')"
          />
        </div>
      </div>
      <div class="btn" @click.stop="confirm">
        {{ gL("confirm") }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
//s提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 600px;
    font-size: 30px;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content-input {
      padding: 40px 100px 20px 100px;
      .input {
        border: 1px solid #cccccc;
        text-align: center;
        padding: 9px 0;
        margin-bottom: 20px;
        &:last-child {
          margin-bottom: 0;
        }
        input {
          width: 93%;
          outline: none;
          font-size: 22px;
        }
      }
    }
    .content-input-ug {
      div {
        input::-webkit-input-placeholder {
          text-align: right;
        }
      }
    }
    .wrap {
      margin-bottom: 20px;
      border: 1px solid #cccccc;
      position: relative;
      .market {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 26px;
        cursor: pointer;
        .icon {
          width: 50px;
          height: 48px;
          line-height: 48px;
          text-align: center;
          background-color: #f2f2f2;
          .iconfont {
            color: @grayColor;
          }
        }
        .text {
          padding: 11px;
        }
      }
      .list {
        border: 1px solid #cccccc;
        border-top: none;
        position: absolute;
        width: 100%;
        background-color: #f2f2f2;
        div {
          font-size: 22px;
          padding: 15px 20px;
          cursor: pointer;
        }
        .active {
          background-color: #ff9c00;
          color: #ffffff;
        }
      }
    }
    .btn {
      margin: 20px 100px 20px 100px;
      background: @greenColor;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
  }
}
</style>

<script>
var self;
import { getShiftIndexAPI, postOpenAPI } from "./../api/index.js";
export default {
  name: "startJob",
  created: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
  },
  activated() {
    self.getList();
  },
  watch: {
    number_box(value) {
      if (value) {
        this.market_id = self.market[0].id;
        this.market_name = self.market[0].name;
      }
    }
  },
  data() {
    return {
      ruleForm: {
        balance: "",
        cash: ""
      },
      market_id: "",
      market_name: "",
      market: [],
      market_box: false
    };
  },
  methods: {
    /**
     * 关掉模态框
     */
    startBox() {
      this.$emit("startBox", 1);
      this.ruleForm = {
        balance: "",
        cash: ""
      };
      this.market_id = false;
      this.market_name = false;

      // if (this.market.length) {
      //   this.market_id = self.market[0].id;
      //   this.market_name = self.market[0].name;
      // }
    },
    /**
     * 点击交班时间
     */
    showMarketBox() {
      this.market_box = !this.market_box;
    },
    /**
     * 点击交班时间List
     */
    clickItem(e) {
      this.market_name = e.name;
      this.market_id = e.id;
      this.showMarketBox();
    },
    //获取列表
    getList() {
      getShiftIndexAPI().then(response => {
        if (response.data.data.length) {
          self.market = response.data.data;
          this.$forceUpdate();
          self.market_id = response.data.data[0].id;
          self.market_name = response.data.data[0].name;
        } else {
          this.$message({
            message: this.gL("startJobNoShift"),
            type: "error",
            customClass: this.$toastClass(),
            offset: 120
          });
        }
      });
    },
    /**
     * 点击确定
     */
    confirm() {
      if (!this.market.length) {
        this.$message({
          message: this.gL("startJobNoShift"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (this.ruleForm.balance != "" && this.ruleForm.cash != "") {
        postOpenAPI({
          shift: this.market_id,
          working_balance: Number(this.ruleForm.balance),
          alternate_amount: Number(this.ruleForm.cash)
        }).then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.$message({
              message: this.gL("successfulOperation"),
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
            // this.$setCookie("isHandOver", false, 7); //保存交班cookie，有效期7天
            const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
            storeInfo.isHandOver = false;
            localStorage.setItem("merchant_info", JSON.stringify(storeInfo));
            this.ruleForm = {
              balance: "",
              cash: ""
            };
            this.$emit("startBox");
          }
        });
      } else {
        if (this.ruleForm.balance == "") {
          this.$message({
            message: this.gL("inputBlance"),
            type: "error",
            customClass: this.$toastClass(),
            offset: 120
          });
          return;
        }
        if (this.ruleForm.cash == "") {
          this.$message({
            message: this.gL("inputBlancePrice"),
            type: "error",
            customClass: this.$toastClass(),
            offset: 120
          });
          return;
        }
      }
    }
  },
  props: {
    number_box: {
      type: Boolean,
      default: false
    }
  }
};
</script>
