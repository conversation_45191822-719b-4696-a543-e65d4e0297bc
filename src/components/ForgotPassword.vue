<template>
  <CommonDialog
    :dialogShow.sync="show"
    :title="gL('forgotPassword')"
    width="768px"
  >
    <div class="content-input" :class="{ 'content-input-ug': gLang == 1 }">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
        <el-form-item prop="phone">
          <div class="input-item ">
            <el-input
              v-model="ruleForm.phone"
              type="number"
              :placeholder="gL('pleasePhone')"
            ></el-input>
            <el-button
              class="send-btn"
              type="primary"
              @click="sendCodeHandler"
              >{{
                codeTimeout == 0 ? gL("sendBtn") : codeTimeout + " s"
              }}</el-button
            >
          </div>
        </el-form-item>
        <el-form-item prop="code">
          <div class="input-item">
            <el-input
              v-model="ruleForm.code"
              type="number"
              :placeholder="gL('codePlaceholder')"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item prop="password">
          <div class="input-item">
            <el-input
              v-model="ruleForm.password"
              show-password
              :placeholder="gL('newPasswordPlaceholder')"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item prop="confirm_passwords">
          <div class="input-item">
            <el-input
              v-model="ruleForm.confirm_passwords"
              show-password
              :placeholder="gL('newPasswordPlaceholder')"
            ></el-input>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="btn" @click="changePass">
      {{ gL("confirm") }}
    </div>
  </CommonDialog>
</template>

<script>
import CommonDialog from "./CommonDialog.vue";
import { postResetPasswordSendCodeAPI, postResetPasswordAPI } from "./../api/index.js";

let sendCodeTimer = null;
export default {
  components: {
    CommonDialog
  },
  data() {
    const checkPassword = (rule, value, callback) => {
      if (this.ruleForm.password != this.ruleForm.confirm_passwords) {
        callback(new Error(this.gL("twoPassDifference")));
      } else {
        callback();
      }
    };
    return {
      show: false,
      codeTimeout: 0,
      gLang: 1,
      sendCode: "",
      ruleForm: {
        phone: "",
        code: "",
        password: "",
        confirm_passwords: ""
      },
      rules: {
        phone: [
          { required: true, message: this.gL("pleasePhone"), trigger: "blur" },
          { min: 11, max: 11, message: this.gL("mobile"), trigger: "blur" }
        ],
        code: [
          {
            required: true,
            message: this.gL("codePlaceholder"),
            trigger: "blur"
          }
        ],
        password: [
          {
            required: true,
            message: this.gL("newPasswordPlaceholder"),
            trigger: "blur"
          }
        ],
        confirm_passwords: [
          {
            required: true,
            message: this.gL("newPasswordPlaceholder"),
            trigger: "blur"
          },
          {
            validator: checkPassword,
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    // 显示弹出框
    showDialog() {
      this.gLang = localStorage.getItem("gLang");
      this.show = true;
    },

    // 发送验证码倒计时
    sendCodeHandler() {
      // 重置密码
      if (this.codeTimeout != 0) return;
      if (this.ruleForm.phone == "") {
        this.$message({
          message: this.gL("pleasePhone"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      } else if (this.ruleForm.phone.length != 11) {
        this.$message({
          message: this.gL("mobile"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      postResetPasswordSendCodeAPI({ phone: this.ruleForm.phone })
        .then(res => {
          this.startSendCodeTimer();
          this.sendCode = res.data.data.batchID;
        })
        .catch(err => {
          console.log("----------- err ---- ", err);
        });
    },

    // 启动验证码定时器
    startSendCodeTimer() {
      this.codeTimeout = 60;
      clearInterval(sendCodeTimer);
      sendCodeTimer = setInterval(() => {
        if (this.codeTimeout == 0) {
          clearInterval(sendCodeTimer);
          return;
        }
        this.codeTimeout--;
      }, 1000);
    },

    // 确认修改密码
    changePass() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          postResetPasswordAPI({
            batch_id: this.sendCode,
            code: this.ruleForm.code,
            password: this.ruleForm.password,
            phone: this.ruleForm.phone
          }).then(response => {
            if (response.status >= 200 && response.status < 300) {
              this.$message({
                message: this.gL("successfulOperation"),
                type: "success",
                customClass: this.$toastClass(),
                offset: 120
              });
              this.ruleForm = {
                code: "",
                password: "",
                confirm_passwords: ""
              };
              this.show = false;
            }
          });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.content-input {
  direction: ltr;
  padding: 40px 100px 20px 100px;
  .input-item {
    margin-bottom: 10px;
    display: flex;
    .send-btn {
      margin-inline-start: 20px;
      width: 200px;
    }
  }
}
.content-input-ug {
  div {
    input::-webkit-input-placeholder {
      text-align: right;
    }
  }
}
.btn {
  margin: 20px 100px;
  background: #139d59;
  color: #ffffff;
  font-size: 26px;
  text-align: center;
  padding: 15px 0;
  cursor: pointer;
}
</style>
