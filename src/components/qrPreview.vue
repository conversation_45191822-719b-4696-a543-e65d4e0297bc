<template>
  <div class="mask" v-if="shown">
    <div class="box">
      <div class="title">
        <span></span>
        <span>{{ title }}</span>
        <span class="iconfont icon-jia-copy" @click="cancel"></span>
      </div>
      <div class="content">
        <img :src="image + '?time=' + Date.now()" />
      </div>
    </div>
  </div>
</template>
<style scoped lang="less">
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
//s提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 620px;
    font-size: 30px;
    .title {
      align-items: center;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      background-color: #e6e6e6;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content {
      text-align: center;
      padding: 50px 30px 30px 30px;
      line-height: 1.5;
    }
  }
}
</style>

<script>
export default {
  data() {
    return {
      pwd: "",
    };
  },
  methods: {
    /**
     * 关掉模态框
     */
    cancel() {
      this.$emit("cancel");
    },
  },
  props: {
    shown: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "请用微信扫码",
    },
    image: {
      type: String,
      default: "",
    },
  },
};
</script>
