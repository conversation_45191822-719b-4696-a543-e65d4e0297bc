<template>
  <div class="table-example">
    <h2>自定义Table组件使用示例</h2>
    
    <!-- 基础用法 -->
    <div class="example-section">
      <h3>基础用法</h3>
      <CustomTable
        :data="tableData"
        :columns="columns"
        @row-click="handleRowClick"
        @cell-click="handleCellClick"
        @header-click="handleHeaderClick"
      />
    </div>
    
    <!-- 自定义样式 -->
    <div class="example-section">
      <h3>自定义样式</h3>
      <CustomTable
        :data="tableData"
        :columns="columns"
        :row-class="customRowClass"
        :cell-style="customCellStyle"
        :header-class="customHeaderClass"
        table-class="custom-table"
        table-style="{ border: '2px solid #409eff' }"
      />
    </div>
    
    <!-- 使用插槽 -->
    <div class="example-section">
      <h3>使用插槽</h3>
      <CustomTable
        :data="tableData"
        :columns="columns"
        :show-footer="true"
      >
        <!-- 自定义表头 -->
        <template #header-name="{ column }">
          <span style="color: #409eff;">{{ column.title }} *</span>
        </template>
        
        <!-- 自定义单元格 -->
        <template #cell-age="{ row, value }">
          <span :style="{ color: value > 25 ? '#67c23a' : '#f56c6c' }">
            {{ value }}岁
          </span>
        </template>
        
        <!-- 自定义表尾 -->
        <template #footer-salary="{ column }">
          <strong>总计: {{ getTotalSalary() }}</strong>
        </template>
      </CustomTable>
    </div>
    
    <!-- 继承所有table属性 -->
    <div class="example-section">
      <h3>继承所有table属性</h3>
      <CustomTable
        :data="tableData"
        :columns="columns"
        border="1"
        cellspacing="0"
        cellpadding="10"
        width="100%"
        align="center"
        bgcolor="#f0f0f0"
        class="inherited-table"
        style="margin: 20px 0;"
        @click="handleTableClick"
      />
    </div>
  </div>
</template>

<script>
import CustomTable from './table.vue';

export default {
  name: 'TableExample',
  components: {
    CustomTable
  },
  data() {
    return {
      tableData: [
        { id: 1, name: '张三', age: 25, salary: 8000, department: '技术部' },
        { id: 2, name: '李四', age: 30, salary: 12000, department: '销售部' },
        { id: 3, name: '王五', age: 28, salary: 10000, department: '技术部' },
        { id: 4, name: '赵六', age: 35, salary: 15000, department: '管理部' },
        { id: 5, name: '钱七', age: 22, salary: 6000, department: '技术部' }
      ],
      columns: [
        {
          key: 'name',
          title: '姓名',
          dataIndex: 'name',
          className: 'name-column',
          style: { fontWeight: 'bold' }
        },
        {
          key: 'age',
          title: '年龄',
          dataIndex: 'age',
          className: 'age-column',
          headerClassName: 'age-header',
          headerStyle: { textAlign: 'center' }
        },
        {
          key: 'salary',
          title: '薪资',
          dataIndex: 'salary',
          className: 'salary-column',
          footer: '薪资统计',
          footerClassName: 'salary-footer',
          footerStyle: { textAlign: 'right', fontWeight: 'bold' }
        },
        {
          key: 'department',
          title: '部门',
          dataIndex: 'department',
          className: 'department-column'
        }
      ]
    };
  },
  methods: {
    // 行点击事件
    handleRowClick(row, index) {
      console.log('行点击:', row, index);
      this.$message.success(`点击了第${index + 1}行: ${row.name}`);
    },
    
    // 单元格点击事件
    handleCellClick(row, column, rowIndex, colIndex) {
      console.log('单元格点击:', row, column, rowIndex, colIndex);
    },
    
    // 表头点击事件
    handleHeaderClick(column, index) {
      console.log('表头点击:', column, index);
      this.$message.info(`点击了表头: ${column.title}`);
    },
    
    // 表格点击事件
    handleTableClick(event) {
      console.log('表格点击:', event);
    },
    
    // 自定义行样式类
    customRowClass(row, index) {
      return {
        'high-salary': row.salary > 10000,
        'low-salary': row.salary < 8000,
        'even-row': index % 2 === 0
      };
    },
    
    // 自定义单元格样式
    customCellStyle(row, column, rowIndex, colIndex) {
      if (column.key === 'salary') {
        return {
          color: row.salary > 10000 ? '#67c23a' : '#606266',
          fontWeight: row.salary > 10000 ? 'bold' : 'normal'
        };
      }
      return {};
    },
    
    // 自定义表头样式类
    customHeaderClass(column) {
      return {
        'important-header': column.key === 'name',
        'numeric-header': column.key === 'age' || column.key === 'salary'
      };
    },
    
    // 计算总薪资
    getTotalSalary() {
      return this.tableData.reduce((sum, item) => sum + item.salary, 0);
    }
  }
};
</script>

<style scoped>
.table-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 40px;
}

.example-section h3 {
  color: #409eff;
  margin-bottom: 15px;
}

/* 自定义表格样式 */
.custom-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.custom-table .table-header {
  background-color: #409eff;
  color: white;
}

.custom-table .table-row:hover {
  background-color: #ecf5ff;
}

/* 继承的表格样式 */
.inherited-table {
  border: 2px solid #409eff;
  border-radius: 8px;
}

.inherited-table .table-header {
  background-color: #409eff;
  color: white;
}

/* 高薪资行样式 */
.high-salary {
  background-color: #f0f9ff;
}

/* 低薪资行样式 */
.low-salary {
  background-color: #fef0f0;
}

/* 偶数行样式 */
.even-row {
  background-color: #fafafa;
}

/* 重要表头样式 */
.important-header {
  background-color: #409eff !important;
  color: white !important;
}

/* 数字列表头样式 */
.numeric-header {
  text-align: center !important;
}

/* 薪资列样式 */
.salary-column {
  text-align: right;
}

/* 薪资表尾样式 */
.salary-footer {
  background-color: #409eff !important;
  color: white !important;
}
</style> 