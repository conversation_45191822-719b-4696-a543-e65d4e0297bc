import CustomTable from './table.vue';

// 导出组件
export default CustomTable;

// 导出组件名称
export { CustomTable };

// 导出组件配置
export const TableConfig = {
  name: 'CustomTable',
  component: CustomTable,
  install(Vue) {
    Vue.component('CustomTable', CustomTable);
  }
};

// 导出类型定义（用于TypeScript）
export const TableProps = {
  // 表格数据
  data: {
    type: Array,
    default: () => []
  },
  // 列配置
  columns: {
    type: Array,
    default: () => []
  },
  // 是否显示表头
  showHeader: {
    type: Boolean,
    default: true
  },
  // 是否显示表尾
  showFooter: {
    type: <PERSON>olean,
    default: false
  },
  // 行键值获取函数
  rowKey: {
    type: [String, Function],
    default: 'id'
  },
  // 表格样式类
  tableClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 表格样式
  tableStyle: {
    type: [String, Object],
    default: ''
  },
  // 行样式类函数
  rowClass: {
    type: Function,
    default: null
  },
  // 行样式函数
  rowStyle: {
    type: Function,
    default: null
  },
  // 单元格样式类函数
  cellClass: {
    type: Function,
    default: null
  },
  // 单元格样式函数
  cellStyle: {
    type: Function,
    default: null
  },
  // 表头样式类函数
  headerClass: {
    type: Function,
    default: null
  },
  // 表头样式函数
  headerStyle: {
    type: Function,
    default: null
  },
  // 表尾样式类函数
  footerClass: {
    type: Function,
    default: null
  },
  // 表尾样式函数
  footerStyle: {
    type: Function,
    default: null
  }
};

// 导出事件定义
export const TableEvents = {
  // 行点击事件
  'row-click': '(row, index)',
  // 行双击事件
  'row-dblclick': '(row, index)',
  // 单元格点击事件
  'cell-click': '(row, column, rowIndex, colIndex)',
  // 表头点击事件
  'header-click': '(column, index)'
};

// 导出插槽定义
export const TableSlots = {
  // 表头插槽
  'header-{key}': '{ column, index }',
  // 单元格插槽
  'cell-{key}': '{ row, column, rowIndex, colIndex, value }',
  // 表尾插槽
  'footer-{key}': '{ column, index }'
};

// 导出列配置类型
export const ColumnConfig = {
  key: 'String',           // 列键值
  title: 'String',         // 列标题
  dataIndex: 'String',     // 数据字段
  className: 'String',     // 列CSS类名
  style: 'Object',         // 列样式
  headerClassName: 'String', // 表头CSS类名
  headerStyle: 'Object',     // 表头样式
  footer: 'String',        // 表尾文本
  footerClassName: 'String', // 表尾CSS类名
  footerStyle: 'Object'      // 表尾样式
};

// 导出工具函数
export const TableUtils = {
  // 创建列配置
  createColumn(key, title, dataIndex, options = {}) {
    return {
      key,
      title,
      dataIndex,
      ...options
    };
  },
  
  // 创建数据行
  createRow(id, data) {
    return {
      id,
      ...data
    };
  },
  
  // 格式化列配置数组
  formatColumns(columns) {
    return columns.map((column, index) => ({
      key: column.key || `col_${index}`,
      title: column.title || column.label || `列${index + 1}`,
      dataIndex: column.dataIndex || column.key,
      ...column
    }));
  },
  
  // 验证表格数据
  validateData(data) {
    if (!Array.isArray(data)) {
      console.warn('Table data must be an array');
      return false;
    }
    return true;
  },
  
  // 验证列配置
  validateColumns(columns) {
    if (!Array.isArray(columns)) {
      console.warn('Table columns must be an array');
      return false;
    }
    return true;
  }
};

// 导出默认样式
export const DefaultStyles = {
  table: {
    width: '100%',
    borderCollapse: 'collapse',
    borderSpacing: 0
  },
  header: {
    backgroundColor: '#f5f7fa',
    color: '#606266',
    fontWeight: 500,
    textAlign: 'left',
    padding: '12px 0',
    borderBottom: '1px solid #ebeef5'
  },
  row: {
    borderBottom: '1px solid #ebeef5'
  },
  cell: {
    padding: '12px 0',
    borderBottom: '1px solid #ebeef5'
  },
  footer: {
    backgroundColor: '#fafafa',
    color: '#606266',
    fontWeight: 500,
    textAlign: 'left',
    padding: '12px 0',
    borderTop: '1px solid #ebeef5'
  }
};

// 导出常量
export const TableConstants = {
  // 默认CSS类名
  CSS_CLASSES: {
    TABLE_HEADER: 'table-header',
    TABLE_ROW: 'table-row',
    TABLE_CELL: 'table-cell',
    TABLE_FOOTER: 'table-footer'
  },
  
  // 默认事件名
  EVENTS: {
    ROW_CLICK: 'row-click',
    ROW_DBLCLICK: 'row-dblclick',
    CELL_CLICK: 'cell-click',
    HEADER_CLICK: 'header-click'
  },
  
  // 插槽前缀
  SLOT_PREFIXES: {
    HEADER: 'header-',
    CELL: 'cell-',
    FOOTER: 'footer-'
  }
}; 