<template>
  <div class="test-container">
    <h1>Table组件测试</h1>
    
    <!-- 基础测试 -->
    <div class="test-section">
      <h2>基础功能测试</h2>
      <CustomTable
        :data="testData"
        :columns="testColumns"
        @row-click="onRowClick"
        @cell-click="onCellClick"
        @header-click="onHeaderClick"
      />
    </div>
    
    <!-- 样式测试 -->
    <div class="test-section">
      <h2>样式功能测试</h2>
      <CustomTable
        :data="testData"
        :columns="testColumns"
        :row-class="testRowClass"
        :cell-style="testCellStyle"
        table-class="test-table"
        border="1"
        cellspacing="0"
        cellpadding="8"
        width="100%"
      />
    </div>
    
    <!-- 插槽测试 -->
    <div class="test-section">
      <h2>插槽功能测试</h2>
      <CustomTable
        :data="testData"
        :columns="testColumns"
        :show-footer="true"
      >
        <template #header-name="{ column }">
          <span style="color: red;">{{ column.title }} 🔥</span>
        </template>
        <template #cell-age="{ value }">
          <span :style="{ color: value > 25 ? 'green' : 'orange' }">
            {{ value }}岁
          </span>
        </template>
        <template #footer-salary="{ column }">
          <strong>总薪资: {{ totalSalary }}</strong>
        </template>
      </CustomTable>
    </div>
    
    <!-- 事件测试结果 -->
    <div class="test-section">
      <h2>事件测试结果</h2>
      <div class="event-log">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomTable from './table.vue';

export default {
  name: 'TableTest',
  components: {
    CustomTable
  },
  data() {
    return {
      testData: [
        { id: 1, name: '测试用户1', age: 25, salary: 8000, department: '技术部' },
        { id: 2, name: '测试用户2', age: 30, salary: 12000, department: '销售部' },
        { id: 3, name: '测试用户3', age: 22, salary: 6000, department: '技术部' },
        { id: 4, name: '测试用户4', age: 35, salary: 15000, department: '管理部' }
      ],
      testColumns: [
        {
          key: 'name',
          title: '姓名',
          dataIndex: 'name',
          className: 'name-column'
        },
        {
          key: 'age',
          title: '年龄',
          dataIndex: 'age',
          className: 'age-column',
          headerStyle: { textAlign: 'center' }
        },
        {
          key: 'salary',
          title: '薪资',
          dataIndex: 'salary',
          className: 'salary-column',
          footer: '薪资统计',
          footerStyle: { textAlign: 'right', fontWeight: 'bold' }
        },
        {
          key: 'department',
          title: '部门',
          dataIndex: 'department',
          className: 'department-column'
        }
      ],
      eventLogs: []
    };
  },
  computed: {
    totalSalary() {
      return this.testData.reduce((sum, item) => sum + item.salary, 0);
    }
  },
  methods: {
    // 行点击事件
    onRowClick(row, index) {
      this.addLog(`行点击: ${row.name} (索引: ${index})`);
    },
    
    // 单元格点击事件
    onCellClick(row, column, rowIndex, colIndex) {
      this.addLog(`单元格点击: ${row.name} - ${column.title} (${rowIndex}, ${colIndex})`);
    },
    
    // 表头点击事件
    onHeaderClick(column, index) {
      this.addLog(`表头点击: ${column.title} (索引: ${index})`);
    },
    
    // 测试行样式类
    testRowClass(row, index) {
      return {
        'high-salary': row.salary > 10000,
        'low-salary': row.salary < 8000,
        'even-row': index % 2 === 0
      };
    },
    
    // 测试单元格样式
    testCellStyle(row, column, rowIndex, colIndex) {
      if (column.key === 'salary') {
        return {
          color: row.salary > 10000 ? '#67c23a' : '#606266',
          fontWeight: row.salary > 10000 ? 'bold' : 'normal'
        };
      }
      return {};
    },
    
    // 添加日志
    addLog(message) {
      this.eventLogs.unshift({
        message,
        time: new Date().toLocaleTimeString()
      });
      
      // 限制日志数量
      if (this.eventLogs.length > 10) {
        this.eventLogs = this.eventLogs.slice(0, 10);
      }
    }
  }
};
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.test-section h2 {
  color: #409eff;
  margin-bottom: 15px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

/* 测试表格样式 */
.test-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.test-table .table-header {
  background-color: #409eff;
  color: white;
}

.test-table .table-row:hover {
  background-color: #ecf5ff;
}

/* 高薪资行样式 */
.high-salary {
  background-color: #f0f9ff;
}

/* 低薪资行样式 */
.low-salary {
  background-color: #fef0f0;
}

/* 偶数行样式 */
.even-row {
  background-color: #fafafa;
}

/* 薪资列样式 */
.salary-column {
  text-align: right;
}

/* 事件日志样式 */
.event-log {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.log-item {
  padding: 5px 0;
  border-bottom: 1px solid #e4e7ed;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item::before {
  content: '[' attr(data-time) '] ';
  color: #909399;
  font-weight: bold;
}
</style> 