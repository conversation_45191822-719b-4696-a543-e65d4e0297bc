<template>
  <table v-bind="$attrs" v-on="$listeners" :class="tableClass" :style="tableStyle">
    <thead v-if="showHeader">
      <tr>
        <th v-for="(column, index) in columns" :key="index" :class="getHeaderClass(column)"
          :style="getHeaderStyle(column)" @click="handleHeaderClick(column, index)">
          <slot :name="`header-${column.key || index}`" :column="column" :index="index">
            {{ column.title || column.label }}
          </slot>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(row, rowIndex) in data" :key="getRowKey(row, rowIndex)" :class="getRowClass(row, rowIndex)"
        :style="getRowStyle(row, rowIndex)" @click="handleRowClick(row, rowIndex)"
        @dblclick="handleRowDblClick(row, rowIndex)">
        <td v-for="(column, colIndex) in columns" :key="colIndex" :class="getCellClass(row, column, rowIndex, colIndex)"
          :style="getCellStyle(row, column, rowIndex, colIndex)"
          @click="handleCellClick(row, column, rowIndex, colIndex)">
          <slot :name="`cell-${column.key || colIndex}`" :row="row" :column="column" :rowIndex="rowIndex"
            :colIndex="colIndex" :value="getCellValue(row, column)">
            {{ getCellValue(row, column) }}
          </slot>
        </td>
      </tr>
    </tbody>
    <tfoot v-if="showFooter">
      <tr>
        <td v-for="(column, index) in columns" :key="index" :class="getFooterClass(column)"
          :style="getFooterStyle(column)">
          <slot :name="`footer-${column.key || index}`" :column="column" :index="index">
            {{ column.footer }}
          </slot>
        </td>
      </tr>
    </tfoot>
  </table>
</template>

<script>
export default {
  name: 'CustomTable',
  inheritAttrs: false,
  props: {
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },
    // 列配置
    columns: {
      type: Array,
      default: () => []
    },
    // 是否显示表头
    showHeader: {
      type: Boolean,
      default: true
    },
    // 是否显示表尾
    showFooter: {
      type: Boolean,
      default: false
    },
    // 行键值获取函数
    rowKey: {
      type: [String, Function],
      default: 'id'
    },
    // 表格样式类
    tableClass: {
      type: [String, Array, Object],
      default: ''
    },
    // 表格样式
    tableStyle: {
      type: [String, Object],
      default: ''
    },
    // 行样式类函数
    rowClass: {
      type: Function,
      default: null
    },
    // 行样式函数
    rowStyle: {
      type: Function,
      default: null
    },
    // 单元格样式类函数
    cellClass: {
      type: Function,
      default: null
    },
    // 单元格样式函数
    cellStyle: {
      type: Function,
      default: null
    },
    // 表头样式类函数
    headerClass: {
      type: Function,
      default: null
    },
    // 表头样式函数
    headerStyle: {
      type: Function,
      default: null
    },
    // 表尾样式类函数
    footerClass: {
      type: Function,
      default: null
    },
    // 表尾样式函数
    footerStyle: {
      type: Function,
      default: null
    }
  },
  computed: {
    // 计算表格的CSS类
    computedTableClass() {
      if (typeof this.tableClass === 'string') {
        return this.tableClass;
      } else if (Array.isArray(this.tableClass)) {
        return this.tableClass.join(' ');
      } else if (typeof this.tableClass === 'object') {
        return Object.keys(this.tableClass)
          .filter(key => this.tableClass[key])
          .join(' ');
      }
      return '';
    }
  },
  methods: {
    // 获取行键值
    getRowKey(row, index) {
      if (typeof this.rowKey === 'function') {
        return this.rowKey(row, index);
      } else if (typeof this.rowKey === 'string') {
        return row[this.rowKey] || index;
      }
      return index;
    },

    // 获取单元格值
    getCellValue(row, column) {
      if (column.key) {
        return row[column.key];
      } else if (column.dataIndex) {
        return row[column.dataIndex];
      }
      return '';
    },

    // 获取行样式类
    getRowClass(row, index) {
      let classes = [];

      // 添加默认行样式类
      classes.push('table-row');

      // 添加用户自定义行样式类
      if (this.rowClass) {
        const customClass = this.rowClass(row, index);
        if (customClass) {
          if (typeof customClass === 'string') {
            classes.push(customClass);
          } else if (Array.isArray(customClass)) {
            classes = classes.concat(customClass);
          } else if (typeof customClass === 'object') {
            classes = classes.concat(
              Object.keys(customClass).filter(key => customClass[key])
            );
          }
        }
      }

      return classes.join(' ');
    },

    // 获取行样式
    getRowStyle(row, index) {
      if (this.rowStyle) {
        return this.rowStyle(row, index);
      }
      return '';
    },

    // 获取单元格样式类
    getCellClass(row, column, rowIndex, colIndex) {
      let classes = ['table-cell'];

      // 添加列特定的样式类
      if (column.className) {
        classes.push(column.className);
      }

      // 添加用户自定义单元格样式类
      if (this.cellClass) {
        const customClass = this.cellClass(row, column, rowIndex, colIndex);
        if (customClass) {
          if (typeof customClass === 'string') {
            classes.push(customClass);
          } else if (Array.isArray(customClass)) {
            classes = classes.concat(customClass);
          } else if (typeof customClass === 'object') {
            classes = classes.concat(
              Object.keys(customClass).filter(key => customClass[key])
            );
          }
        }
      }

      return classes.join(' ');
    },

    // 获取单元格样式
    getCellStyle(row, column, rowIndex, colIndex) {
      let style = {};

      // 合并列样式
      if (column.style) {
        Object.assign(style, column.style);
      }

      // 合并用户自定义单元格样式
      if (this.cellStyle) {
        const customStyle = this.cellStyle(row, column, rowIndex, colIndex);
        if (customStyle) {
          Object.assign(style, customStyle);
        }
      }

      return style;
    },

    // 获取表头样式类
    getHeaderClass(column) {
      let classes = ['table-header'];

      if (column.headerClassName) {
        classes.push(column.headerClassName);
      }

      if (this.headerClass) {
        const customClass = this.headerClass(column);
        if (customClass) {
          if (typeof customClass === 'string') {
            classes.push(customClass);
          } else if (Array.isArray(customClass)) {
            classes = classes.concat(customClass);
          } else if (typeof customClass === 'object') {
            classes = classes.concat(
              Object.keys(customClass).filter(key => customClass[key])
            );
          }
        }
      }

      return classes.join(' ');
    },

    // 获取表头样式
    getHeaderStyle(column) {
      let style = {};

      if (column.headerStyle) {
        Object.assign(style, column.headerStyle);
      }

      if (this.headerStyle) {
        const customStyle = this.headerStyle(column);
        if (customStyle) {
          Object.assign(style, customStyle);
        }
      }

      return style;
    },

    // 获取表尾样式类
    getFooterClass(column) {
      let classes = ['table-footer'];

      if (column.footerClassName) {
        classes.push(column.footerClassName);
      }

      if (this.footerClass) {
        const customClass = this.footerClass(column);
        if (customClass) {
          if (typeof customClass === 'string') {
            classes.push(customClass);
          } else if (Array.isArray(customClass)) {
            classes = classes.concat(customClass);
          } else if (typeof customClass === 'object') {
            classes = classes.concat(
              Object.keys(customClass).filter(key => customClass[key])
            );
          }
        }
      }

      return classes.join(' ');
    },

    // 获取表尾样式
    getFooterStyle(column) {
      let style = {};

      if (column.footerStyle) {
        Object.assign(style, column.footerStyle);
      }

      if (this.footerStyle) {
        const customStyle = this.footerStyle(column);
        if (customStyle) {
          Object.assign(style, customStyle);
        }
      }

      return style;
    },

    // 处理行点击事件
    handleRowClick(row, index) {
      this.$emit('row-click', row, index);
    },

    // 处理行双击事件
    handleRowDblClick(row, index) {
      this.$emit('row-dblclick', row, index);
    },

    // 处理单元格点击事件
    handleCellClick(row, column, rowIndex, colIndex) {
      this.$emit('cell-click', row, column, rowIndex, colIndex);
    },

    // 处理表头点击事件
    handleHeaderClick(column, index) {
      this.$emit('header-click', column, index);
    }
  }
};
</script>

<style lang="less" scoped>
/* 基础表格样式 */
table {
  width: 100%;

  thead {
    background: rgba(242, 242, 242, 1);
    tr {
      th {
        padding: 0 15px;
        height: 50px;
        line-height: 50px;
        font-weight: bold;
        font-size: 22px;
        color: #6A6A6A;
      }
    }
  }

  tbody {
    tr {

      td {
        text-align: center;
        border-bottom: 1px dashed #D9D9D9 !important;
        height: 50px;
        line-height: 50px;
        font-size: 22px;
      }
    }
  }
}
</style>
