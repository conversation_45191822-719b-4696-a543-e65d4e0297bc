# 自定义Table组件

这是一个纯原生的Vue2 table组件，支持所有原生table属性的继承，具有高度的可定制性。

## 特性

- ✅ 继承所有原生table属性
- ✅ 支持自定义样式和类名
- ✅ 支持插槽自定义内容
- ✅ 支持事件处理
- ✅ 支持表头、表尾自定义
- ✅ 支持行、单元格样式函数
- ✅ 响应式设计
- ✅ 斑马纹效果
- ✅ 悬停效果

## 基本用法

```vue
<template>
  <CustomTable
    :data="tableData"
    :columns="columns"
    @row-click="handleRowClick"
  />
</template>

<script>
import CustomTable from './table.vue';

export default {
  components: {
    CustomTable
  },
  data() {
    return {
      tableData: [
        { id: 1, name: '张三', age: 25, salary: 8000 },
        { id: 2, name: '李四', age: 30, salary: 12000 }
      ],
      columns: [
        { key: 'name', title: '姓名', dataIndex: 'name' },
        { key: 'age', title: '年龄', dataIndex: 'age' },
        { key: 'salary', title: '薪资', dataIndex: 'salary' }
      ]
    };
  },
  methods: {
    handleRowClick(row, index) {
      console.log('点击行:', row, index);
    }
  }
};
</script>
```

## Props

### data
- **类型**: `Array`
- **默认值**: `[]`
- **说明**: 表格数据数组

### columns
- **类型**: `Array`
- **默认值**: `[]`
- **说明**: 列配置数组

### showHeader
- **类型**: `Boolean`
- **默认值**: `true`
- **说明**: 是否显示表头

### showFooter
- **类型**: `Boolean`
- **默认值**: `false`
- **说明**: 是否显示表尾

### rowKey
- **类型**: `String | Function`
- **默认值**: `'id'`
- **说明**: 行键值获取方式

### tableClass
- **类型**: `String | Array | Object`
- **默认值**: `''`
- **说明**: 表格CSS类名

### tableStyle
- **类型**: `String | Object`
- **默认值**: `''`
- **说明**: 表格内联样式

### rowClass
- **类型**: `Function`
- **默认值**: `null`
- **说明**: 行样式类函数

### rowStyle
- **类型**: `Function`
- **默认值**: `null`
- **说明**: 行样式函数

### cellClass
- **类型**: `Function`
- **默认值**: `null`
- **说明**: 单元格样式类函数

### cellStyle
- **类型**: `Function`
- **默认值**: `null`
- **说明**: 单元格样式函数

### headerClass
- **类型**: `Function`
- **默认值**: `null`
- **说明**: 表头样式类函数

### headerStyle
- **类型**: `Function`
- **默认值**: `null`
- **说明**: 表头样式函数

### footerClass
- **类型**: `Function`
- **默认值**: `null`
- **说明**: 表尾样式类函数

### footerStyle
- **类型**: `Function`
- **默认值**: `null`
- **说明**: 表尾样式函数

## Events

### row-click
- **参数**: `(row, index)`
- **说明**: 行点击事件

### row-dblclick
- **参数**: `(row, index)`
- **说明**: 行双击事件

### cell-click
- **参数**: `(row, column, rowIndex, colIndex)`
- **说明**: 单元格点击事件

### header-click
- **参数**: `(column, index)`
- **说明**: 表头点击事件

## Slots

### header-{key}
- **作用域**: `{ column, index }`
- **说明**: 自定义表头内容

### cell-{key}
- **作用域**: `{ row, column, rowIndex, colIndex, value }`
- **说明**: 自定义单元格内容

### footer-{key}
- **作用域**: `{ column, index }`
- **说明**: 自定义表尾内容

## 列配置 (columns)

```javascript
{
  key: 'name',           // 列键值
  title: '姓名',         // 列标题
  dataIndex: 'name',     // 数据字段
  className: 'name-col', // 列CSS类名
  style: {               // 列样式
    fontWeight: 'bold'
  },
  headerClassName: 'header-col', // 表头CSS类名
  headerStyle: {                 // 表头样式
    textAlign: 'center'
  },
  footer: '统计',        // 表尾文本
  footerClassName: 'footer-col', // 表尾CSS类名
  footerStyle: {                 // 表尾样式
    textAlign: 'right'
  }
}
```

## 样式函数示例

### 行样式类函数
```javascript
rowClass(row, index) {
  return {
    'high-salary': row.salary > 10000,
    'low-salary': row.salary < 8000,
    'even-row': index % 2 === 0
  };
}
```

### 单元格样式函数
```javascript
cellStyle(row, column, rowIndex, colIndex) {
  if (column.key === 'salary') {
    return {
      color: row.salary > 10000 ? '#67c23a' : '#606266',
      fontWeight: row.salary > 10000 ? 'bold' : 'normal'
    };
  }
  return {};
}
```

## 继承原生table属性

组件会自动继承所有原生table属性，包括但不限于：

- `border`
- `cellspacing`
- `cellpadding`
- `width`
- `height`
- `align`
- `bgcolor`
- `class`
- `style`
- `id`
- `title`
- 等等...

## 完整示例

```vue
<template>
  <CustomTable
    :data="tableData"
    :columns="columns"
    :row-class="customRowClass"
    :cell-style="customCellStyle"
    :show-footer="true"
    border="1"
    cellspacing="0"
    cellpadding="10"
    width="100%"
    class="my-table"
    @row-click="handleRowClick"
  >
    <!-- 自定义表头 -->
    <template #header-name="{ column }">
      <span style="color: #409eff;">{{ column.title }} *</span>
    </template>
    
    <!-- 自定义单元格 -->
    <template #cell-age="{ row, value }">
      <span :style="{ color: value > 25 ? '#67c23a' : '#f56c6c' }">
        {{ value }}岁
      </span>
    </template>
    
    <!-- 自定义表尾 -->
    <template #footer-salary="{ column }">
      <strong>总计: {{ getTotalSalary() }}</strong>
    </template>
  </CustomTable>
</template>
```

## CSS类名

组件会自动添加以下CSS类名：

- `.table-header` - 表头
- `.table-row` - 表格行
- `.table-cell` - 表格单元格
- `.table-footer` - 表尾

这些类名可以用于自定义样式。

## 注意事项

1. 组件使用 `inheritAttrs: false` 来确保原生属性正确传递
2. 所有事件都会通过 `$listeners` 传递给原生table元素
3. 样式函数应该返回对象或字符串，数组会被自动处理
4. 插槽名称格式为 `header-{key}`、`cell-{key}`、`footer-{key}`
5. 列配置中的 `key` 字段用于插槽名称，如果不存在则使用索引 