<template>
  <div>
    <div class="mask" v-if="number_box">
      <div class="box" id="box">
        <div class="title">
          <span></span>
          <span>{{
            food_data.food_format_id == 1 ? gL("setLimits") : gL("kilo")
          }}</span>
          <span class="cancel iconfont icon-jia-copy" @click="cancel"></span>
        </div>
        <div class="num">
          <div
            class="input"
            :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
          >
            <!-- <div class="number">{{num_text}}</div> -->
            <input
              class="number"
              :placeholder="gL('limitsCount')"
              v-model="num_text"
              @keyup.enter="clickOpen"
              oninput="if(value.length>2)value=value.slice(0,2)"
            />
            <div
              class="text"
              :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
            >
              {{ food_name }}
            </div>
          </div>
          <div class="num-list">
            <div class="nbox">
              <div class="num-item" @click="clickNum('1')">1</div>
              <div class="num-item" @click="clickNum('2')">2</div>
              <div class="num-item" @click="clickNum('3')">3</div>
              <div class="num-item" @click="clickNum('4')">4</div>
              <div class="num-item" @click="clickNum('5')">5</div>
              <div class="num-item" @click="clickNum('6')">6</div>
              <div class="num-item" @click="clickNum('7')">7</div>
              <div class="num-item" @click="clickNum('8')">8</div>
              <div class="num-item" @click="clickNum('9')">9</div>
              <div class="num-item">
                <div
                  v-if="food_data.food_format_id == 2"
                  @click="clickNum('.')"
                >
                  .
                </div>
              </div>
              <div class="num-item" @click="clickNum('0')">0</div>
              <div class="num-item" @click="removeNum">
                <span class="iconfont icon-tuige"></span>
              </div>
            </div>
          </div>
          <div class="btn" @click="clickOpen">
            {{ gL("confirm") }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
//数字键盘提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 600px;
    font-size: 30px;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .input {
      display: flex;
      justify-content: space-between;
      padding: 20px 75px 0 75px;
      align-items: center;
      .number {
        width: 115px;
        border-bottom: 2px solid @textColor;
        text-align: center;
        font-size: 22px;
        color: @greenColor;
        padding-bottom: 10px;
        outline: none;
      }
      .text {
        width: 70%;
        white-space: normal;
        text-overflow: ellipsis;
        padding-bottom: 10px;
        font-size: 23px;
      }
    }
    .num-list {
      padding: 20px 75px 0 75px;
      .nbox {
        border-top: 1px solid #cccccc;
        border-left: 1px solid #cccccc;
        display: flex;
        flex-wrap: wrap;
      }
      .num-item {
        width: 33.3%;
        text-align: center;
        height: 85px;
        line-height: 85px;
        font-weight: bold;
        cursor: pointer;
        border-right: 1px solid #cccccc;
        border-bottom: 1px solid #cccccc;
        .iconfont {
          font-size: 26px;
          color: #666666;
        }
      }
    }
    .btn {
      margin: 20px 75px 20px 75px;
      background: @greenColor;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
  }
}
//动画
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}
@-webkit-keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
  }
}
/*弹层动画（从上往下）*/
@keyframes fadeInDown {
  0% {
    -webkit-transform: translate3d(0, -20%, 0);
    -webkit-transform: translate3d(0, -20%, 0);
    transform: translate3d(0, -20%, 0);
    transform: translate3d(0, -20%, 0);
    opacity: 0;
  }
  100% {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}

@-webkit-keyframes fadeInDown {
  0% {
    -webkit-transform: translate3d(0, -20%, 0);
    opacity: 0;
  }
  100% {
    -webkit-transform: none;
    opacity: 1;
  }
}
.slip-down {
  animation: fadeInDown 0.5s;
}
</style>

<script>
import { postSellClearSetCountAPI } from "./../api/index.js"
var self;

export default {
  data() {
    return {
      num_text: "", //新客数量
      food_name: "",
      food_data: "",
    };
  },
  methods: {
    /**
     * 点击弹出的数字
     */
    clickNum(e) {
      var num = 6;
      if (this.food_data.food_format_id == 2) {
        num = 3;
        if (
          (this.num_text.length == 0 && e == ".") ||
          (this.num_text.indexOf(".") != -1 && e == ".")
        )
          return;
      }
      if (this.num_text.length < 4) {
        if (this.num_text == "0" && e != ".") {
          this.num_text = e;
        } else {
          if (this.num_text == "0" && e == 0) return;
          this.num_text = this.num_text + e;
        }
      }
      if (this.food_data.food_format_id == 2) {
        this.num_text = this.num_text.replace(
          /^(\-)*(\d+)\.(\d\d).*$/,
          "$1$2.$3"
        );
      }
    },
    /**
     * 删除数字
     */
    removeNum(e) {
      if (this.num_text.length > 1) {
        this.num_text = this.num_text.substr(0, this.num_text.length - 1);
      } else {
        this.num_text = "";
      }
    },
    /**
     * 关掉模态框
     */
    cancel(e) {
      this.$emit("cancel");
      this.num_text = "";
      let down = document.querySelector('#box')
      down.classList.add('slip-down')
    },
    /**
     * 父件来的值
     * e=估清数量
     * s=美食名称
     * d=美食数据
     */
    openBox(e, s, d) {
      this.food_name = s;
      this.food_data = d;
      if (e != "") {
        var num = Number(e);
        this.num_text = num.toString();
      }
    },
    /**
     * 点击餐桌信息
     */
    clickOpen(e) {
      // 沽清不能为0
      if (this.num_text != "" && this.num_text >= 0) {
        postSellClearSetCountAPI({
          food_id: this.food_id,
          sell_clear_count: this.num_text,
        })
          .then((response) => {
            if (response.status >= 200 && response.status < 300) {
              this.num_text = "";
              this.$emit("cancel");
            }
          });
      } else if (this.num_text < 0) {
        this.$message({
          message: this.gL("limitInputGreaterZero"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
      } else {
        this.$message({
          message: this.gL("inputLimits"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
      }
    },
  },
  props: {
    number_box: {
      type: Boolean,
      default: false,
    },
    food_id: {
      default: 0,
    },
  },
};
</script>
