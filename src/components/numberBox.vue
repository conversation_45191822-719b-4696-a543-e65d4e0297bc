<template>
    <div class="mask" v-if="number_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ gL("newGuest") }}</span>
          <span class="iconfont icon-jia-copy" @click="cancel"></span>
        </div>
        <div class="num">
          <div class="input">
            <div class="number">
              <input
                v-model="num_text"
                type="number"
                oninput="if(value.length>2)value=value.slice(0,2)"
                @keyup.enter="clickOpen"
                :placeholder="gL('pepoleCount')"
              />
            </div>
          </div>
          <div class="num-list">
            <div class="nbox">
              <div class="num-item" @click="clickNum('1')">1</div>
              <div class="num-item" @click="clickNum('2')">2</div>
              <div class="num-item" @click="clickNum('3')">3</div>
              <div class="num-item" @click="clickNum('4')">4</div>
              <div class="num-item" @click="clickNum('5')">5</div>
              <div class="num-item" @click="clickNum('6')">6</div>
              <div class="num-item" @click="clickNum('7')">7</div>
              <div class="num-item" @click="clickNum('8')">8</div>
              <div class="num-item" @click="clickNum('9')">9</div>
              <div class="num-item"></div>
              <div class="num-item" @click="clickNum('0')">0</div>
              <div class="num-item" @click="removeNum">
                <span class="iconfont icon-tuige"></span>
              </div>
            </div>
          </div>
          <div class="btn">
            <!-- {{gL('confirm')}} -->
            <el-button
              class="button"
              @click="clickOpen"
              type="primary"
              :loading="load"
              >{{ gL("confirm") }}</el-button
            >
          </div>
        </div>
      </div>
    </div>
</template>

<style scoped lang="less">
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
//数字键盘提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 600px;
    font-size: 30px;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .input {
      display: flex;
      justify-content: center;
      .number {
        width: 150px;
        border-bottom: 2px solid @textColor;
        text-align: center;
        padding: 20px 0;
        input {
          font-weight: bold;
          font-size: 30px;
          color: @greenColor;
          width: 100%;
          text-align: center;
          outline: none;
        }
      }
    }
    .num-list {
      padding: 20px 75px 0 75px;
      .nbox {
        border-top: 1px solid #cccccc;
        border-left: 1px solid #cccccc;
        display: flex;
        flex-wrap: wrap;
      }
      .num-item {
        width: 33.3%;
        text-align: center;
        padding: 27px 0;
        font-weight: bold;
        cursor: pointer;
        border-right: 1px solid #cccccc;
        border-bottom: 1px solid #cccccc;
        .iconfont {
          font-size: 26px;
          color: #666666;
        }
      }
    }
    .btn {
      margin: 20px 75px 20px 75px;
      // background: @greenColor;
      color: #ffffff;
      text-align: center;
      .button {
        font-size: 22px;
        cursor: pointer;
        padding: 15px 0;
        width: 100%;
      }
    }
  }
}
/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

@-webkit-keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
  }
}
</style>

<script>
import { postOrdersAPI } from "./../api/index.js"
var self;
export default {
  data() {
    return {
      num_box: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "", "0"],
      num_text: "2", //新客数量
      load: false, //新客数量
      isOneClick: true,
    };
  },
  activated() {
    this.num_text = "2";
  },
  watch: {
    number_box(value) {
      if (value) {
        this.isOneClick = true;
      }
    }
  },
  methods: {
    /**
     * 点击弹出的数字
     */
    clickNum(e) {
      console.log(e);
      if (this.num_text.length < 2) {
        if ((this.isOneClick && this.num_text == "2") || this.num_text == "0") {
          if (e != 0) {
            this.num_text = e;
            this.isOneClick = false;
          }
        } else {
          this.num_text = this.num_text + e;
        }
      }
    },
    /**
     * 删除数字
     */
    removeNum(e) {
      if (this.num_text.length > 1) {
        this.num_text = this.num_text.substr(0, this.num_text.length - 1);
      } else {
        this.num_text = "0";
      }
    },
    /**
     * 关掉模态框
     */
    cancel(e) {
      this.num_text = "2";
      this.$emit("cancel", 1);
    },
    /**
     * 点击餐桌信息
     */
    clickOpen(e) {
      if (this.num_text > 0) {
        this.load = true;
        postOrdersAPI({
          table_id: parseInt(this.table_id),
          customers_count: parseInt(this.num_text),
        }).then((response) => {
            if (response.status >= 200 && response.status < 300) {
              this.num_text = "";
              this.$emit("cancel", 2, response.data.data.order_id);

              this.$router.push({
                name: "foodList",
                params: { id: this.table_id },
                query: { id: response.data.data.order_id },
              });
            }
          }).finally(() => {
            this.load = false;
          });
      } else {
        this.$message({
          message: this.gL("pepoleErrot"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
      }
    },
  },
  props: {
    number_box: {
      type: Boolean,
      default: false,
    },
    table_id: {
      default: 0,
    },
  },
};

</script>
