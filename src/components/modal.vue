<template>
    <div class="mask" v-if="number_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ modal_title }}</span>
          <span class="iconfont icon-jia-copy" @click="cancel"></span>
        </div>
        <div class="content" :style="{ direction: gLang == 1 ? 'rtl' : 'ltr' }">
          {{ modal_content }}
        </div>
        <div class="btn" @click="confirm">
          {{ gL("confirm") }}
        </div>
      </div>
    </div>
</template>

<style scoped lang="less">
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
//s提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 620px;
    font-size: 30px;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .title {
      align-items: center;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      background-color: #e6e6e6;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content {
      text-align: center;
      padding: 50px 30px 30px 30px;
      line-height: 1.5;
    }
    .btn {
      margin: 20px 75px 20px 75px;
      background: @greenColor;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
  }
}
//动画
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}
@-webkit-keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
  }
}
/*弹层动画（从上往下）*/
@keyframes fadeInDown {
  0% {
    -webkit-transform: translate3d(0, -20%, 0);
    -webkit-transform: translate3d(0, -20%, 0);
    transform: translate3d(0, -20%, 0);
    transform: translate3d(0, -20%, 0);
    opacity: 0;
  }
  100% {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}

@-webkit-keyframes fadeInDown {
  0% {
    -webkit-transform: translate3d(0, -20%, 0);
    opacity: 0;
  }
  100% {
    -webkit-transform: none;
    opacity: 1;
  }
}
.slip-down {
  animation: fadeInDown 0.5s;
}
</style>

<script>
export default {
  data() {
    return {
      gLang: 1,
    };
  },
  created() {
    this.gLang = localStorage.getItem("langId");
  },
  methods: {
    /**
     * 关掉模态框
     */
    cancel() {
      this.$emit("cancel");
    },
    /**
     * 点击确定
     */
    confirm() {
      this.$emit("confirm");
    },
  },
  props: {
    number_box: {
      type: Boolean,
      default: false,
    },
    modal_content: {
      type: String,
      default: "确定吗？",
    },
    modal_title: {
      type: String,
      default: "提示",
    },
    table_id: {
      default: 0,
    },
  },
};
</script>
