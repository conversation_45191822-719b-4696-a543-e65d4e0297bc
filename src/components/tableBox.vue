<template>
  <div class="wrap">
    <!-- 菜单和搜索框 -->
    <div class="top">
      <div class="menu">
        <el-tabs
          v-model="activeName"
          type="card"
          class="menu-item"
          @tab-click="clickMenu"
        >
          <el-tab-pane
            v-for="(item, index) in tables"
            :key="item.id"
            :label="item.name"
            :name="item.item"
            :id="item.id"
          >
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="serach">
        <div class="input">
          <span class="iconfont icon-search search"></span>
          <input
            type="text"
            :placeholder="gL('tableNumber')"
            v-model.trim="serachText"
            v-on:input="inputSearch"
            oninput="if(value.length>8)value=value.slice(0,8)"
          />
        </div>
        <div class="del" v-if="delete_text" @click="removeSerach">
          <span class="iconfont icon-jia-copy"></span>
        </div>
      </div>
    </div>
    <!-- 餐桌 -->
    <div class="table">
      <div class="box">
        <div
          class="table-item"
          v-for="(item, index) in table_list"
          :key="index"
          :class="item.active ? 'active' : 'asd'"
          @click="showNumberBox(item)"
        >
          <div
            class="table-num"
            :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }"
          >
            {{ gLang == 1 ? item.name_ug : item.name_zh }}
          </div>
          <div class="table-img">
            <span
              class="iconfont icon-zhuozi"
              v-if="item.table_status == 1"
            ></span>
            <span
              class="iconfont icon-zhuozi green"
              v-if="item.table_status == 2"
            ></span>
            <span
              class="iconfont icon-wodedingdan zhang"
              v-if="item.table_status == 3"
            ></span>
          </div>
          <div class="table-place">
            <span>{{ item.customers_count }}</span>
            <span>/</span>
            <span>{{ item.seating_capacity }}</span>
          </div>
        </div>
        <div class="empty" v-if="table_list.length == 0">
          {{ gL("noMessage") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getAreasListAPI,
  getTablesListAPI,
  getCollageOrderTablesAPI
} from "./../api/index.js";
var self, str;
export default {
  created: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
    str = "private-" + storeInfo.merchant_no + ":App\\Events\\TableStatus";
    // this.sockets.subscribe(str, (data) => {
    //   this.getTableList();
    // })
    //获取区域
    self.getList();
    self.getTableList();
  },
  data() {
    return {
      gLang: 1,
      tables: [],
      delete_text: false, //删除搜索框内容按钮
      serachText: "", //搜索内容
      active_menu: "", //选中的区或楼
      table_list: [], //列表
      table_status: "", //餐桌状态
      table_info: {}, //餐桌信息
      table_info_id: 0, //餐桌信息ID
      order_info: 0, //订单总额和数量
      activeName: "first"
    };
  },
  methods: {
    //搜索输入框输入时候
    inputSearch() {
      setTimeout(() => {
        this.getTableList();
      }, 300);
      if (this.serachText.length != 0) {
        this.delete_text = true;
      } else {
        this.delete_text = false;
      }
    },
    //删除搜索内容
    removeSerach() {
      this.serachText = "";
      this.delete_text = false;
      this.getTableList();
    },
    //选择Top菜单
    clickMenu(tab, event) {
      this.active_menu = tab.$attrs.id;
      this.getTableList();
    },
    /**
     * 点击餐桌
     */
    showNumberBox(e) {
      self.table_list.forEach(item => {
        self.$set(item, "active", false);
      });
      if (self.table_state == 2) {
        e.active = true;
        self.$emit("getTtableId", e);
      }
      if (self.table_state == 1) {
        self.$emit("openMergeBox", e);
      }
    },
    /**
     * 关掉模态框
     */
    cancel(e) {
      this.number_box = false;
    },
    /**
     * 获取区域
     */
    getList() {
      getAreasListAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          var all = { name: this.gL("all"), item: "first", id: 0 };
          response.data.data.unshift(all);
          this.tables = response.data.data;
        }
      });
    },
    /**
     * 餐桌列表
     */
    getTableList() {
      var data = {};
      if (self.active_menu != "") {
        data.area_id = self.active_menu;
      }
      if (self.serachText != "") {
        data.keyword = self.serachText;
      }
      getTablesListAPI(data).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.table_list.forEach(item => {
            self.$set(item, "active", false);
          });
          self.table_list = response.data.data.data;
          self.order_info = response.data.data.order_info;
        }
      });
    }
  },
  // sockets: {
  //   //不能改,j建立连接自动调用connect
  //   connect: function() {
  //     //与socket.io连接后回调
  //     console.log("socket connected::::::::::::");
  //   },
  //   disconnect() {
  //     console.log("socket disconnect::::::::::::");
  //   },
  // },
  props: {
    table_state: {
      type: Number,
      default: 0
    },
    table_id: {
      default: 0
    }
  }
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.empty {
  margin: 0 auto;
}
.wrap {
  //  position: absolute;
  //  top: 0;
  //  bottom: 0;
  height: 100%;
  width: 100%;
  padding-right: 0;
  //菜单和输入框
  .top {
    height: 50px;
    width: 100%;
    background-color: @bgColor;
    display: flex;
    justify-content: space-between;
    padding: 5px;
    .menu {
      width: 76%;
      display: flex;
      color: #ffffff;
      height: 100%;
      // overflow-x: scroll;
      .menu-item {
        width: 100%;
        height: 100%;
        // padding: 0 25px;
        // line-height: 40px;
        font-size: 26px;
        cursor: pointer;
      }
      .active {
        background-color: @greenColor;
      }
    }
    .serach {
      width: 220px;
      background: #ffffff;
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .input {
        width: 80%;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #666666;
        width: 70%;
      }
      .search {
        color: #666666;
        font-size: 24px;
        padding: 0 10px;
      }
      .del {
        width: 40px;
        height: 100%;
        background-color: #e6e6e6;
        text-align: center;
        line-height: 40px;
      }
    }
  }
  //餐桌
  .table {
    // padding: 20px;
    padding-right: 0;
    // background: #e5e5e5;
    height: 89%;
    width: 100%;
    position: relative;
    overflow-y: scroll;
    .box {
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      height: 100%;
      padding-left: 20px;
      .table-item {
        text-align: center;
        width: 140px;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #ffffff;
        padding-top: 10px;
        margin-right: 21px;
        margin-bottom: 20px;
        height: 155px;
        cursor: pointer;
        border: 1px solid #e6e6e6;
        .table-num {
          width: 100%;
          font-size: 22px;
          color: #1a1a1a;
          padding-bottom: 18px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          padding-top: 5px;
        }
        .table-img {
          padding-bottom: 15px;
          .iconfont {
            font-size: 45px;
            color: #e6e6e6;
          }
          .green {
            color: @greenColor;
          }
          .zhang {
            color: #ff9c00;
          }
        }
        .table-place {
          font-size: 22px;
          color: #666666;
          background-color: #f2f2f2;
          width: 100%;
          text-align: center;
          padding: 10px 0;
        }
      }
      .active {
        border: 3px solid #139d59;
        height: 160px;
        margin-bottom: 16px;
      }
    }
    .checkout {
      font-size: 22px;
      color: #1a1a1a;
      display: flex;
      justify-content: center;
      // position: absolute;
      // bottom: 20px;
      // width: 97%;
      .price {
        direction: rtl;
        padding-left: 70px;
      }
    }
  }
}
</style>
