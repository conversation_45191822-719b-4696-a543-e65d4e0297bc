<template>
  <div>
    <div class="mask" v-if="number_box">
      <div class="box">
        <div class="title">
          <span>{{gL('shopInfo')}}</span>
        </div>
        <div class="content">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
            <div class="row">
              <el-col :span="11">
                <el-form-item prop="city_id">
                    <el-select v-model="ruleForm.city_id" :placeholder="gL('selectCity')" @change="changeCity">
                      <el-option
                        v-for="item in city_list"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                      </el-option>
                    </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item prop="region_area_id">
                  <el-select v-model="ruleForm.region_area_id" :placeholder="gL('selectArea')">
                    <el-option
                      v-for="item in area_list"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </div>
            <el-form-item prop="category_id">
              <el-select v-model="ruleForm.category_id" :placeholder="gL('rescate')" style="width:100%">
                <el-option
                  v-for="item in category_list"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="name_ug">
              <el-input v-model="ruleForm.name_ug"  class='input-ug' :class="gLang==1?'uy-input':'zh-input'" :placeholder="gL('shopName')+gL('ug')"></el-input>
            </el-form-item>
            <el-form-item prop="name_zh">
              <el-input v-model="ruleForm.name_zh"  :class="gLang==1?'uy-input':'zh-input'" :placeholder="gL('shopName')+gL('zh')"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="btn" @click="confirm">
            {{gL('confirm')}}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
   //s提示框
   .mask{
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      background-color: rgba(0,0,0,.5);
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 1366px;
      min-height: 768px;
      .box{
        background-color: #ffffff;
        width: 620px;
        font-size: 30px;
        .title{
          background-color: #e6e6e6;
          text-align: center;
          color:#1a1a1a;
          padding: 25px 20px;
          position: relative;
          text-align: center;
          .iconfont{
            position: absolute;
            right: 20px;
            font-size: 23px;
            color:@grayColor;
            cursor: pointer;
          }
        }
        .content{
          text-align: center;
          padding: 50px 75px 10px 75px;
          .row{
            display: flex;
            justify-content: space-between;
          }
        }
        .btn{
          margin: 20px 75px 20px 75px;
          background: @greenColor;
          color:#ffffff;
          font-size: 26px;
          text-align: center;
          padding: 15px 0;
          cursor: pointer;
        }
      }
   }
</style>

<script>
import { getCheckMerchantAPI, getMerchantCategoryListAPI, getRegionsListAPI, getRegionsAreaListAPI, postInitMerchantAPI } from "./../api/index.js"
  var self;
  export default {
     created: function() {
      self = this;
      self.gLang = localStorage.getItem("langId");
      // self.validate();
    },
    data(){
      return {
        ruleForm:{
          category_id:'',
          name_ug:'',
          name_zh:'',
          region_area_id:'',
          city_id:'',
      },
      rules: {
          name_ug: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' },
          ],
          name_zh: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          category_id: [
            { required: true, message: this.gL('plaeseChooise'), trigger: 'change' }
          ],
          region_area_id: [
            { required: true, message: this.gL('plaeseChooise'), trigger: 'change' }
          ],
          city_id: [
            { required: true, message: this.gL('plaeseChooise'), trigger: 'change' }
          ],
        },
        city_list:[],
        area_list:[],
        category_list:[],
        number_box:false,
      }
    },
  methods:{
    validate(){
      getCheckMerchantAPI().then((response) => {
        if(response.status >= 200 && response.status < 300){
          if(response.data.data==-1){
            self.getCityList();
            self.merchatList();
            self.number_box = true;
          }
        }
      });
    },
    merchatList(){
      getMerchantCategoryListAPI().then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.category_list = response.data.data;
        }
      });
    },
    getCityList(){
      getRegionsListAPI().then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.city_list = response.data.data;
        }
      });
    },
    changeCity(){
      getRegionsAreaListAPI(self.ruleForm.city_id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.area_list = response.data.data;
        }
      });
    },
    /**
     * 关掉模态框
     */
    cancel () {
      this.$emit('cancel');
    },
    /**
     * 点击确定
     */
    confirm(){ 
      self.$refs.ruleForm.validate((valid) => {
        if (valid) {
          postInitMerchantAPI(self.ruleForm).then((response) => {
            if(response.status >= 200 && response.status < 300){
                self.number_box = false;
              }
            });
          } else {
          return false;
        }
      // this.$emit('confirm');
      })
    },
  },
}
</script>