<template>
    <div class="modal">
        <div class="dialog" :style="{width: _width, height: _height}">
            <div class="title-bar">
                <p style="flex-grow: 1; text-align: center">{{title}}</p>
                <span class="iconfont icon-jia-copy" style="cursor: pointer; font-size: 26px; width: 60px; text-align: center;" @click="cancel"></span>
            </div>
            <div style="flex-grow: 1; padding: 15px; display: flex;">
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        width: {
            type: Number,
            default: 600
        },
        height: {
            type: Number,
            default: 400
        },
        title: {
            type: String,
            default: ""
        }
    },
    data() {
        return {
            
        }
    },
    computed: {
        _width() {
            return this.width + "px"
        },
        _height() {
            return this.height + "px"
        }
    },
    methods: {
        cancel() {
            this.$emit("close")
        }
    }
}
</script>
<style lang="css" scoped>
.modal {
    position: fixed;
    top: 0px;
    bottom: 0px;
    right: 0px;
    left: 0px;
    display: flex;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100;
    align-items: center;
    justify-content: center;
}
.dialog {
    background-color: white;
    display: flex;
    flex-direction: column;
}
.title-bar {
    background-color: rgba(0, 0, 0, 0.089);
    height: 60px;
    font-size: 30px;
    display: flex;
    flex-direction: row;
    align-items: center;
}
* {
    transition: all 0.3s;
}
</style>