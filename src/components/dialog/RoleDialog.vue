<template>
  <div class="mask" v-show="show">
    <div class="box">
      <div class="title">
        <span></span>
        <span>{{ title }}</span>
        <span class="iconfont icon-jia-copy" @click="cancel"></span>
      </div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
        <div class="content">
          <div class="items">
            <div class="row space-between">
              <div class="reset-btn">
                <el-button
                  type="info"
                  plain
                  icon="el-icon-refresh"
                  @click="resetPermission"
                  >重置</el-button
                >
              </div>
              <div class="row-title">
                {{ gL("permission").replace("%count", getPermissionCount) }}
              </div>
            </div>
            <div
              class="content-block"
              :class="gLang == 1 ? 'uy-box' : 'zh-box'"
            >
              <el-tree
                ref="tree"
                :data="data"
                :props="defaultProps"
                node-key="key"
                default-expand-all
                check-on-click-node
                show-checkbox
                :default-checked-keys="defaultCheckedKeys"
                @node-click="nodeClickHandler"
              ></el-tree>
            </div>
          </div>
          <div class="items">
            <div class="row">
              <el-form-item prop="name_ug">
                <el-input
                  v-model="ruleForm.name_ug"
                  :placeholder="gL('uyNamePlaceholder')"
                  :disabled="isDetail"
                ></el-input>
              </el-form-item>
            </div>
            <div class="row">
              <el-form-item prop="name_zh">
                <el-input
                  v-model="ruleForm.name_zh"
                  :placeholder="gL('zhNamePlaceholder')"
                  :disabled="isDetail"
                ></el-input>
              </el-form-item>
            </div>
            <div class="row type">
              <div
                class="check"
                :class="ruleForm.state == 0 ? 'active' : ''"
                @click="clickCheck(0)"
              >
                {{ gL("off") }}
              </div>
              <div
                class="check"
                :class="ruleForm.state == 1 ? 'active' : ''"
                @click="clickCheck(1)"
              >
                {{ gL("open") }}
              </div>
            </div>
          </div>
        </div>
      </el-form>
      <div class="adds">
        <div class="btn" @click="confirm(2)">
          {{ gL("confirm") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "roleDialog",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ""
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  activated() {
    if (this.$refs.tree) {
      this.checkedKeys = this.$refs.tree.getCheckedKeys();
    }
  },
  watch: {
    "$refs.tree": {
      handler() {
        if (this.$refs.tree) {
          this.checkedKeys = this.$refs.tree.getCheckedKeys();
        }
      },
      immediate: true
    }
  },
  computed: {
    getPermissionCount() {
      return this.checkedKeys.length;
    }
  },
  data() {
    return {
      gLang: 1,
      isMounted: false,
      ruleForm: {
        name_ug: "",
        name_zh: "",
        state: 1
      },
      rules: {
        name_ug: [
          { required: true, message: "请输入英文名称", trigger: "blur" }
        ],
        name_zh: [
          { required: true, message: "请输入中文名称", trigger: "blur" }
        ]
      },
      defaultProps: {
        label: this.gLang == 1 ? "name_ug" : "name_zh",
        children: "items"
      },
      defaultCheckedKeys: [],
      checkedKeys: [],
      isDetail: false
    };
  },
  created() {
    this.gLang = localStorage.getItem("langId");
  },
  methods: {
    // 状态点击
    clickCheck(val) {
      if (this.isDetail) return;
      this.ruleForm.state = val;
    },

    // 节点点击
    nodeClickHandler() {
      this.checkedKeys = this.$refs.tree.getCheckedKeys();
    },

    // 重置权限
    resetPermission() {
      if (this.isDetail) return;
      this.$refs.tree.setCheckedKeys([]);
    },

    // 编辑
    editForm(row, type = "default") {
      this.defaultCheckedKeys = row.permissions;
      this.ruleForm.name_ug = row.name_ug;
      this.ruleForm.name_zh = row.name_zh;
      this.ruleForm.state = row.state;
      this.$nextTick(() => {
        this.checkedKeys = this.$refs.tree.getCheckedKeys();
      });
      this.isDetail = type == "detail";
      if (this.isDetail) {
        this.data.forEach(item => {
          console.log("----------- disabled", item);
          item.disabled = true;
          if (item.items && item.items.length > 0) {
            item.items.forEach(it => {
              it.disabled = true;
            });
          }
        });
      }
    },

    // 重置
    resetForm() {
      this.resetPermission();
      this.ruleForm.name_ug = "";
      this.ruleForm.name_zh = "";
      this.ruleForm.state = 1;
      this.checkedKeys = [];
    },

    // 确认
    confirm(type) {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.$emit("confirm", {
            ...this.ruleForm,
            permissions: this.$refs.tree.getCheckedKeys()
          });
        }
      });
    },
    // 关闭
    cancel() {
      this.resetForm();
      this.$nextTick(() => {
        this.$emit("cancel");
      });
    }
  }
};
</script>

<style lang="less">
.el-tree {
  padding: 10px 0px;
}
.uy-box .el-tree {
  direction: rtl;
  .el-checkbox {
    margin-inline: 8px;
  }
  [role="group"] .el-tree-node__content {
    padding-inline-start: 18px;
  }
  .el-tree-node__expand-icon {
    transform: rotate(180deg);
    &.expanded {
      transform: rotate(90deg);
    }
  }
}
.zh-box .el-tree {
  direction: ltr;
  .el-checkbox {
    margin-inline: 8px;
  }
}
</style>

<style lang="less" scoped>
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 920px;
    font-size: 30px;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: #666666;
        cursor: pointer;
      }
    }
    .content {
      padding: 50px 75px 5px 75px;
      display: flex;
      justify-content: space-between;
      .items {
        width: 48%;
        .row {
          margin-bottom: 20px;
        }
        .type {
          display: flex;
          justify-content: space-between;
          .check {
            width: 48%;
            border: 1px solid #cccccc;
            color: #666666;
            font-size: 18px;
            text-align: center;
            cursor: pointer;
            height: 40px;
            line-height: 40px;
            border-radius: 3px;
          }
          .active {
            background-color: #139d59;
            color: #fff;
          }
        }
      }
    }
    .adds {
      display: flex;
      margin: 20px 75px 20px 75px;
      justify-content: space-between;
      .add-btn {
        background-color: #ff9c00;
      }
      .btn {
        width: 100%;
        margin: 0;
      }
      .edit {
        width: 100%;
      }
    }
    .btn {
      background: #139d59;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
      margin: 20px 75px 20px 75px;
    }
  }
  .space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .content-block {
    width: 100%;
    height: 460px;
    background-color: #e6e6e6;
    padding: 6px;
    overflow-y: scroll;
  }
}
</style>
