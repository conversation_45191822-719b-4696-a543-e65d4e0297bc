<template>
  <CommonDialog :dialogShow.sync="show" width="620px" :title="gL('tips')">
    <div class="container">
      <div class="content-box">
        {{ content }}
      </div>
      <div class="btn-box">
        <div class="btn" @click="confirm">
          {{ gL("confirm") }}
        </div>
      </div>
    </div>
  </CommonDialog>
</template>

<script>
import CommonDialog from "../CommonDialog.vue";
export default {
  components: {
    CommonDialog
  },
  props: {
    content: {
      type: String,
      defautl: ""
    }
  },
  data() {
    return {
      show: false
    };
  },
  methods: {
    showDialog() {
      console.log("showDialog ---");
      this.show = true;
    },
    confirm() {
      this.$emit('confirm')
      this.show = false;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  padding: 20px 0px;
}
.content-box {
  padding: 30px;
  font-size: 30px;
  text-align: center;
}

.btn-box {
  margin-top: 20px;
}

.btn {
  flex-grow: 1;
  font-size: 26px;
  text-align: center;
  padding: 15px 0px;
  cursor: pointer;
  background: #139d59;
  color: #ffffff;
}
</style>
