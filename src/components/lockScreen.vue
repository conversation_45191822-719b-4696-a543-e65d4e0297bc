<template>
  <div id="app">
    <div class="wrap">
      <!-- 输入框 -->
      <div class="input-box">
        <div class="name input">
          <label class="name-label" for="name">
            <span class="name-icon iconfont icon-people_fill"></span>
          </label>
          <input
            id="name"
            type="number"
            oninput="if(value.length>11)value=value.slice(0,11)"
            v-model="name"
            :placeholder="gL('accountName')"
            disabled="disabled"
            class="name-input"
          />
        </div>
        <div class="inputs">
          <div class="pass input">
            <label for="pass">
              <span class="iconfont icon-icon2"></span>
            </label>
            <input
              id="pass"
              :type="!easyPassword ? 'password' : 'text'"
              v-model="pass"
              maxlength="16"
              :placeholder="gL('password')"
              @keyup.enter="clickLogin"
            />
            <label
              for="pass"
              class="eye-icon"
              @click="() => (easyPassword = !easyPassword)"
            >
              <span
                class="icons"
                :class="
                  !easyPassword ? 'icons-eye-fill' : 'icons-eye-slash-fill'
                "
              ></span>
            </label>
          </div>
        </div>
        <!-- 登录按钮 -->
        <div class="btn">
          <el-button
            class="button"
            type="primary"
            size="small"
            @click="clickLogin"
            >{{ gL("login") }}</el-button
          >
        </div>

        <!-- 返回登录页面 -->
        <div class="back-login" @click="backLogin">
          {{ gL("switchingUser") }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@bgColor: #333333;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@orangeColor: #ff9c00;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;

.eye-icon {
  cursor: pointer;
  display: inline-block;
  width: 30px;
  text-align: center;
  .icons {
    font-size: 24px;
  }
}

.back-login {
  font-size: 22px;
  margin-top: 30px;
  color: #42b17a;
  cursor: pointer;
}

.wrap {
  width: 100%;
  background-color: @bgColor;
  height: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  min-width: @minWidth;
  min-height: @minHeight;
  padding: 60px 40px 20px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  // 顶部
  .top {
    width: 100%;
    text-align: center;
    position: relative;
    padding-bottom: 50px;
    .ip-btn {
      position: absolute;
      left: 0;
      font-size: 26px;
      color: @greenColor;
      top: 7px;
      cursor: pointer;
    }
    .ip-btn-zh {
      font-size: 22px;
    }
    .title {
      font-size: 40px;
      color: #ffffff;
    }
    .lng {
      position: absolute;
      right: 0;
      top: 10px;
      display: flex;
      justify-content: space-between;
      .uy {
        font-size: 26px;
        color: #b2b2b2;
        cursor: pointer;
      }
      .zh {
        font-size: 22px;
        color: #b2b2b2;
        cursor: pointer;
      }
      .active {
        color: @greenColor;
      }
      .lines {
        display: inline-block;
        width: 2px;
        height: 20px;
        background: @graphiteColor;
        margin: 0 20px;
      }
    }
  }
  .input-box {
    margin-bottom: auto;
  }
  // 输入框
  .inputs {
    background-color: #ffffff;
    font-size: 26px;
    .input {
      padding: 20px;
      border-bottom: 1px solid @graphiteColor;
      &:last-child {
        border-bottom: none;
      }
      .iconfont {
        font-size: 26px;
        color: #b3b3b3;
      }
      input {
        outline: none;
        padding-left: 25px;
        width: 320px;
        color: #1a1a1a;
      }
    }
  }
  .name {
    display: flex;
    flex-direction: column;
    .name-input {
      padding: 20px;
      background: transparent;
      text-align: center;
      font-size: 26px;
    }
    .name-label {
      margin: 0 auto;
      width: 80px;
      height: 80px;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 50%;
      position: relative;
      .name-icon {
        font-size: 60px;
        position: absolute;
        top: 0;
        line-height: 80px;
        left: 50%;
        margin-left: -30px;
        color: #555;
      }
    }
  }

  //记住密码
  .check {
    width: 433px;
    display: flex;
    align-items: center;
    font-size: 22px;
    color: #f2f2f2;
    padding-top: 20px;
    input {
      cursor: pointer;
      width: 25px;
      height: 25px;
    }
    label {
      cursor: pointer;
      padding: 0 20px;
      margin-bottom: -6px;
    }
  }
  // 登录按钮
  .btn {
    width: 430px;
    // background: @greenColor;
    font-size: 26px;
    color: #ffffff;
    // padding: 20px 0;
    text-align: center;
    margin-top: 40px;
    cursor: pointer;
    &:hover {
      // background-color: #2db170;
    }
    button {
      width: 100%;
      padding: 20px 0;
      font-size: 26px;
    }
  }
  .info {
    display: flex;
    font-size: 22px;
    color: @graphiteColor;
    .num {
      direction: ltr;
      display: inline-block;
    }
  }
}
//s提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  // align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box {
    background-color: #ffffff;
    width: 660px;
    font-size: 30px;
    height: 420px;
    margin-top: 150px;
    .title {
      align-items: center;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      background-color: #e6e6e6;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content {
      text-align: center;
      padding: 50px 75px 30px 75px;
      .inputs {
        border: 1px solid #cccccc;
        display: flex;
        padding-top: 10px;
        padding-bottom: 7px;
        direction: ltr;
        input {
          width: 30%;
          text-align: center;
          outline: none;
        }
      }
    }
    .btn {
      margin: 20px 75px 20px 75px;
      background: @greenColor;
      color: #ffffff;
      font-size: 26px;
      text-align: center;
      padding: 15px 0;
      cursor: pointer;
    }
    .btn.sec {
      background: @orangeColor;
    }
    .content {
      padding: 40px 60px;
      .i-line {
        display: flex;
        margin-bottom: 20px;
        .check {
          line-height: 50px;
          width: 150px;
          margin-right: 20px;
          input {
            vertical-align: middle;
            outline: none;
          }
          label {
            vertical-align: middle;
            color: #666;
          }
        }
        .check.fixed {
          text-align: right;
          margin-left: 20px;
          margin-right: 0;
        }
        .input {
          flex: 1;
          input.domain {
            width: 100%;
            padding: 10px 4% 7px;
            border: #cccccc 1px solid;
            outline: none;
          }
        }
      }
      .btn-area {
        display: flex;
        .btn {
          flex: 1;
          margin: 0 20px;
        }
        :first-child {
          margin-left: 0;
        }
        :last-child {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
<script>
// import shopInfo from "./components/shopInfo.vue";
import { postAuthorizationsAPI } from "./../api/index.js";
var self;
export default {
  name: "app",
  created() {
    self = this;
    self.isDesktopRuntime = DesktopRuntime != undefined;
    //如果是记住获取Cookie里的数据
    const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
    if (storeInfo.user != "") {
      self.name = storeInfo.user;
    } else {
      self.name = "";
    }
  },
  activated() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.pass = "";
    self.load = false;
  },
  props: {
    lockScreen: {
      type: Boolean,
      value: false
    },
    openLockScreen: {
      type: Boolean,
      value: true
    }
  },
  data() {
    return {
      isDesktopRuntime: false,
      msg: "login",
      gLang: 1,
      name: "",
      pass: "",
      load: false,
      easyPassword: false
    };
  },
  components: {
    // shopInfo
  },
  methods: {
    // 返回登录页面
    backLogin() {
      this.$emit("back");
    },

    // 登录按钮
    clickLogin() {
      if ((self.name.length > 2) & (self.pass.length > 5)) {
        self.load = true;
        const storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
        postAuthorizationsAPI(
          {
            username: self.name,
            password: self.pass,
            merchant_no: storeInfo.merchant_no
          },
          {
            noLogin: true
          }
        )
          .then(response => {
            if (response.status >= 200 && response.status < 300) {
              self.$message({
                message: self.gL("successLogin"),
                type: "success",
                customClass: self.$toastClass(),
                offset: 120
              });
              localStorage.setItem(
                "merchant_info",
                JSON.stringify({
                  user: this.name,
                  userName: response.data.data.user.name,
                  id: response.data.data.user.id,
                  merchant_name_zh: response.data.data.merchant.name_zh,
                  merchant_name_ug: response.data.data.merchant.name_ug,
                  merchant_address_ug: response.data.data.merchant.address_ug,
                  merchant_address_zh: response.data.data.merchant.address_zh,
                  operation_password: response.data.data.user.operation_password,
                  isHandOver: response.data.data.user.is_handover,
                  merchant_no: response.data.data.merchant.no,
                  merchant_phone: response.data.data.merchant.phone,
                  mode: response.data.data.merchant.mode
                })
              );

              localStorage.setItem(
                "token",
                response.data.data.token.access_token
              );
              self.load = false;
              self.$emit("closeLockScreen");
            }
          })
          .catch(err => {
            self.load = false;
            if (err.response == undefined) {
              this.$message({
                message: self.gL("error"),
                type: "warning",
                customClass: self.$toastClass(),
                offset: 120
              });
            }
          });
      } else {
        if (self.name == "") {
          this.$message({
            message: self.gL("inputAccount"),
            type: "warning",
            customClass: self.$toastClass(),
            offset: 120
          });
        } else if (self.pass == "") {
          this.$message({
            message: self.gL("inputPassword"),
            type: "warning",
            customClass: self.$toastClass(),
            offset: 120
          });
        } else if (self.name.length < 3) {
          this.$message({
            message: self.gL("accountLength"),
            type: "warning",
            customClass: self.$toastClass(),
            offset: 120
          });
        } else if (self.pass.length < 6) {
          this.$message({
            message: self.gL("passLength"),
            type: "warning",
            customClass: self.$toastClass(),
            offset: 120
          });
        }
      }
    }
  }
};
</script>
