<template>
  <div class="common-dialog-mask" v-if="dialogShow">
    <div class="common-dialog-box" :style="{ width: width }">
      <div class="dialog-title">
        <span :style="{ direction: direction }">{{ title }}</span>
        <span class="iconfont icon-jia-copy" @click="close"></span>
      </div>
      <div
        class="dialog-content"
        :style="{ direction: direction }"
      >
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: "800px"
    },
    title: {
      type: String,
      default: ""
    },
    direction: {
      type: String,
      default: "rtl"
    }
  },
  data() {
    return {};
  },
  methods: {
    close() {
      this.$emit("update:dialogShow", false);
      this.$emit("close");
    }
  }
};
</script>

<style lang="less" scoped>
/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}
.common-dialog-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 990;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;

  .common-dialog-box {
    background-color: #ffffff;
    font-size: 30px;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;

    .dialog-title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: #666666;
        cursor: pointer;
      }
    }

    .dialog-content {
      padding: 15px 30px;
      padding-bottom: 0;
      font-size: 26px;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
