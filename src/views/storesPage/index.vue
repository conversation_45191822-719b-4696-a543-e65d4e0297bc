<template>
  <div class="stores-page-box">
    <webview v-if="isElectron" class="web-view" :src="`https://cms-ros.mulazim.com/admin/lang/${langId == 1 ? 'ug' : 'zh_CN'}`"></webview>
    <iframe v-else class="web-view" :src="`https://cms-ros.mulazim.com/admin/lang/${langId == 1 ? 'ug' : 'zh_CN'}`"></iframe>
  </div>
</template>

<script>
export default {
  data() {
    return {
      langId: 1,
      isElectron: window.electronAPI && window.electronAPI.isElectron
    };
  },
  activated() {
    this.langId = localStorage.getItem("langId");
  }
};
</script>

<style lang="less" scoped>
.stores-page-box {
    width: 100%;
    height: 100%;
    .web-view {
        width: 100%;
        height: 100%;
    }
}
</style>
