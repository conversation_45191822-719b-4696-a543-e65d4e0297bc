<template>
  <div class="secondary-main" :class="gLang == 1 ? 'ug-box' : 'zh-box'">
    <div class="image-box" v-if="viewType == 'image'">
      <img
        v-if="gLang == 1"
        src="./../../assets/images/volcome_ug.jpg"
        alt=""
      />
      <img
        v-if="gLang == 2"
        src="./../../assets/images/volcome_zh.jpg"
        alt=""
      />
    </div>

    <div class="qrcode-box" v-if="viewType == 'qrcode'">
      <el-row>
        <el-col :span="14">
          <div class="food-title">{{ gL("price") }}</div>
          <div class="line"></div>
          <div class="food-item">
            <el-col class="food-item-title center" :span="6">{{
              gL("price")
            }}</el-col>
            <el-col class="food-item-title center" :span="6">{{
              gL("count")
            }}</el-col>
            <el-col class="food-item-title" :span="12">{{
              gL("names")
            }}</el-col>
          </div>
          <div class="foods-box">
            <div
              class="food-item"
              v-for="(item, index) in qrCodeList.foods &&
                qrCodeList.foods.order_details"
              :key="index"
            >
              <el-col class="food-item-text center" :span="6"
                >￥{{ item.price }}</el-col
              >
              <el-col class="food-item-text center" :span="6">{{
                item.foods_count
              }}</el-col>
              <el-col class="food-item-text" :span="12">{{
                gLang == 1 ? item.food_name_ug : item.food_name_zh
              }}</el-col>
            </div>
          </div>
          <div class="line"></div>
          <div class="food-footer">
            <div class="footer-text">
              {{ gL("secondaryPageFooterText") }}
            </div>
            <div class="footer-price">￥{{ qrCodeList.foods.price }}</div>
          </div>
        </el-col>
        <el-col class="qrcode-box-col" :span="10">
          <div class="pay-icon">
            <img src="./../../assets/images/wechat_pay.png" alt="" />
          </div>
          <div class="total-price">￥{{ qrCodeList.foods.price }}</div>
          <div class="qrcode">
            <div class="qrcode-svg" v-html="qrCodeList.qrcode"></div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="recharge-qrcode-box" v-if="viewType == 'rechargeQrcode'">
      <div class="pay-icon">
        <img src="./../../assets/images/wechat_pay.png" alt="" />
      </div>
      <div class="total-price">￥{{ qrCodeList.recharge_amount }}</div>
      <div class="qrcode">
        <img :src="qrCodeList.code && qrCodeList.code.code_url" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  activated() {
    this.gLnag = localStorage.getItem("langId");
  },

  mounted() {
    window.electronAPI &&
      window.electronAPI.showPayQrCodeSecondary((event, data) => {
        this.qrCodeList = data;
        this.viewType = "qrcode";
      });

    window.electronAPI &&
      window.electronAPI.closePayQrCodeSecondary(() => {
        this.viewType = "image";
      });

    window.electronAPI &&
      window.electronAPI.showRechargePayQrCodeSecondary((event, data) => {
        console.log("dataaa -> ", data);
        this.qrCodeList = data;
        this.viewType = "rechargeQrcode";
      });

    window.electronAPI &&
      window.electronAPI.closeRechargePayQrCodeSecondary(() => {
        this.viewType = "image";
      });
  },

  data() {
    return {
      gLang: 1,
      viewType: "image",
      qrCodeList: {}
    };
  }
};
</script>

<style lang="less" scoped>
.secondary-main {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;

  &.ug-box {
    direction: rtl;
  }

  &.zh-box {
    direction: ltr;
  }

  .image-box {
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .qrcode-box {
    width: 100%;
    height: 100%;
    background-color: #fff;
    padding: 30px;
    box-sizing: border-box;

    .food-title {
      font-size: 38px;
      text-align: center;
      padding: 10px;
    }
    .line {
      width: 100%;
      height: 2px;
      background-color: #000;
      margin: 15px 0px;
    }
    .foods-box {
      width: 100%;
      height: 70vh;
      padding: 10px 0px;
      overflow-y: auto;
    }
    .food-item-title {
      font-size: 38px;
    }
    .center {
      text-align: center;
    }
    .food-item-text {
      font-size: 36px;
    }
    .food-item {
      height: 55px;
    }
    .food-footer {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      font-size: 36px;
      padding: 0px 20px;
    }
  }
  .qrcode-box-col,
  .recharge-qrcode-box {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 80px 0px;
  }

  .pay-icon {
    width: 150px;
    height: 150px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .total-price {
    color: #ff5e00;
    font-size: 48px;
    height: 150px;
    line-height: 150px;
  }
}
</style>
