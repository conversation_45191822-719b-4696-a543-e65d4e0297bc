<template>
  <CommonDialog
    :dialogShow.sync="show"
    :title="
      isEdit
        ? this.gL('editCreditDialogTitle')
        : this.gL('addCreditDialogTitle')
    "
    @close="close"
  >
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
      <div
        class="content"
        :class="
          gLang == 1
            ? 'add-credit-dialog-content_ug'
            : 'add-credit-dialog-content_zh'
        "
      >
        <div class="content-input">
          <div class="item">
            <el-form-item prop="name_zh" :label="gL('nameZh')">
              <el-input
                :style="{ direction: 'ltr' }"
                :placeholder="gL('inputCreditNameZh')"
                maxlength="16"
                v-model="ruleForm.name_zh"
              ></el-input>
            </el-form-item>
          </div>

          <div class="item">
            <el-form-item prop="credit_limit" :label="`${gL('creditLimit')} (${gL('rmb')})`">
              <el-input
                min="1"
                v-positive-integer="1"
                v-model="ruleForm.credit_limit"
                :style="{ direction: 'ltr' }"
                :placeholder="`${gL('creditLimit')} (${gL('rmb')})`"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="content-input">
          <div class="item">
            <el-form-item prop="name_ug" :label="gL('nameUg')">
              <el-input
                :placeholder="gL('inputCreditNameUg')"
                maxlength="16"
                v-model="ruleForm.name_ug"
              ></el-input>
            </el-form-item>
          </div>

          <div class="item">
            <el-form-item prop="phone" :label="gL('phoneNumber')">
              <el-input
                :placeholder="gL('phoneNumber')"
                :style="{ direction: 'ltr' }"
                :disabled="isEdit"
                type="number"
                v-model="ruleForm.phone"
              ></el-input>
            </el-form-item>
          </div>
        </div>
      </div>
      <div class="btn" @click="confirm">
        {{ gL("confirm") }}
      </div>
    </el-form>
  </CommonDialog>
</template>

<script>
import CommonDialog from "./../../../components/CommonDialog.vue";
export default {
  components: {
    CommonDialog
  },
  data() {
    return {
      show: false,
      isEdit: false,
      gLang: 1,
      ruleForm: {
        name_ug: "",
        name_zh: "",
        phone: "",
        credit_limit: "",
        status: 1
      },
      rules: {
        name_ug: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" }
        ],
        name_zh: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" }
        ],
        phone: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
          { min: 11, max: 11, message: this.gL("mobile"), trigger: "blur" }
        ],
        credit_limit: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    close() {
      this.ruleForm = {
        name_ug: "",
        name_zh: "",
        phone: "",
        credit_limit: "",
        status: 1
      };
      this.show = false;
    },
    confirm() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          if (this.ruleForm.credit_limit < 1) {
            this.$message({
              message: this.gL("creditPriceTips"),
              type: "warning",
              customClass: this.$toastClass(),
              offset: 120
            });
            return;
          }
          this.$emit("confirm", this.isEdit, {
            ...this.ruleForm,
            credit_limit: parseFloat(this.ruleForm.credit_limit)
          });
        }
      });
    },
    showDialog(type, editData) {
      this.gLang = localStorage.getItem("langId");
      this.show = true;
      if (type == "add") {
        this.isEdit = false;
      } else if (type == "edit") {
        this.isEdit = true;
        this.ruleForm.name_ug = editData.name_ug;
        this.ruleForm.name_zh = editData.name_zh;
        this.ruleForm.phone = editData.phone;
        this.ruleForm.credit_limit = editData.credit_limit;
        this.ruleForm.status = editData.status;
      }
    }
  }
};
</script>

<style>
.add-credit-dialog-content_ug .el-form-item__label {
  float: right;
  direction: rtl;
}
.add-credit-dialog-content_zh .el-form-item__label {
  float: left;
  direction: ltr;
}
</style>

<style lang="less" scoped>
.content {
  padding: 35px 20px 0 20px;
  display: flex;
  justify-content: space-between;
}
.content-input {
  width: 47%;
  .item {
    width: 100%;
    margin-bottom: 30px;
  }
  .select {
    margin-bottom: 20px;
  }
  .sex {
    display: flex;
    justify-content: space-between;
    .check {
      width: 48%;
      background-color: #f2f2f2;
      border: 1px solid #cccccc;
      color: #666666;
      font-size: 18px;
      text-align: center;
      cursor: pointer;
      padding: 10px;
    }
    .active {
      background-color: #139d59;
      color: #fff;
    }
  }
}

.btn {
  margin: 20px 70px;
  background: #139d59;
  color: #ffffff;
  font-size: 26px;
  text-align: center;
  padding: 15px 0;
  cursor: pointer;
}
</style>
