<template>
  <div>
    <div class="mask" v-if="number_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ gL("creditRepayment") }}</span>
          <span
            class="iconfont icon-jia-copy"
            @click="cancelRechargeBox"
          ></span>
        </div>
        <div class="content">
          <div class="calculator-screen">
            <div
              class="boxs"
              :style="{
                borderColor: '#139d59'
              }"
            >
              <div class="calculation-screen">
                <div>
                  <span>￥</span>
                  <input
                    type="text"
                    class="number"
                    maxlength="4"
                    @input="testing('number')"
                    v-model="number"
                    :placeholder="gL('memberPricePlaceholder')"
                  />
                </div>
                <span
                  class="iconfont icon-jia-copy"
                  v-if="number.length"
                  @click="() => (number = '')"
                ></span>
              </div>
            </div>
          </div>
          <div class="pay-list">
            <div
              class="pay-item"
              v-for="(item, index) in pay_list"
              :key="index"
              :class="item.id == pay_state ? 'active' : ''"
              @click="payState(item)"
            >
              <img
                :src="item.icon"
                alt=""
                width="30px"
                style="margin-right: 15px"
              />
              <span>{{ gLang == 1 ? item.name_ug : item.name_zh }}</span>
            </div>
          </div>
          <div class="btn-box">
            <div class="numbers">
              <div class="one">
                <div
                  class="item"
                  v-for="(item, index) in num_list"
                  :key="item"
                  @click="calculation(item)"
                >
                  {{ item }}
                </div>
              </div>
              <div class="two">
                <div class="iconfont icon-tuige" @click="removeNumber"></div>
                <div @click="calculation('20')">20</div>
                <div @click="calculation('50')">50</div>
                <div @click="calculation('.')">.</div>
              </div>
            </div>
            <div class="btns-price">
              <div class="check-out" @click="recharge">
                {{ gL("recharge") }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mask" v-if="qrcode_box">
      <div class="box qrcode">
        <div class="title">
          <span></span>
          <span>{{ gL("wechatPay") }}</span>
          <span class="iconfont icon-jia-copy" @click="closeQrcode"></span>
        </div>
        <div
          class="content"
          :class="{ 'qrcode-box': itemData.pay_method == 2 }"
        >
          <div v-if="itemData.pay_method == 2" v-html="qrcode"></div>
          <span v-if="itemData.pay_method == 1">{{ gL("paid") }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
//s提示框
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1366px;
  min-height: 768px;
  .box.qrcode {
    width: 600px;
  }
  .box {
    background-color: #ffffff;
    width: 880px;
    font-size: 30px;
    -webkit-animation: fadelogIn 0.4s;
    animation: fadelogIn 0.4s;
    .title {
      background-color: #e6e6e6;
      color: #1a1a1a;
      padding: 25px 20px;
      position: relative;
      text-align: center;
      .iconfont {
        position: absolute;
        right: 20px;
        font-size: 23px;
        color: @grayColor;
        cursor: pointer;
      }
    }
    .content {
      text-align: center;
      padding: 40px 50px;
      .pay-list {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        margin: 30px 0;
        .pay-item {
          width: 23%;
          background-color: #e6e6e6;
          text-align: center;
          font-size: 22px;
          padding: 25px 0;
          margin-right: 18px;
          margin-bottom: 18px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          &:nth-child(4n) {
            margin-right: 0;
          }
        }
        .active {
          background: @greenColor;
          color: #ffffff;
        }
      }
      .calculator-screen {
        // margin: 30px 0;
        display: flex;
        justify-content: space-between;
        .boxs {
          width: 100%;
          border: 1px solid @graphiteColor;
          color: #ffffff;
          font-size: 30px;
          display: flex;
        }
        .calculation-screen {
          padding: 15px;
          color: @grayColor;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .cash {
            font-size: 26px;
          }
          .iconfont {
            font-size: 21px;
            color: @grayColor;
            cursor: pointer;
          }
          .number {
            font-size: 30px;
            // font-weight: bold;
            color: @grayColor;
            outline: none;
            width: 290px;
          }
        }
        .difference {
          color: #ff5151;
          .text {
            color: #ff5151;
          }
        }
      }
      .btn-box {
        width: 100%;
        height: 270px;
        display: flex;
        justify-content: space-between;
        .numbers {
          width: 60%;
          display: flex;
          justify-content: space-between;
          .one {
            width: 75%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            height: 103.5%;
            .item {
              width: 31.5%;
              background: #e6e6e6;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 10px;
              font-size: 30px;
              color: @grayColor;
              font-weight: bold;
              cursor: pointer;
            }
            .item:last-child {
              width: 64.5%;
            }
          }
          .two {
            width: 22%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
            div {
              background: #e6e6e6;
              height: 22%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 30px;
              color: @grayColor;
              font-weight: bold;
              cursor: pointer;
            }
            .iconfont {
              font-size: 33px;
            }
          }
        }
        .btns-price {
          width: 38%;
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          .checks {
            display: flex;
            justify-content: space-between;
            height: 21%;
            font-size: 26px;
            .check {
              height: 100%;
              width: 48%;
              display: flex;
              cursor: pointer;
              background-color: #cccccc;
              color: #ffffff;
              .check-item {
                display: flex;
                justify-content: center;
                width: 60%;
                align-items: center;
                background-color: #666666;
              }
            }
            .active {
              flex-direction: row-reverse;
              .check-item {
                background-color: @greenColor;
              }
            }
          }
          .check-out {
            background-color: #ff9c00;
            font-size: 30px;
            height: 100%;
            color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            &:hover {
              opacity: 0.8;
            }
          }
        }
      }
    }
  }
}
/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

// @-webkit-keyframes fadelogIn {
//   0% {
//     -webkit-transform: translate3d(0, 100%, 0);
//   }
//   100% {
//     -webkit-transform: none;
//   }
// }

.qrcode-box {
  min-height: 580px;
}
</style>

<script>
import QRCode from "qrcode";
import {
  getPaymentTypesPayableListAPI,
  postCreditRePaymentAPI,
  postCreditNativePayAPI,
  getCreditRePaymentQueryAPI,
  postCreditRePaymentMicroPayAPI
} from "./../../../api/index.js";
var self;
export default {
  props: {
    number_box: {
      type: Boolean,
      default: false
    },
    creditData: {
      type: Object,
      default: {}
    }
  },
  activated: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
  },
  deactivated() {
    clearInterval(self.interFlag);
  },
  data() {
    return {
      gLang: 1,
      pay_list: "",
      pay_state: 0,
      num_list: ["7", "8", "9", "4", "5", "6", "1", "2", "3", "0", "00"], //默认支付类型
      qrcode_box: false, //二维码模态框
      number: "", //计算内容
      qrcode: "", //二维码
      itemData: "",
      interFlag: "",
      nextCode: null,
      nextTime: null,
      lastCode: null,
      lastTime: null,
      scanPayCode: "",
      modalLoading: null,
      payNo: ""
    };
  },
  watch: {
    async number_box(value) {
      if (value) {
        this.modalLoading = this.$loading();
        await this.getPayList();
      }
    }
  },
  methods: {
    // 监听扫码器输入
    scanPayBox() {
      document.addEventListener("keypress", this.listenScanPay);
      this.nextCode = null;
      this.nextTime = null;
      this.lastCode = null;
      this.lastTime = null;
    },

    listenScanPay(e) {
      this.nextCode = e.charCode;
      this.nextTime = new Date().getTime();
      // 扫码器的录入速度远快过手动录入，两个字符间隔小于20MS判定录入设备为扫码器
      if (
        this.lastCode != null &&
        this.lastTime != null &&
        this.nextTime - this.lastTime <= 20
      ) {
        this.scanPayCode += String.fromCharCode(this.lastCode);
      } else if (
        this.lastCode != null &&
        this.lastTime != null &&
        this.nextTime - this.lastTime > 100
      ) {
        this.scanPayCode = "";
      }
      this.lastCode = this.nextCode;
      this.lastTime = this.nextTime;
      if (e.code == "Enter" && this.scanPayCode.length > 10) {
        console.log("Enter -> ", this.scanPayCode);
        this.getCode(this.scanPayCode);
        this.scanPayCode = "";
      }
    },
    /**
     * 支付类型
     */
    getPayList(s) {
      getPaymentTypesPayableListAPI({
        type: 0
      })
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.pay_list = response.data.data.filter(item => {
              if (item.id == 4 || item.id == 11) {
                return false;
              }
              return true;
            });
            this.pay_state = response.data.data[0].id;
          }
        })
        .finally(() => {
          this.modalLoading.close();
        });
    },
    /**
     * 删除数字
     */
    removeNumber() {
      this.number = this.number.substr(0, this.number.length - 1);
    },
    /**
     * 计算
     */
    calculation(e) {
      if (this.number.length > 6) return;
      if (e.length == 2 && this.number.length >= 5) return;
      if (e.length == 3 && this.number.length > 4) return;
      if (e === "." && (this.number.indexOf(".") !== -1 || this.number === ""))
        return;

      // 如果已经有小数点，检查小数点后位数
      if (this.number.indexOf(".") !== -1) {
        const decimalPart = this.number.split(".")[1];
        if (decimalPart && decimalPart.length >= 2) {
          return; // 小数点后已经有两位，不再接受输入
        }
      }

      this.number = this.number + e;
    },

    //验证充值金额
    testing(type) {
      this[type] = this[type].replace(/[^\d.]/g, ""); //先把非数字的都替换掉，除了数字和.
      this[type] = this[type].replace(/\./g, ""); //必须保证第一个为数字而不是.
    },
    /**
     * 点击支付类型
     */
    payState(e) {
      self.itemData = e;
      if (e.id == 1) {
        if (self.number != "") {
          self.pay_state = e.id;
          self.getWechat(e);
        } else {
          self.$message({
            message: self.gL("inputRecharge"),
            type: "error",
            customClass: self.$toastClass(),
            offset: 120
          });
        }
      } else {
        self.pay_state = e.id;
      }
    },
    /**
     * 关掉模态框
     */
    cancelRechargeBox() {
      self.$emit("cancelRechargeBox");
      self.number = "";
    },
    recharge() {
      if (self.number != "") {
        postCreditRePaymentAPI({
          holder_id: this.creditData.id,
          payment_type_id: this.pay_state,
          amount: parseFloat(this.number) * 100
        }).then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.cancelRechargeBox();
            self.$emit("recharge", response.data.data);

            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
          }
        });
      } else {
        self.$message({
          message: self.gL("inputRecharge"),
          type: "error",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    /**
     * 点击确定
     */
    confirm() {
      self.$emit("confirm");
    },
    /**
     * 微信充值
     */
    getWechat(e) {
      if (e.pay_method == 1) {
        self.qrcode_box = true;
        this.scanPayBox();
      } else if (e.pay_method == 2) {
        postCreditNativePayAPI({
          holder_id: this.creditData.id,
          amount: parseFloat(this.number) * 100
        }).then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.payNo = response.data.data.payment_no;
            if (this.pay_state == 1) {
              QRCode.toString(
                response.data.data.pay_url,
                {
                  errorCorrectionLevel: "L",
                  width: 500,
                  height: 500,
                  margin: 1
                },
                (error, qrcode) => {
                  if (error != null) {
                    console.log("create qrcode error", error);
                    return;
                  }
                  this.qrcode = qrcode;
                  this.qrcode_box = true;
                  this.getPayStatus();
                }
              );

              // window.electronAPI &&
              //   window.electronAPI.showRechargePayQrCode({
              //     code: response.data,
              //     recharge_amount: this.number
              //   });
            }
          }
        });
      }
    },
    //刷码支付
    getPayStatus() {
      self.interFlag = setInterval(function() {
        if (self.qrcode_box) {
          getCreditRePaymentQueryAPI(self.payNo, {
            isNoMessage: true
          }).then(response => {
            if (response.status >= 200 && response.status < 300) {
              if (response.data.data.status == 1) {
                self.qrcode_box = false;
                self.closeQrcode();
                self.number = "";
                self.$emit("cancelRechargeBox", 1);
              }
            }
          });
        } else {
          clearInterval(self.interFlag);
        }
      }, 3000);
    },
    //刷卡支付
    getCode(e) {
      if (self.itemData.pay_method == 1 && self.qrcode_box) {
        postCreditRePaymentMicroPayAPI({
          holder_id: this.creditData.id,
          amount: parseFloat(self.number) * 100,
          auth_code: e
        }).then(response => {
          if (response.status >= 200 && response.status < 300) {
            if (response.data.data.status == 1) {
              self.closeQrcode();
              self.number = "";
              self.$emit("cancelRechargeBox", 1);
              self.$message({
                message: self.gL("paymentSuccess"),
                type: "success",
                customClass: self.$toastClass(),
                offset: 120
              });
            }
          }
        });
      }
    },
    //关掉模态框
    closeQrcode() {
      window.electronAPI && window.electronAPI.closeRecahrgePayQrCode();
      document.addEventListener("keypress", this.listenScanPay);
      self.qrcode_box = false;
      self.pay_state = self.pay_list[0].id;
      clearInterval(self.interFlag);
      // self.cancelRechargeBox();
    }
  }
};
</script>
