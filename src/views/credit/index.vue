<template>
  <div class="wrap">
    <div class="top" v-if="!detail">
      <div class="serach">
        <div class="input">
          <span class="iconfont icon-search search"></span>
          <input
            type="number"
            :placeholder="gL('phoneNumber')"
            v-model="serachText"
            v-on:input="inputSearch"
          />
        </div>
        <div class="del" v-if="delete_text" @click="removeSerach">
          <span class="iconfont icon-jia-copy"></span>
        </div>
      </div>
      <div class="all" v-if="tableData.length != 0">
        <div>
          <span>{{ gL("pepoleCount") }}:</span>
          <span class="price">{{ topStatis.total_count }}</span>
        </div>
        <div :style="{ direction: gLang == 2 ? 'ltr' : 'rtl' }">
          <span>{{ gL("creditPrice") }}:</span>
          <span class="price" style="display: inline-block"
            >￥{{ $toMoney(topStatis.total_balance) }}</span
          >
        </div>
      </div>
      <div
        class="btn"
        @click="addVipBoxs(0)"
        :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
      >
        <span class="iconfont icon-jia-copy-copy"></span>
        <span>{{ gL("creditAddText") }}</span>
      </div>
    </div>
    <div class="top" v-if="detail">
      <div class="detail-tit">
        <div
          class="menus"
          :class="record ? 'active' : ''"
          @click="recordClick(0)"
        >
          {{ gL("creditDetail") }}
        </div>
        <div
          class="menus"
          :class="!record ? 'active' : ''"
          @click="recordClick(1)"
        >
          {{ gL("creditHistory") }}
        </div>
      </div>
      <div class="btns">
        <div class="btn-item rechar" @click="openRechargeBox()">
          {{ gL("creditRepayment") }}
        </div>
        <!-- <div class="btn-item edit" @click="addVipBoxs(2)">
          {{ gL("edit") }}
        </div> -->
        <div class="btn-item back" @click="backDetail">{{ gL("back") }}</div>
      </div>
    </div>
    <div class="table" style="height: 90%; overflow-y: scroll" v-if="!detail">
      <el-table
        :data="tableData"
        header-cell-class-name="header"
        :header-cell-style="{ background: '#2e3033' }"
        :cell-style="{ background: '#f2f2f2', borderColor: '#cccccc' }"
        @row-click="vipDetail"
        row-class-name="row"
        style="width: 100%"
        v-loading="tableLoading"
        height="100%"
      >
        <el-table-column
          :label="gL('serialNumber')"
          type="index"
          align="center"
          width="60"
        >
        </el-table-column>
        <el-table-column prop="phone" align="center" :label="gL('phoneNumber')">
        </el-table-column>
        <el-table-column prop="name_ug" align="center" :label="gL('nameUg')">
        </el-table-column>
        <el-table-column prop="name_zh" align="center" :label="gL('nameZh')">
        </el-table-column>
        <el-table-column align="center" :label="gL('creditPrice')">
          <template slot-scope="scope">
            <span>{{ formatNumber(scope.row.balance) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="credit_limit"
          align="center"
          :label="gL('creditLimit')"
        >
          <template slot-scope="scope">
            <span>{{ formatNumber(scope.row.credit_limit) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          align="center"
          width="140"
          :label="gL('state')"
        >
          <template slot-scope="scope">
            <el-switch
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.status"
              @change="value => changeSwitch(value, scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column :label="gL('operation')" prop="dosome" align="center">
          <template slot-scope="scope">
            <!-- <el-button
              type="text"
              class="rmb"
              icon="iconfont icon-RMB"
              circle
              @click.stop="openRechargeBox(0, scope.row)"
            ></el-button> -->
            <!-- <span style="padding: 0 15px">
              <span class="line"></span>
            </span> -->
            <el-button
              type="text"
              class="edits"
              icon="iconfont icon-shiliangzhinengduixiang"
              @click.stop="addVipBoxs(1, scope.row)"
              circle
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="paging">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="totalCount"
        >
        </el-pagination>
      </div>
    </div>
    <div v-if="detail" style="height: 100%">
      <div class="no-record" v-if="!record" style="height: 100%">
        <div class="top">
          <div class="select">
            <el-select
              v-model="recordType"
              :placeholder="gL('plaeseChooise')"
              @change="getRecordList('change')"
            >
              <el-option
                v-for="(item, index) in recordTypeList"
                :key="index"
                :label="item.name"
                :value="index"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="table" style="height: 83%; overflow-y: scroll">
          <el-table
            :data="recordList"
            header-cell-class-name="header"
            :header-cell-style="{ background: '#2e3033' }"
            :cell-style="{ background: '#f2f2f2', borderColor: '#cccccc' }"
            row-class-name="row"
            v-loading="tableLoading"
            style="width: 100%"
          >
            <el-table-column
              prop="order_no"
              align="center"
              width="320"
              :label="gL('ordeNum')"
            >
            </el-table-column>
            <el-table-column
              prop="cashier_name"
              align="center"
              :label="gL('cashier')"
            >
            </el-table-column>
            <el-table-column
              align="center"
              :label="gL('type')"
              prop="type_label"
            >
            </el-table-column>
            <el-table-column align="center" :label="gL('prices')">
              <template slot-scope="scope">
                <span
                  v-if="scope.row.type == 2 || scope.row.type == 3"
                  style="color: #139d59;"
                  >{{ formatNumber(scope.row.amount) }}</span
                >
                <span v-if="scope.row.type == 1" style="color: #f94545;">{{
                  formatNumber(scope.row.amount)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" :label="gL('creditPrice')">
              <template slot-scope="scope">
                <span v-if="scope.row.balance > 0" style="color: #f94545;">{{
                  formatNumber(scope.row.balance)
                }}</span>
                <span v-else style="color: #139d59;">{{
                  formatNumber(scope.row.balance)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="updated_at"
              align="center"
              :label="gL('creditTime')"
            >
            </el-table-column>
          </el-table>
          <div class="paging">
            <el-pagination
              @current-change="historyCurrentChange"
              :current-page.sync="historyCurrentPage"
              background
              layout="total, prev, pager, next"
              :total="historyTotalCount"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <div class="record" v-if="record">
        <div
          class="detail"
          :style="{ textAlign: gLang == 2 ? 'left' : 'right' }"
        >
          <div
            class="row"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
          >
            <div
              class="colmun"
              :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
            >
              <div class="colmun-item">{{ rowData.phone }}</div>
              <div class="colmun-item label">{{ gL("phoneNumber") }}</div>
            </div>
            <div
              class="colmun"
              :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
            >
              <div class="colmun-item">{{ rowData.created_at }}</div>
              <div class="colmun-item label">{{ gL("createdDate") }}</div>
            </div>
          </div>
          <div
            class="row"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
          >
            <div
              class="colmun"
              :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
            >
              <div class="colmun-item">{{ rowData.name_ug }}</div>
              <div class="colmun-item label">{{ gL("nameUg") }}</div>
            </div>
            <div
              class="colmun"
              :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
            >
              <div class="colmun-item">{{ rowData.name_zh }}</div>
              <div class="colmun-item label">{{ gL("nameZh") }}</div>
            </div>
          </div>
          <div
            class="row"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
          >
            <div
              class="colmun"
              :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
            >
              <div class="colmun-item">
                {{ formatNumber(rowData.credit_limit) }}
              </div>
              <div class="colmun-item label">{{ gL("creditLimit") }}</div>
            </div>
            <div
              class="colmun"
              :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
            >
              <div class="colmun-item" :style="{ color: '#ff9c00' }">
                {{ formatNumber(rowData.balance) }}
              </div>
              <div class="colmun-item label">{{ gL("creditPrice") }}</div>
            </div>
          </div>

          <div
            class="row"
            :style="{ flexDirection: gLang == 2 ? 'row' : 'row-reverse' }"
          >
            <div
              class="colmun"
              :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
            >
              <div class="colmun-item" :style="{ color: '#ff9c00' }">
                {{ formatNumber(rowData.credit_limit - rowData.balance) }}
              </div>
              <div class="colmun-item label">{{ gL("availableLimit") }}</div>
            </div>
            <div
              class="colmun"
              :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
            >
              <div class="colmun-item" v-if="rowData.status == 1">
                {{ gL("open") }}
              </div>
              <div class="colmun-item" v-if="rowData.status == 0">
                {{ gL("off") }}
              </div>
              <div class="colmun-item label">{{ gL("state") }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 模态框 -->
    <AddCreditDialog ref="addCreditDialog" @confirm="creditDialogConfirm" />

    <CreditRecharge
      ref="recharge"
      :number_box="rechargeBox"
      :creditData="rowData"
      @cancelRechargeBox="cancelRechargeBox"
      @recharge="recharge"
    ></CreditRecharge>

    <!-- 开班 -->
    <startJob :number_box="job_box" @startBox="startBox"></startJob>
  </div>
</template>

<script>
import CreditRecharge from "./components/CreditRecharge.vue";
import { debounce, formatNumber } from "./../../utils/utils.js";
import AddCreditDialog from "./components/AddCreditDialog.vue";
import startJob from "../../components/startJob.vue";
import {
  getCreditListAPI,
  psotCreditAddAPI,
  putCreditEditAPI,
  putCreditStatusAPI,
  getCreditHistoryAPI
} from "./../../api/index.js";
export default {
  components: {
    CreditRecharge,
    AddCreditDialog,
    startJob
  },
  activated: function() {
    this.serachText = ""; //搜索内容
    this.delete_text = false; //搜索框删除按钮
    this.gLang = localStorage.getItem("langId");
    this.currentPage = 1;
    this.historyCurrentPage = 1;
    this.gLang = localStorage.getItem("langId");
    this.getData();
    this.detail = false;
    this.record = true;
    this.recordType = 0;
  },
  data() {
    return {
      gLang: 1,
      serachText: "", // 搜索内容
      delete_text: false, // 搜索框删除按钮
      detail: false, // 挂账人详情
      topStatis: {}, // 挂账列表统计
      tableData: [], // 挂账人列表
      totalCount: 0, // 总条数
      currentPage: 1, // 当前页数
      rowData: {}, //行数据
      historyCurrentPage: 1, //消费记录当前页数
      historyTotalCount: 0, //消费记录总条数
      recordType: 0, //消费和充值
      recordTypeList: [], //消费和充值
      record: true, //是否消费记录
      recordList: [], //消费和充值列表
      tableLoading: false,

      rechargeBox: false, //VIP登录窗口
      addeditBox: false, //添加或编辑充值
      amout: "",
      formatNumber: formatNumber,
      job_box: false //交班窗口
    };
  },
  methods: {
    // 挂账人修改状态
    changeSwitch(value, row) {
      const loading = this.$loading();
      putCreditStatusAPI(row.id, { status: value })
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.$message({
              message: this.gL("successfulOperation"),
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
          }
        })
        .catch(() => {
          this.getData();
        })
        .finally(() => {
          loading.close();
        });
    },

    //搜索输入框输入时候
    inputSearch() {
      if (this.serachText.length > 11) {
        this.serachText = this.serachText.slice(0, 11);
        return;
      }
      if (this.serachText.length != 0) {
        this.delete_text = true;
      } else {
        this.delete_text = false;
      }
      debounce(this.getData, 1000);
    },
    //删除搜索内容
    removeSerach() {
      this.serachText = "";
      this.delete_text = false;
      this.getData();
    },
    //获取数据
    getData() {
      this.tableLoading = true;
      getCreditListAPI(this.currentPage, 10, this.serachText)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.tableData = response.data.data;
            this.totalCount = response.data.meta.total;
            this.topStatis = response.data.top;

            if (this.rowData) {
              for (let i = 0; i < response.data.data.length; i++) {
                if (this.rowData.id == response.data.data[i].id) {
                  this.rowData = response.data.data[i];
                }
              }
            }
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    // 分页请求
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData();
    },

    // 历史记录分页请求
    historyCurrentChange(val) {
      this.historyCurrentPage = val;
      this.getRecordList();
    },

    // 点击添加或编辑挂账人按钮
    addVipBoxs(e, s) {
      // 0 添加 1 和 2 编辑
      if (e == 0) {
        this.$refs.addCreditDialog.showDialog("add");
      } else if (e == 1) {
        this.rowData = s;
        this.$refs.addCreditDialog.showDialog("edit", this.rowData);
      } else if (e == 2) {
        this.$refs.addCreditDialog.showDialog("edit", this.rowData);
      }
    },

    // 添加或编辑挂账人
    creditDialogConfirm(isEdit, form) {
      if (!isEdit) {
        // 说明是添加
        const loading = this.$loading();
        psotCreditAddAPI(form)
          .then(response => {
            if (response.status >= 200 && response.status < 300) {
              this.$message({
                message: this.gL("successfulOperation"),
                type: "success",
                customClass: this.$toastClass(),
                offset: 120
              });
              this.getData();
              this.$refs.addCreditDialog.close();
            }
          })
          .finally(() => {
            loading.close();
          });
      } else {
        // 说明是编辑
        const loading = this.$loading();
        putCreditEditAPI(this.rowData.id, form)
          .then(response => {
            if (response.status >= 200 && response.status < 300) {
              this.$message({
                message: this.gL("successfulOperation"),
                type: "success",
                customClass: this.$toastClass(),
                offset: 120
              });
              this.getData();
              this.$refs.addCreditDialog.close();
            }
          })
          .finally(() => {
            loading.close();
          });
      }
    },

    // 显示详情页
    vipDetail(row, column) {
      if (column.property == "status") return;
      this.rowData = row;
      this.detail = true;
      this.record = true;
    },

    // 返回列表页
    backDetail() {
      this.detail = false;
    },

    //点击消费和详情
    recordClick(e) {
      if (e == 1) {
        this.record = false;
        this.getRecordList(0);
      } else {
        this.record = true;
      }
    },

    //消费充值列表
    getRecordList(type) {
      if (type == "change") {
        this.historyCurrentPage = 1;
      }
      console.log("type --- ", type);
      let query = `?page=${this.historyCurrentPage}&limit=10&holder_id=${this.rowData.id}`;
      if (this.recordType != 0) {
        query += `&type=${this.recordType}`;
      }
      this.tableLoading = true;
      getCreditHistoryAPI(query)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.recordList = response.data.data;
            this.historyTotalCount = response.data.meta.total;
            this.recordTypeList = response.data.types;
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    /**
     * 显示VIP充值登录窗口
     */
    openRechargeBox() {
      // if (s == 0) {
      //   this.rowData = e;
      // }
      // console.log(s);
      // if (s == 2) {
      //   this.addeditBox = true;
      //   this.$refs.recharge.getAmout();
      // }
      // console.log(this.rowData);
      this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      if (this.storeInfo.isHandOver) {
        this.job_box = true;
        return;
      }
      this.rechargeBox = true;
    },

    //关闭开班和开班成功
    startBox(e) {
      this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
      this.job_box = false;
    },

    /**
     * 关闭VIP充值登录窗口
     */
    cancelRechargeBox(e) {
      console.log(e);
      if (e) {
        this.getData();
      }
      this.rechargeBox = false;
    },
    //充值
    recharge() {
      this.getData();

      if (!this.record) {
        this.getRecordList();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .top {
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    background-color: #2e3033;
    color: #ffffff;
    height: 7%;
    align-items: center;
    border-bottom: 1px solid #666666;
    .serach {
      width: 250px;
      background: #4d4d4d;
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 80%;
      input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        width: 70%;
        background: #4d4d4d;
        &::placeholder {
          color: #cccccc;
        }
      }
      .search {
        color: #cccccc;
        font-size: 24px;
        padding: 0 10px;
        vertical-align: -2px;
      }
      .del {
        width: 57px;
        height: 100%;
        color: #cccccc;
        justify-content: center;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 22px;
        }
      }
    }
    .btn {
      background-color: #ff9c00;
      // width: 200px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      column-gap: 10px;
      padding: 0 25px;
      height: 80%;
      font-size: 24px;
      cursor: pointer;
      .iconfont {
        font-size: 19px;
      }
    }
    .detail-tit {
      font-size: 24px;
      display: flex;
      .menus {
        font-size: 22px;
        padding: 10px 20px;
        text-align: center;
        cursor: pointer;
      }
      .active {
        background: #139d59;
        color: #ffffff;
      }
    }
    .btns {
      display: flex;
      .btn-item {
        font-size: 22px;
        margin-left: 20px;
        padding: 10px 0;
        width: 130px;
        text-align: center;
        cursor: pointer;
      }
      .edit {
        background-color: #139d59;
      }
      .back {
        background-color: #ffffff;
        color: #1a1a1a;
      }
      .rechar {
        background-color: #ff9c00;
      }
    }
    .all {
      display: flex;
      font-size: 24px;
      width: 500px;
      justify-content: space-between;
      .price {
        color: #ff9c00;
      }
    }
    .select {
      padding: 10px 0;
    }
  }
  .paging {
    text-align: center;
    padding-top: 10px;
    position: fixed;
    right: 0;
    bottom: 10px;
  }
  .recharge {
    background: #139d59;
    color: #ffffff;
    padding: 9px 0;
    width: 110px;
    margin: 0 auto;
    cursor: pointer;
  }
  .detail {
    padding: 40px 25px;
    background-color: #f2f2f2;
    height: 92%;
    .row {
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      padding: 25px;
      .colmun {
        display: flex;
        width: 49%;
        font-size: 22px;
        .colmun-item {
          width: 60%;
        }
        .label {
          width: 40%;
          color: #666666;
        }
        .price {
          color: #ff9c00;
        }
        .integral {
          color: #139d59;
        }
      }
      &:nth-of-type(odd) {
        background-color: #ffffff;
      }
      &:nth-of-type(even) {
        background-color: #f5f5f5;
      }
    }
  }
  .rmb {
    color: #ff9c00;
  }
  .edits {
    color: #139d59;
  }
  .line {
    color: #fff;
    border-left: 1px solid #ccc;
    padding-right: 10px;
  }
}
</style>
