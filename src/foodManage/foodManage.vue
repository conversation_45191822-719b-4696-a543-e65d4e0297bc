<template>
  <div class="wrap" :class="gLang == 1 ? 'ug-box' : 'zh-box'">
    <div class="menus">
      <div class="box">
        <router-link
          class="menus-item"
          v-for="(item, index) in menus_list"
          :key="index"
          :to="item.key"
        >
          {{ item.name }}
        </router-link>
      </div>


    </div>
    <!-- 餐桌 -->
    <div class="table">
      <router-view></router-view>
      <!-- <keep-alive>
        <food ref="food" v-if="activeIndex == '/foodManage/food'"></food>
        <cate ref="cate" v-if="activeIndex == '/foodManage/category'"></cate>
        <food-all ref="foodAll" v-if="activeIndex == '/foodManage/allFood'"></food-all>
        <specPage ref="specPage" v-if="activeIndex == '/foodManage/spec'"></specPage>
        <addCharge ref="addCharge" v-if="activeIndex == '/foodManage/addCharge'"></addCharge>
      </keep-alive> -->
    </div>
  </div>
</template>

<script>

export default {
  components: {
  },
  activated: function() {
    this.gLang = localStorage.getItem("langId");
    this.activeIndex = "";
    this.addText = this.gL("addFood");
    this.activeIndex = this.$route.path;
  },
  data() {
    return {
      gLang: 1,
      menus_list: [
        {
          name: this.gL("myFood"),
          key: "/foodManage/food"
        },
        {
          name: this.gL("foodCate"),
          key: "/foodManage/category"
        },
        {
          name: this.gL("foodAll"),
          key: "/foodManage/all"
        },
        {
          name: this.gL("foodSpecTitle"),
          key: "/foodManage/spec"
        }
      ],
      activeIndex: "",
      addText: this.gL("addFood")
    };
  },
  methods: {
    //添加
    add() {
      alert("alim")
      // if (this.activeIndex == '/foodManage/food') {
      //   this.$refs.food.addFood();
      // } else {
      //   this.$refs.cate.addCate();
      // }
      // this.$router.push("/addFood");
    },
  }
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.empty {
  margin: 0 auto;
}
.wrap {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  //菜单和输入框
  .menus {
    display: flex;
    background-color: @bgColor;
    padding: 5px;
    font-size: 24px;
    color: #ffffff;
    justify-content: space-between;
    border-bottom: 1px solid @grayColor;
    .box {
      display: flex;
    }
    .menus-item {
      padding: 12px 40px;
      text-align: center;
      cursor: pointer;
    }
    .active-link {
      background-color: @greenColor;
      color: #ffffff;
    }
  }


  .table {
    height: 93%;
    flex: 1;
  }
}
/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

@-webkit-keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
  }
}
</style>
