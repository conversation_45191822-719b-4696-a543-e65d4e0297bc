<template>
  <div class="wraps food food-manage" :class="gLang == 1 ? 'ug-box' : 'zh-box'">
    <div
        class="adds"
        @click="addFood"
      >
        <span>{{ gL("addFood") }}</span>
        <span class="iconfont icon-jia-copy-copy"></span>
      </div>
    <div class="top topp">
      <div class="menu" :class="gLang == 2 ? 'menu-zh' : ''">
        <el-tabs
          v-model="activeName"
          type="card"
          class="menu-item"
          @tab-click="clickMenu"
        >
          <el-tab-pane
            v-for="item in tables"
            :key="item.id"
            :label="
              item.foods_count == undefined || Number.isNaN(item.foods_count)
                ? (gLang == 1 ? item.name_ug : item.name_zh)
                : (gLang == 1 ? item.name_ug : item.name_zh) + ' (' + item.foods_count + ')'
            "
            :name="item.item"
            :id="item.id"
          >
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="serach">
        <div class="input">
          <span class="iconfont icon-search search"></span>
          <input
            type="text"
            oninput="if(value.length>40)value=value.slice(0,40)"
            :placeholder="gL('shortcut') + '/' + gL('names')"
            v-model="serachText"
            v-on:input="inputSearch"
            @keyup.enter="inputSearch"
          />
        </div>
        <div class="del" v-if="delete_text" @click="removeSerach">
          <span class="iconfont icon-jia-copy"></span>
        </div>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        header-cell-class-name="headers"
        :header-cell-style="{ background: '#e6e6e6' }"
        :cell-style="cell"
        row-class-name="row"
        v-loading="tableLoading"
        style="width: 100%"
      >
        <el-table-column
          :label="gL('serialNumber')"
          type="index"
          align="center"
          width="60"
        >
        </el-table-column>
        <el-table-column align="center" width="80px" :label="gL('img')">
          <template slot-scope="scope">
            <img
              :src="scope.row.image + '?x-oss-process=style/w20'"
              width="50px"
              height="50px"
              class="food-image"
              alt=""
              style="border: 1px solid #cccccc"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" align="center" :label="gL('foodName')">
        </el-table-column>
        <el-table-column
          prop="shortcut_code"
          align="center"
          width="95px"
          :label="gL('shortcut')"
        >
        </el-table-column>
        <el-table-column
          prop="food_category_name"
          align="center"
          :label="gL('type')"
        >
        </el-table-column>
        <el-table-column
          prop="price"
          width="85px"
          align="center"
          :label="gL('price')"
        >
        </el-table-column>
        <el-table-column
          prop="vip_price"
          width="90px"
          align="center"
          :label="gL('vipPrice')"
        >
        </el-table-column>
        <el-table-column
          prop="cost_price"
          align="center"
          width="100px"
          :label="gL('costPrice')"
        >
        </el-table-column>
        <el-table-column
          prop="sort"
          align="center"
          width="80px"
          :label="gL('sort')"
        >
        </el-table-column>
        <el-table-column
          align="center"
          width="120"
          :label="gL('foodScanSwitch')"
        >
          <template slot-scope="scope">
            <el-switch
              active-color="#13ce66"
              inactive-color="#ff4949"
              v-model="scope.row.support_scan_order"
              @change="changeSwitchScanOrder(scope.$index, scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column align="center" width="70" :label="gL('state')">
          <template slot-scope="scope">
            <el-switch
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.state"
              @change="changeSwitch(scope.$index, scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          :label="gL('operation')"
          prop="dosome"
          align="center"
          width="120px"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="iconfont icon-shiliangzhinengduixiang"
              @click="editData(scope.row)"
              circle
            ></el-button>
            <span style="padding-right: 9px; padding-left: 15px">
              <span class="line"></span>
            </span>
            <el-button
              type="text"
              class="danger"
              @click="delData(scope.row)"
              icon="iconfont icon-qingkong"
              circle
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="mask" v-if="modal_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ modal_title }}</span>
          <span class="iconfont icon-jia-copy" @click="cancel(2)"></span>
        </div>

        <div class="content-box" ref="contentBox">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
            <div class="content" style="flex-direction: row">
              <div class="items">
                <div class="row">
                  <el-form-item prop="food_category_id">
                    <el-select
                      v-model="ruleForm.food_category_id"
                      :placeholder="gL('foodCate')"
                    >
                      <el-option
                        v-for="(item, index) in cate_list"
                        :key="index"
                        :label="gLang == 1 ? item.name_ug : item.name_zh"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="format_id">
                    <el-select
                      v-model="ruleForm.format_id"
                      :placeholder="gL('format')"
                    >
                      <el-option
                        v-for="(item, index) in format_list"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="name_ug" class="ug-input-box">
                    <!-- <el-input
                      v-model="ruleForm.name_ug"
                      :placeholder="gL('inputFoodNameUg')"
                      :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                      @input="getCategoryImage"
                      maxlength="40"
                    ></el-input> -->

                    <el-autocomplete
                      class="food-input"
                      :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                      v-model="ruleForm.name_ug"
                      :fetch-suggestions="querySearch"
                      :placeholder="gL('inputFoodNameUg')"
                      value-key="name_ug"
                      @input="getCategoryImage"
                      :trigger-on-focus="false"
                    ></el-autocomplete>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="name_zh">
                    <el-input
                      v-model="ruleForm.name_zh"
                      :placeholder="gL('inputFoodNameZh')"
                      :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                      maxlength="40"
                    ></el-input>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="shortcut_code">
                    <el-input
                      v-model.number="ruleForm.shortcut_code"
                      :placeholder="gL('inputShortcutCode')"
                      :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                      :maxlength="4"
                    ></el-input>
                  </el-form-item>
                </div>
              </div>
              <div class="items">
                <div class="row inp">
                  <div class="input-item">
                    <div class="lab">{{ gL("costPrice") }}</div>
                    <el-form-item prop="cost_price">
                      <div class="input">
                        <input
                          v-model="ruleForm.cost_price"
                          type="text"
                          maxlength="4"
                          class="number"
                          @input="testing('cost_price')"
                        />
                      </div>
                    </el-form-item>
                  </div>
                  <div class="input-item">
                    <div class="lab">{{ gL("price") }}</div>
                    <el-form-item prop="price">
                      <div class="input">
                        <input
                          v-model="ruleForm.price"
                          type="text"
                          maxlength="4"
                          class="number"
                          @input="parallel('price')"
                        />
                      </div>
                    </el-form-item>
                  </div>
                  <div class="input-item">
                    <div class="lab">{{ gL("vipPrice") }}</div>
                    <el-form-item prop="vip_price">
                      <div class="input">
                        <input
                          v-model="ruleForm.vip_price"
                          type="text"
                          maxlength="4"
                          class="number"
                          @input="testing('vip_price')"
                          @keyup.enter="confirm(2)"
                        />
                      </div>
                    </el-form-item>
                  </div>
                </div>
                <div class="row">
                  <el-form-item prop="sort">
                    <el-input
                      :placeholder="gL('sort')"
                      v-model="ruleForm.sort"
                      type="number"
                      oninput="if(value.length>3)value=value.slice(0,3)"
                    >
                      <template slot="append">{{ gL("sort") }}</template>
                    </el-input>
                  </el-form-item>
                </div>
                <div class="row type">
                  <el-form-item prop="image">
                    <div class="img">
                      <img
                        :src="ruleForm.image ? ruleForm.image : defaultImage"
                        alt=""
                      />
                    </div>
                  </el-form-item>
                  <div class="checks">
                    <div
                      class="check"
                      :class="ruleForm.state == 0 ? 'active' : ''"
                      @click="ruleForm.state = 0"
                    >
                      {{ gL("off") }}
                    </div>
                    <div
                      class="check"
                      :class="ruleForm.state == 1 ? 'active' : ''"
                      @click="ruleForm.state = 1"
                    >
                      {{ gL("open") }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="content image">
              <div class="img-item">
                <el-upload
                  class="avatar-uploader"
                  :action="url"
                  name="image"
                  list-type="text"
                  :data="imageData"
                  :headers="imageHeader"
                  :on-success="handleAvatarSuccess"
                  :on-error="handleAvatarError"
                  :before-upload="() => (uploadLoading = $loading())"
                >
                  <!-- <img
                  v-if="ruleForm.image"
                  :src="ruleForm.image"
                  class="avatar"
                /> -->
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </div>
              <swiper
                :options="swiperOption"
                :class="gLang == 1 ? 'ug-swiper' : 'zh-swiper'"
              >
                <swiper-slide
                  v-for="(item, index) in edit_images.length
                    ? edit_images
                    : img_list"
                  :key="index"
                >
                  <div
                    class="img-item"
                    :class="{ 'img-active': item == ruleForm.image }"
                    :key="index"
                    @click="imgClick1(item)"
                    id="img-item"
                  >
                    <img :src="item + '?x-oss-process=style/w20'" alt="" />
                  </div>
                </swiper-slide>
                <div class="swiper-scrollbar" slot="scrollbar"></div>
              </swiper>
            </div>
          </el-form>

          <!-- 添加团购 -->
          <div class="content">
            <el-switch
              v-model="isGroupBuy"
              @change="changeGroupBuy"
              :active-text="gL('groupBuyText')"
            >
            </el-switch>
          </div>

          <div
            class="content group-buy-container"
            v-if="isGroupBuy && groupBuyList.length"
          >
            <div
              class="group-buy-item"
              v-for="(item, index) in groupBuyList"
              :key="index"
            >
              <div class="close-icon" @click="deleteGroupBuy(index)">
                <i class="el-icon-circle-close-outline"></i>
              </div>

              <el-row class="row" type="flex" justify="space-between">
                <el-col :span="11">
                  <el-input
                    v-model="item.name_ug"
                    :placeholder="gL('inputFoodNameUg')"
                    :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                    maxlength="40"
                  ></el-input>
                </el-col>
                <el-col :span="11">
                  <el-input
                    v-model="item.name_zh"
                    :placeholder="gL('inputFoodNameZh')"
                    :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                    maxlength="40"
                  ></el-input>
                </el-col>
              </el-row>
              <el-row class="row">
                <el-col :span="24" class="add-food-btn-box">
                  <div class="add-food-btn">
                    <el-button type="primary" @click="addGroupBuyFood(index)"
                      >{{ gL("addGroupBuyText")
                      }}<i class="el-icon-upload2"></i>
                    </el-button>
                  </div>
                  <div class="tag-box">
                    <el-tag
                      v-for="tag in item.combo_food_id"
                      :key="tag.id"
                      closable
                      type="success"
                      @close="closeTagHandler(tag, index)"
                    >
                      {{ gLang == 1 ? tag.name_ug : tag.name_zh }}
                    </el-tag>
                  </div>
                </el-col>
              </el-row>
              <el-row class="row" type="flex" justify="space-between">
                <el-col :span="11">
                  <el-input
                    v-model="item.combo_count"
                    :placeholder="gL('inputGroupBuyCount')"
                    :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                    type="number"
                  ></el-input>
                </el-col>
                <el-col :span="11">
                  <el-input
                    v-model="item.combo_price"
                    :placeholder="gL('inputGroupBuyPrice')"
                    :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                    type="number"
                  ></el-input>
                </el-col>
              </el-row>
            </div>

            <div class="add-group-btn-box">
              <el-button type="primary" @click="addGroupBuy"
                >{{ gL("addNewGroupBuy") }}
                <i
                  class="el-icon-circle-plus-outline
"
                ></i
              ></el-button>
            </div>
          </div>
        </div>

        <div class="adds">
          <div class="btn add-btn" @click="confirm(1)" v-if="addBox">
            {{ gL("continueAdd") }}
          </div>
          <div class="btn" @click="confirm(2)" :class="!addBox ? 'edit' : ''">
            {{ gL("confirm") }}
          </div>
        </div>
      </div>
    </div>
    <div class="mask" v-if="img_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ modal_title }}</span>
          <span class="iconfont icon-jia-copy" @click="cancelImg"></span>
        </div>
        <div class="content image">
          <div class="img-item">
            <el-upload
              class="avatar-uploader"
              :action="url"
              name="image"
              :data="imageData"
              :headers="imageHeader"
              :on-success="handleAvatarSuccess"
              :on-error="handleAvatarError"
              :before-upload="() => (uploadLoading = $loading())"
            >
              <img v-if="ruleForm.image" :src="ruleForm.image" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
          <!--          <div-->
          <!--            class="img-item"-->
          <!--            v-for="item in img_list"-->
          <!--            :key="item.id"-->
          <!--            @click="imgClick(item.image_url,item.id)"-->
          <!--            id="img-item"-->
          <!--            :identifier="'r' + item.id"-->
          <!--          >-->
          <!--            <img :src="item.image_url" alt="" />-->
          <!--          </div>-->
        </div>
        <div class="btn" @click="confirmImg">
          {{ gL("confirm") }}
        </div>
      </div>
    </div>
    <modal
      :number_box="confirm_box"
      :modal_content="modal_content"
      :modal_title="modals_title"
      @cancel="cancels"
      @confirm="confirmBox"
    ></modal>

    <!-- 选择美食弹出框 -->
    <div class="add-food-modal">
      <el-dialog
        :title="gL('addFoodModalTitle')"
        :visible.sync="showFoodModal"
        width="50%"
        center
      >
        <div
          class="menu add-food-modal-menu"
          :class="gLang == 1 ? 'ug-modal' : 'zh-modal'"
        >
          <el-tabs
            v-model="foodModalActiveName"
            type="card"
            class="menu-item"
            @tab-click="clickFoodModalMenu"
          >
            <el-tab-pane
              v-for="item in tables"
              :key="item.id"
              :label="
                item.foods_count == undefined || Number.isNaN(item.foods_count)
                  ? (gLang == 1 ? item.name_ug : item.name_zh)
                  : (gLang == 1 ? item.name_ug : item.name_zh) + ' (' + item.foods_count + ')'
              "
              :name="item.item"
              :id="item.id"
            >
            </el-tab-pane>
          </el-tabs>

          <div class="check-box">
            <el-checkbox
              v-if="foodModalActiveName == 'first'"
              :indeterminate="isFoodModalIndeterminate"
              v-model="checkAll"
              @change="handleCheckAllChange"
              >{{ gL("chooiseAll") }}</el-checkbox
            >
            <div style="margin: 15px 0;"></div>
            <el-checkbox-group
              v-model="checkedFoods"
              @change="handleCheckedFoodsChange"
            >
              <template v-for="food in foodList">
                <el-checkbox :label="food.id">{{
                  gLang == 1 ? food.name_ug : food.name_zh
                }}</el-checkbox>
              </template>
            </el-checkbox-group>
          </div>
        </div>

        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelAddFoodModal">{{ gL("cancel") }}</el-button>
          <el-button type="primary" @click="confirmAddFoodModal">{{
            gL("confirm")
          }}</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import modal from "../components/modal.vue";
import {
  putFoodStateAPI,
  getFoodDefaultImageAPI,
  getLibraryFoodsImageAPI,
  getFoodListAPI,
  getFoodsFormatAPI,
  // getFoodCategoriesAPI,
  getFoodCategoriesListAPI,
  postFoodAddAPI,
  putFoodUpdateAPI,
  deleteFoodAPI,
  getLibraryFoodsListAPI,
  getChangeSupportScanOrderAPI
} from "@/api/index.js";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import { debounce } from "./../utils/utils.js";
import "swiper/dist/css/swiper.css";
var self;
export default {
  created: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.activeName = "first";
    self.activeId = "";
    self.ruleForm.food_category_id = "";
    self.serachText = ""; //搜索内容
    self.delete_text = ""; //搜索框删除按钮
    self.getData();
    self.getList();
    self.formatList();
    self.url =
      "https://" + localStorage.getItem("ip_adr") + "/api/v1/upload/image";
  },

  data() {
    return {
      uploadLoading: null,
      swiperOption: {
        scrollbar: {
          el: ".swiper-scrollbar"
        },
        freeMode: true,
        width: 200
      },
      isGroupBuy: false,
      groupBuyList: [],
      showFoodModal: false,
      foodModalActiveName: "first",
      isFoodModalIndeterminate: true,
      checkAll: false,
      checkedFoods: [],
      foodTempList: [],
      foodList: [],
      modalFoodIndex: NaN,
      edit_images: [],
      name: "name",
      gLang: 1,
      tableData: [],
      serachText: "", //搜索内容
      delete_text: "", //搜索框删除按钮
      activeId: "",
      modal_box: false,
      tables: [],
      cate_list: [],
      modal_title: this.gL("addFood"),
      modal_content: "",
      modals_title: "",
      defaultImage: require("../../static/images/defau.png"),
      ruleForm: {
        state: 1,
        food_category_id: "",
        shortcut_code: "",
        cost_price: "",
        vip_price: "",
        price: "",
        name_zh: "",
        name_ug: "",
        image: "",
        sort: "",
        format_id: 1
      },
      url: "",
      rules: {
        format_id: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" }
        ],
        sort: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" }
        ],
        shortcut_code: [
          {
            required: true,
            message: this.gL("inputShortcutCode"),
            trigger: "blur"
          },
          { type: "number", message: this.gL("inputShortcutCodeNumber") }
        ],
        food_category_id: [
          {
            required: true,
            message: this.gL("selectFoodType"),
            trigger: "change"
          }
        ],
        name_zh: [
          {
            required: true,
            message: this.gL("inputFoodNameZh"),
            trigger: "blur"
          }
        ],
        name_ug: [
          {
            required: true,
            message: this.gL("inputFoodNameUg"),
            trigger: "blur"
          }
        ],
        cost_price: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" }
        ],
        vip_price: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" }
        ],
        price: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" }
        ],
        image: [
          { required: true, message: this.gL("uploadImg"), trigger: "blur" }
        ]
      },
      imageData: {
        folder: "foods"
      },
      imageHeader: {
        Authorization: "Bearer " + localStorage.getItem("token"),
        MerchantNo: JSON.parse(localStorage.getItem("merchant_info")).merchant_no,
        "accept-language": localStorage.langId == 1 ? "ug-CN" : "zh-CN"
      },
      addBox: true,
      activeName: "first",
      img_box: false,
      confirm_box: false,
      img_list: [],
      defaultImg: 'this.src="' + require("../../static/images/defau.png") + '"', //默认图片
      format_list: [], //按斤按数量
      tableLoading: false
    };
  },
  methods: {
    // 扫码点菜小程序下单switch
    changeSwitchScanOrder(index, row) {
      getChangeSupportScanOrderAPI(row.id)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
          }
        })
        .catch(err => {
          this.getData();
        });
    },

    // 删除选择的tag
    closeTagHandler(tag, index) {
      const item = this.groupBuyList[index].combo_food_id;
      for (let i = 0; i < item.length; i++) {
        if (item[i].id == tag.id) {
          this.groupBuyList[index].combo_food_id.splice(i, 1);
        }
      }
    },

    // 取消选择美食弹出框
    cancelAddFoodModal() {
      this.showFoodModal = false;
      this.foodModalActiveName = "first";
      this.isFoodModalIndeterminate = true;
      this.checkAll = false;
      this.checkedFoods = [];
      this.modalFoodIndex = NaN;
    },

    // 确定选择美食弹出框
    confirmAddFoodModal() {
      // this.groupBuyList[this.modalFoodIndex].combo_food_id = this.checkedFoods.
      const list = [];
      for (let i = 0; i < this.checkedFoods.length; i++) {
        for (let j = 0; j < this.foodTempList.length; j++) {
          if (this.checkedFoods[i] == this.foodTempList[j].id) {
            list.push(this.foodTempList[j]);
          }
        }
      }
      this.groupBuyList[this.modalFoodIndex].combo_food_id = list;
      this.cancelAddFoodModal();
    },

    // 美食列表多选框单选美食
    handleCheckedFoodsChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.foodTempList.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.foodTempList.length;
    },

    // 美食列表多选框全选
    handleCheckAllChange(val) {
      this.checkedFoods = val ? this.foodList.map(item => item.id) : [];
      this.isFoodModalIndeterminate = false;
    },

    // 点击添加美食列表
    clickFoodModalMenu(e) {
      if (e.$attrs.id == 0) {
        this.foodList = this.foodTempList;
        return;
      }
      this.foodList = this.foodTempList.filter(item => {
        return item.food_category_id == e.$attrs.id;
      });
    },

    // 点击添加美食按钮
    addGroupBuyFood(index) {
      this.showFoodModal = true;
      this.foodList = this.foodTempList;
      this.modalFoodIndex = index;
      this.checkedFoods = this.groupBuyList[index].combo_food_id.map(
        item => item.id
      );
      console.log("this.checkedFoods", this.checkedFoods);
    },

    // 点击删除团购按钮
    deleteGroupBuy(index) {
      if (this.groupBuyList.length == 1) {
        this.$message({
          message: this.gL("cancelGroupBuyTips"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
        return;
      }
      this.groupBuyList.splice(index, 1);
    },

    // 点击添加新团购按钮
    addGroupBuy() {
      const check = this.checkGroupBuy();
      if (!check) return;
      this.groupBuyList.push({
        name_ug: "",
        name_zh: "",
        combo_food_id: [],
        combo_count: "",
        combo_price: ""
      });
    },

    // 验证团购数据是不是填
    checkGroupBuy() {
      for (let i = 0; i < this.groupBuyList.length; i++) {
        console.log("i", i, this.groupBuyList[i]);
        for (let value in this.groupBuyList[i]) {
          console.log("value", this.groupBuyList[i][value]);
          if (
            !this.groupBuyList[i][value] ||
            this.groupBuyList[i][value].length == 0
          ) {
            this.$message({
              message: this.gL(`groupBuy_${value}_tips`),
              type: "warning",
              customClass: self.$toastClass(),
              offset: 120
            });
            return false;
          }
        }
      }
      return true;
    },

    // 启用团购按钮
    changeGroupBuy() {
      if (this.isGroupBuy) {
        this.groupBuyList.push({
          name_ug: "",
          name_zh: "",
          combo_food_id: [],
          combo_count: "",
          combo_price: ""
        });
        this.$nextTick(() => {
          this.$refs.contentBox.scrollTo({
            top: 1000,
            behavior: "smooth"
          });
        });
      } else {
        this.groupBuyList = [];
      }
    },

    // 获取添加美食或编辑美食时现有图片列表
    getImageList() {
      if (!this.img_list.length) {
        getFoodDefaultImageAPI().then(response => {
          this.img_list = response.data.data.map(item => item.image_url);
        });
      }
    },
    cell({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 2) {
        if (self.gLang == 1) {
          return "direction: rtl";
        }
      }
    },
    //添加
    addFood() {
      this.modal_box = true;
      this.addBox = true;
      this.modal_title = this.gL("addFood");
      this.ruleForm.format_id = 1;
      this.getImageList();
      if (this.tableData.length > 0) {
        this.ruleForm.sort =
          Math.max.apply(
            null,
            this.tableData.map(function(o) {
              return o.sort;
            })
          ) + 1;
      }
    },

    getCategoryImage() {
      debounce(() => {
        console.log("getCategoryImage");
        getLibraryFoodsImageAPI({
          word: this.ruleForm.name_ug
        }).then(response => {
          console.log("response1", response);
          if (response.status >= 200 && response.status < 300) {
            self.edit_images = response.data.data.images;
          }
        });
      });
    },
    querySearch(value, callback) {
      const param = {
        page: 1,
        q: value
      };
      debounce(() => {
        getLibraryFoodsListAPI(param).then(response => {
          if (response.status >= 200 && response.status < 300) {
            console.log(response.data.data.data, "ssss");
            callback(response.data.data.data);
          }
        });
      }, 500);
    },

    //搜索输入框输入时候
    inputSearch() {
      if (self.serachText.length != 0) {
        self.delete_text = true;
      } else {
        self.delete_text = false;
      }
      self.getData();
    },
    //删除搜索内容
    removeSerach() {
      self.serachText = "";
      self.delete_text = false;
      self.getData();
    },
    //点击点菜
    clickMenu(e) {
      self.activeId = e.$attrs.id;
      if (e.$attrs.id != 0) {
        self.ruleForm.food_category_id = e.$attrs.id;
      }
      self.getData();
    },
    //获取数据
    getData() {
      var data = {};
      if (self.activeId != "") {
        data.food_category_id = self.activeId;
      }
      if (self.serachText != "") {
        data.keyword = self.serachText;
      }
      this.tableLoading = true;
      getFoodListAPI(data)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.tableData = response.data.data;
            if (!this.foodTempList.length || !this.activeId) {
              this.foodTempList = response.data.data;
            }
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    formatList() {
      self.format_list = [];
      getFoodsFormatAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.format_list = response.data.data;
        }
      });
    },
    //获取角色列表
    getList() {
      self.cate_list = [];
      getFoodCategoriesListAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          var count = 0;
          response.data.data.forEach(item => {
            count += item.foods_count;
          });
          var all = {
            name_ug: self.gL("all"),
            name_zh: self.gL("all"),
            item: "first",
            id: 0,
            foods_count: count
          };
          response.data.data.unshift(all);
          self.tables = response.data.data;
          response.data.data.forEach(item => {
            if (item.id != 0) {
              self.cate_list.push(item);
            }
          });
        }
      });
    },
    //开关
    changeSwitch(index, row) {
      putFoodStateAPI(row.id)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
          }
        })
        .catch(err => {
          this.getData();
        });
    },
    //编辑
    editData(row) {
      this.id = row.id;
      this.addBox = false;
      this.modal_box = true;
      this.ruleForm.shortcut_code =
        row.shortcut_code == "" ? "" : parseInt(row.shortcut_code);
      this.ruleForm.food_category_id = row.food_category_id;
      this.ruleForm.cost_price = row.cost_price;
      this.ruleForm.vip_price = row.vip_price;
      this.ruleForm.state = row.state;
      this.ruleForm.price = row.price;
      this.ruleForm.name_zh = row.name_zh;
      this.ruleForm.name_ug = row.name_ug;
      this.ruleForm.sort = row.sort;
      this.ruleForm.image = row.image;
      this.ruleForm.format_id = row.format_id;
      this.modal_title = this.gL("editFood");
      this.isGroupBuy = row.combos.length ? true : false;
      this.groupBuyList = row.combos.map(item => {
        return {
          name_ug: item.name_ug,
          name_zh: item.name_zh,
          combo_count: item.count,
          combo_food_id: item.childs.map(item => ({
            ...item,
            combo_id: item.id,
            id: item.food_id
          })),
          combo_price: item.combo_price
        };
      });

      this.getImageList();
    },

    cancel(e) {
      let sort = "";
      if (e == 2) {
        this.modal_box = false;
      } else {
        sort = this.ruleForm.sort + 1;
      }

      this.ruleForm.state = 1;
      this.ruleForm.shortcut_code = "";
      this.ruleForm.cost_price = "";
      this.ruleForm.vip_price = "";
      this.ruleForm.price = "";
      this.ruleForm.name_zh = "";
      this.ruleForm.name_ug = "";
      this.ruleForm.image = "";
      this.ruleForm.sort = sort;
      this.ruleForm.format_id = 1;

      this.groupBuyList = [];
      this.isGroupBuy = false;
    },
    confirm(e) {
      if (this.addBox) {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            const check = this.checkGroupBuy();
            if (!check) return;
            let data = {
              ...this.ruleForm,
              shortcut_code: this.ruleForm.shortcut_code.toString(),
              is_combo: 0
            };

            if (this.isGroupBuy) {
              let foodList = [];
              for (let i = 0; i < this.groupBuyList.length; i++) {
                foodList.push({
                  ...this.groupBuyList[i],
                  combo_food_id: this.groupBuyList[i].combo_food_id
                    .map(item => item.id)
                    .join(",")
                });
              }
              data.is_combo = 1;
              data.combo_foods = foodList;
            }
            postFoodAddAPI(data).then(response => {
              if (response.status >= 200 && response.status < 300) {
                self.$message({
                  message: self.gL("successfulOperation"),
                  type: "success",
                  customClass: self.$toastClass(),
                  offset: 120
                });
                self.getData();
                self.getList();
                self.cancel(e);
              }
            });
          } else {
            return false;
          }
        });
      } else {
        self.$refs.ruleForm.validate(valid => {
          if (valid) {
            const check = this.checkGroupBuy();
            if (!check) return;
            let data = {
              ...this.ruleForm,
              shortcut_code: this.ruleForm.shortcut_code.toString(),
              is_combo: 0
            };

            if (this.isGroupBuy) {
              let foodList = [];
              for (let i = 0; i < this.groupBuyList.length; i++) {
                foodList.push({
                  ...this.groupBuyList[i],
                  combo_food_id: this.groupBuyList[i].combo_food_id
                    .map(item => item.id)
                    .join(",")
                });
              }
              data.is_combo = 1;
              data.combo_foods = foodList;
            }
            putFoodUpdateAPI(self.id, data).then(response => {
              if (response.status >= 200 && response.status < 300) {
                self.$message({
                  message: self.gL("successfulOperation"),
                  type: "success",
                  customClass: self.$toastClass(),
                  offset: 120
                });
                self.getData();
                self.cancel(e);
              }
            });
          } else {
            return false;
          }
        });
      }
    },
    //删除
    delData(row) {
      self.id = row.id;
      self.confirm_box = true;
      if (self.gLang == 1) {
        self.modal_content =
          "《" +
          row.name +
          "》" +
          self.gL("confirs") +
          self.gL("confirmdelete");
      } else {
        self.modal_content =
          self.gL("confirmdelete") +
          "《" +
          row.name +
          "》" +
          self.gL("confirs");
      }
      self.modals_title = self.gL("tips");
    },
    cancels() {
      self.confirm_box = false;
    },
    confirmBox() {
      self.cancels();
      self.delRowData();
    },
    delRowData() {
      deleteFoodAPI(this.id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
          self.getData();
        }
      });
    },
    cancelImg() {
      self.img_box = false;
      self.ruleForm.image = "";
    },
    confirmImg() {
      self.img_box = false;
    },
    openImg() {
      self.img_box = true;
      getFoodDefaultImageAPI().then(response => {
        self.img_list = response.data.data;
        console.log(" self.img_list", self.img_list);
      });
    },
    imgClick(e, v) {
      console.log("v", v);
      console.log("e", e);
      self.ruleForm.image = e;
      // let img = document.querySelector("#img-item");
      // console.log(img);
      // img.classList.add("img-active");
      let imgs = document.querySelectorAll("#img-item1");
      imgs.forEach(element => {
        element.classList.remove("img-active");
      });
      let img = document.querySelector("#img-item1[identifier=r" + v + "]");
      img.classList.add("img-active");
    },

    imgClick1(image) {
      console.log("image", image);
      self.ruleForm.image = image;
    },
    handleAvatarSuccess(res, file) {
      if (res.message) {
        self.ruleForm.image = res.message;
        self.img_box = false;
      } else {
        self.$message({
          message: self.gL("uploadError"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
      this.uploadLoading.close();
    },
    handleAvatarError(err) {
      try {
        const messages = JSON.parse(err.message);
        if (messages && messages.message) {
          this.$message({
            message: messages.message,
            type: "warning",
            customClass: this.$toastClass(),
            offset: 120
          });
        }
        this.uploadLoading.close();
      } catch (error) {
        this.$message({
          message: this.gL("error"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        this.uploadLoading.close();
      }
    },
    //输入售价，同时填充会员价
    parallel(e) {
      self.testing(e);
      self.ruleForm.vip_price = self.ruleForm.price;
    },
    //验证价格
    testing(e) {
      self.ruleForm[e] = self.ruleForm[e].replace(/[^\d.]/g, ""); //先把非数字的都替换掉，除了数字和.
      self.ruleForm[e] = self.ruleForm[e].replace(/^\./g, ""); //必须保证第一个为数字而不是.
      self.ruleForm[e] = self.ruleForm[e]
        .replace(".", "$#$")
        .replace(/\./g, "")
        .replace("$#$", "."); //保证.只出现一次，而不能出现两次以上
    }
  },
  components: {
    modal,
    swiper,
    swiperSlide
  }
};
</script>

<style lang="less">
.food-manage {
  .el-upload-list {
    display: none;
  }

  .food-manage .el-icon-upload2 {
    padding: 0px 6px;
  }

  .group-buy-container .group-buy-item .tag-box .el-tag {
    height: 41px;
    line-height: 41px;
  }

  .add-food-modal-menu {
    .menu-item .el-tabs__nav-next {
      background-color: #139d59;
    }
    .menu-item .el-tabs__nav-prev {
      background-color: #139d59;
    }
    .el-tabs__nav {
      border: none;
    }
    .el-tabs__nav .el-tabs__item {
      border-left: none;
    }
    .el-checkbox__input {
      margin-inline: 6px;
    }
    .el-checkbox {
      width: 33.333%;
      padding: 5px 0px;
      margin: 0;
    }
    .el-checkbox-group {
      max-height: 300px;
      overflow-y: auto;
    }
  }
}

.content-box .content .items .ug-input-box .food-input input::placeholder {
  direction: rtl;
  text-align: right;
}

.el-autocomplete-suggestion {
  direction: rtl;
}
</style>

<style lang="less" scoped>
.img-active {
  border: 3px solid #139d59 !important;
}

.paging {
  text-align: center;
  padding-top: 10px;
  position: fixed;
  right: 0;
  bottom: 10px;
}

.content-box {
  max-height: 620px;
  overflow-y: auto;
  padding-bottom: 10px;
}

.content-box .content .items .food-input {
  width: 100%;
}

.content-box .content .items .ug-input-box {
  direction: rtl;
}

.group-buy-container {
  flex-direction: column;
  .group-buy-item {
    width: 100%;
    padding: 40px 20px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    position: relative;
    margin-bottom: 20px;
    .close-icon {
      position: absolute;
      right: 2px;
      top: 2px;
      cursor: pointer;
      color: #139d59;
    }
    .add-food-btn-box {
      display: flex;
      justify-content: flex-start;
      flex-direction: row-reverse;
      .tag-box {
        padding: 0px 10px;
        display: flex;
        flex-direction: row-reverse;
        flex-wrap: wrap;
        column-gap: 10px;
        row-gap: 10px;
      }
    }
  }
  .add-group-btn-box {
    text-align: center;
    margin-top: 10px;
    width: 100%;
  }
}

.food-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
}

.sssd {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #cccccc;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #1e1e1e;
  display: inline-block;
  font-size: inherit;
  height: 40px;
  line-height: 40px;
  outline: none;
  padding: 0 15px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
.img-active::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-top: 30px solid #139d59;
  border-right: 30px solid transparent;
}
.img-active::after {
  content: "✔";
  position: absolute;
  left: 0;
  top: 0;
  font-size: 20px;
  transform: rotate(18deg);
  color: #fff;
}

.menu {
  width: 82%;
  display: flex;
  color: #ffffff;
  height: 100%;
  // overflow-x: scroll;
  .menu-item {
    width: 100%;
    height: 100%;
    // padding: 0 25px;
    // line-height: 40px;
    font-size: 26px;
    cursor: pointer;
  }
  .active {
    background-color: #139d59;
  }
}

.add-food-modal-menu {
  flex-direction: column;
  width: 100%;
  .check-box {
    padding: 20px;
  }
  &.ug-modal .check-box {
    direction: rtl;
  }
  &.zh-modal .check-box {
    direction: ltr;
  }
}

.wraps {
  width: 100%;
  height: 100%;
  position: relative;
  >.adds {
    padding: 0px 36px;
    color: #fff;
    font-size: 22px;
    background: #ff9c00;
    cursor: pointer;
    display: inline-block;
    justify-content: center;
    height: 48px;
    line-height: 48px;
    position: absolute;
    top: -53px;
    right: 5px;
    .iconfont {
      font-size: 18px;
    }
  }
  .top {
    height: 50px;
    width: 100%;
    background-color: #2e3033;
    display: flex;
    justify-content: space-between;
    .serach {
      width: 280px;
      background: #4d4d4d;
      margin: 5px 15px 5px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .input {
        //  width: 70%;
        display: flex;
        align-items: center;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        width: 70%;
        background: #4d4d4d;
        &::placeholder {
          color: #cccccc;
        }
      }
      .search {
        color: #cccccc;
        font-size: 24px;
        padding: 0 10px;
      }
      .del {
        width: 20%;
        height: 100%;
        color: #cccccc;
        justify-content: center;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 22px;
        }
      }
    }
  }
  .table {
    height: 93%;
    overflow-y: scroll;
  }
  //s提示框
  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .box {
      background-color: #ffffff;
      width: 920px;
      font-size: 30px;
      -webkit-animation: fadelogIn 0.4s;
      animation: fadelogIn 0.4s;
      .title {
        background-color: #e6e6e6;
        color: #1a1a1a;
        padding: 25px 20px;
        position: relative;
        text-align: center;
        .iconfont {
          position: absolute;
          right: 20px;
          font-size: 23px;
          color: #666666;
          cursor: pointer;
        }
      }
      .row {
        margin-bottom: 25px;
      }
      .content {
        padding: 50px 75px 0 75px;
        display: flex;
        justify-content: space-between;
        .items {
          width: 48%;
          .inp {
            display: flex;
            font-size: 20px;
            border: 1px solid #cccccc;
            border-radius: 4px;
            .input-item {
              width: 33.333333%;
              border-right: 1px solid #cccccc;
              &:last-child {
                border-right: none;
              }
              input {
                width: 100%;
                outline: none;
                text-align: center;
              }
              .input {
                text-align: center;
                padding: 7px 0;
                font-size: 20px;
              }
              .lab {
                text-align: center;
                padding: 13px 0;
                background: #f2f2f2;
                border-bottom: 1px solid #cccccc;
              }
            }
          }
          .type {
            display: flex;
            justify-content: space-between;
            .img {
              width: 100%;
              background-image: url("../../static/images/defau.png");
              background-size: 100% 100%;
              cursor: pointer;
              border: 1px solid #cccccc;
              overflow: hidden;
              height: 120px;
              img {
                width: 170px;
                height: 120px;
              }
            }
            .checks {
              width: 48%;
              .check {
                width: 100%;
                border: 1px solid #cccccc;
                color: #666666;
                font-size: 18px;
                text-align: center;
                cursor: pointer;
                margin-bottom: 30px;
                height: 45px;
                line-height: 45px;
                border-radius: 3px;
                &:last-child {
                  margin-bottom: 0;
                }
              }
              .active {
                background-color: #139d59;
                color: #fff;
              }
            }
          }
        }
      }
      .image {
        padding-top: 0;
        .img-item {
          width: 170px;
          height: 120px;
          margin-right: 22px;
          cursor: pointer;
          border: 1px solid #999;
          position: relative;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .swiper-container {
        .swiper-slide {
          display: flex;
          justify-content: center;
          .img-item {
            margin: 0;
          }
        }
        // &.ug-swiper .swiper-slide {
        //   margin-right: 22px;
        // }

        // &.zh-swiper .swiper-slide {
        //   margin-right: 22px;
        // }
      }
      .adds {
        display: flex;
        margin: 20px 75px 20px 75px;
        justify-content: space-between;
        .add-btn {
          background-color: #ff9c00;
        }
        .btn {
          width: 48%;
          margin: 0;
        }
        .edit {
          width: 100%;
        }
      }
      .btn {
        background: #139d59;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        padding: 15px 0;
        cursor: pointer;
        margin: 20px 75px 20px 75px;
      }
    }
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 170px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}
.avatar {
  width: 170px !important;
  height: 120px !important;
  display: block;
}
/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

@-webkit-keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
  }
}

.food-image {
  object-fit: cover;
}

</style>
