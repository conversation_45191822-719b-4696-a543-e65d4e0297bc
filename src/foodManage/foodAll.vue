<template>
  <div class="wraps food">
    <div class="top topp">
      <div class="serach">
        <div class="input">
          <span class="iconfont icon-search search"></span>
          <input
              type="text"
              oninput="if(value.length>40)value=value.slice(0,40)"
              :placeholder="gL('shortcutCode') + '/' + gL('names') "
              v-model="serachTextcat"
              value=""
              v-on:input="inputSearch_Canteen_inventory"
              @keyup.enter="inputSearch_Canteen_inventory"
          />
        </div>
        <div class="del" v-if="delete_text_cart" @click="removeSerach">
          <span class="iconfont icon-jia-copy"></span>
        </div>
      </div>
      <div class="menu" :class="gLang == 2 ? 'menu-zh' : ''">
        <el-tabs
          v-model="activeName"
          type="card"
          class="menu-item"
          @tab-click="clickMenu"
        >
          <el-tab-pane
            v-for="item in tables"
            :key="item.id"
            :label="gLang == 1 ? item.name_ug : item.name_zh"
            :id="item.id"
          >
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="serach">
        <div class="input">
          <span class="iconfont icon-search search"></span>
          <input
            type="text"
            oninput="if(value.length>40)value=value.slice(0,40)"
            :placeholder="gL('shortcutCode') + '/' + gL('names')"
            v-model="serachText"
            v-on:input="inputSearch"
            @keyup.enter="inputSearch"
          />
        </div>
        <div class="del" v-if="delete_text" @click="removeSerach">
          <span class="iconfont icon-jia-copy"></span>
        </div>
      </div>
     </div>
    <div class="body">
      <div class="table">
        <el-table
          height="100%"
          class="foods-table"
          :data="tableData"
          header-cell-class-name="headers"
          :header-cell-style="{ background: '#e6e6e6' }"
          :cell-style="cell"
          row-class-name="row"
          style="width: 100%"
          v-loading="tableLoading"
          @row-click="changeMyFood"
        >
          <el-table-column
            :label="gL('serialNumber')"
            type="index"
            align="center"
            width="60"
          >
          </el-table-column>
          <el-table-column align="center" width="80px" :label="gL('img')">
            <template slot-scope="scope">
              <img
                :src="scope.row.image + '?x-oss-process=style/w20'"
                width="50px"
                height="50px"
                class="food-image"
                alt=""
                style="border: 1px solid #cccccc"
              />
            </template>
          </el-table-column>
          <el-table-column
            :prop="gLang == 1 ? 'name_ug' : 'name_zh'"
            align="center"
            :label="gL('foodName')"
          >
          </el-table-column>
          <el-table-column
            :prop="gLang == 1 ? 'category_name_ug' : 'category_name_zh'"
            align="center"
            :label="gL('type')"
          >
          </el-table-column>
          <el-table-column align="center" width="200" :label="gL('selected')">
            <template slot-scope="scope">
              <el-switch
                active-color="#13ce66"
                inactive-color="#ff4949"
                :value="isSelected(scope.row)"
              >
              </el-switch>
            </template>
          </el-table-column>
        </el-table>
        <div class="paging">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageSize"
            background
            :page-size="50"
            layout="prev, pager, next"
            :total="totalCount"
          >
          </el-pagination>

          <!--        <el-pagination-->
          <!--                @size-change="handleSizeChange"-->
          <!--                @current-change="handleCurrentChange"-->
          <!--                :current-page="lastPage"-->
          <!--                :page-sizes="[100, 200, 300, 400]"-->
          <!--                :page-size="100"-->
          <!--                layout="total, sizes, prev, pager, next, jumper"-->
          <!--                :total="totalCount">-->
          <!--        </el-pagination>-->

        </div>
      </div>
      <div class="content-right">
        <div class="title">
          <div class="count">

            {{gL('What_my_restaurant_can_cook')}}:{{ myFoods.length }}
          </div>
          <div class="name" @click="removeAll">
            <span class="icon-edit iconfont icon-qingkong"></span>
          </div>
        </div>
        <div class="list">
          <div  v-show="Search_for_content_true" class="list-item" v-for="(item, index) in myFoods" :key="index">
            <img :src="item.image + '?x-oss-process=style/w20'" alt="" />
            <div class="info">
              <div class="name">
                {{ gLang == 1 ? item.name_ug : item.name_zh }}
              </div>
              <!-- <div class="price">￥18</div> -->
            </div>
            <div class="icon" @click="removeMyFood(item)">
              <span class="icon-edit iconfont icon-qingkong"></span>
            </div>
          </div>

          <div   class="list-item" v-for="(item) in Search_for_content" >
            <img :src="item.image + '?x-oss-process=style/w20'" alt="" />
            <div class="info">
              <div class="name">
                {{ gLang == 1 ? item.name_ug : item.name_zh }}
              </div>
              <!-- <div class="price">￥18</div> -->
            </div>
            <div class="icon" @click="removeMyFood(item)">
              <span class="icon-edit iconfont icon-qingkong"></span>
            </div>
          </div>


        </div>
        <div class="btn" @click="confirm">{{gL('choose_It')}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { postLibraryFoodsImportAPI, getFoodListAPI, getLibraryFoodsListAPI, getLibraryFoodsCategoryListAPI } from "./../api/index.js"
import { debounce } from "./../utils/utils.js"
var self;
export default {
  created: function () {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.activeId = "";
    self.ruleForm.food_category_id = "";
    (self.serachText = ""), //搜索内容
      (self.delete_text = ""), //搜索框删除按钮
      self.getData();
    self.getList();
    // self.getRestaurantFoods();
  },
  data() {
    return {
      Search_for_content_true:true,
      Search_for_content:[],
      totalCount: 0,
      pageSize:1,
      lastPage: 0,
      currentPage: 0,
      gLang: 1,
      tableData: [],
      serachText: "", //搜索内容
      serachTextcat:"",
      delete_text: "", //搜索框删除按钮
      delete_text_cart:"",
      activeId: "",
      tables: [],
      ruleForm: {
        state: 1,
        food_category_id: "",
        shortcut_code: "",
        cost_price: "",
        vip_price: "",
        price: "",
        name_zh: "",
        name_ug: "",
        image: "",
        sort: "",
        format_id: 1,
      },
      activeName: 0,
      myFoods: [],
      myFoodsCount: 0,
      selected: false,
      restaurantFoods: [],
      sameFoods: [],
      tableLoading: false,
    };
  },
  methods: {
    handleSizeChange(val) {
      self.CurrentChange = val;
      console.log()
    },
    handleCurrentChange(val) {
      self.pageSize = val;
      self.getData();
    },

    isSelected(food) {
      let value = self.myFoods.filter((item) => item.id == food.id).length > 0;
      value = value || self.sameFoods.filter((item) => item.name_ug == food.name_ug).length > 0
      return value;

    },
    changeMyFood(row) {
      let food = row;
      const hasExists = self.myFoods.filter((item) => item.id == food.id).length > 0;
      if (!hasExists && self.isSelected(row)) return;
      if (hasExists) {
        self.removeMyFood(row);
        return;
      }
      self.myFoods.push(row);
      console.log('self.myFoods',self.myFoods)
    },
    removeAll() {
      self.myFoods = [];
    },
    removeMyFood(e) {
      self.myFoods.remove(e);
    },

    confirm() {
      var data = {
        food: [],
      };
      self.myFoods.forEach((v, i) => {
        data.food.push({
          id: v.id,
        });
      });
      postLibraryFoodsImportAPI(data).then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: response.data.message,
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
          self.myFoods = [];
          self.getRestaurantFoods()
        }
      });
    },
    cell({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 2) {
        if (self.gLang == 1) {
          return "direction: rtl";
        }
      }
    },
    //获取本餐厅美食
    getRestaurantFoods() {
      getFoodListAPI().then((response) => {
          if (response.status >= 200 && response.status < 300) {
            self.restaurantFoods = response.data.data;

            // self.restaurantFoods.forEach((element) => {
            //   self.tableData.forEach((v) => {
            //     if (element.name_ug == v.name_ug) {
            //       self.sameFoods.push(v);
            //     }
            //   });
            // });
            const foodsOfCurrentResturant = new Map()
            self.restaurantFoods.forEach(item => foodsOfCurrentResturant.set(item.name_ug, item))
            const existing = self.tableData.filter(item => foodsOfCurrentResturant.has(item.name_ug)).filter(item => self.sameFoods.filter(innerItem => item.name_ug == innerItem.name_ug).length == 0)
            self.sameFoods = [...self.sameFoods, ...existing]
          }
        });
    },
    //匹配
    matchFoods() {
      self.restaurantFoods.forEach((element) => {
        self.tableData.forEach((v) => {
          if (element.name_ug == v.name_ug) {
            self.sameFoods.push(v);
          }
        });
      });
      // self.isSelected()
    },
    //搜索输入框输入时候
    inputSearch() {
      if (self.serachText.length != 0) {
        self.delete_text = true;
      } else {
        self.delete_text = false;
      }
      self.inputSearch_Canteen_inventory_filter();
    },

    inputSearch_Canteen_inventory(e) {
      self.tarValue=e.target.value;
      this.pageSize = 1;
      if (self.serachTextcat.length != 0) {
        self.delete_text_cart = true;
      } else {
        self.delete_text_cart = false;
      }
      // self.getData();
      debounce(this.getData, 1000);
    },

    //食堂库存搜索功能
    inputSearch_Canteen_inventory_filter(){
      self.langId=localStorage.getItem("langId");
      if (self.langId=="1"){
        self.Search_for_content= self.myFoods.filter(value => value.name_ug.indexOf(self.serachText) !== -1);
      }else {
        self.Search_for_content= self.myFoods.filter(value => value.name_zh.indexOf(self.serachText) !== -1);
      }


      console.log('self.Search_for_content',self.Search_for_content);
      if ( self.Search_for_content.length >0){
        self.Search_for_content_true=false
      }else{
        self.Search_for_content_true=true;
      }
    },


    //删除搜索内容
    removeSerach() {
      self.serachText = "";
      self.serachTextcat = "";
      self.tarValue = "";
      this.pageSize = 1;
      self.delete_text = false;
      self.delete_text_cart = false;
      this.getData();
    },
    //点击点菜
    clickMenu(e) {
      self.activeId = e.$attrs.id;
      if (e.$attrs.id != 0) {
        self.ruleForm.food_category_id = e.$attrs.id;
      }
      self.getData();
    },
    //获取数据
    getData() {
      this.tableLoading = true;
      var data = {
        page: self.pageSize,
        category_id:self.CurrentChange,
        q:self.tarValue
      };
      if (self.activeId != "") {
        data.category_id = self.activeId;
      }
      getLibraryFoodsListAPI(data).then((response) => {
          if (response.status >= 200 && response.status < 300) {
            self.tableData = response.data.data.data;
            self.totalCount = response.data.data.page.total;
            self.lastPage = response.data.data.page.last_page;
            self.currentPage = response.data.data.page.current_page;

          }
        }).finally(() => {
          this.tableLoading = false;
        });
    },
    //获取角色列表
    getList() {
      getLibraryFoodsCategoryListAPI().then((response) => {
          if (response.status >= 200 && response.status < 300) {
            // var count = 0;
            // response.data.data.forEach((item) => {
            //   count += item.foods_count;
            // });
            // var all = {
            //   name: self.gL("all"),
            //   item: "first",
            //   id: 0,
            //   foods_count: count,
            // };
            // response.data.data.unshift(all);
            self.tables = response.data.data;
            this.getRestaurantFoods()
            // response.data.data.forEach((item) => {
            //   if (item.id != 0) {
            //     self.cate_list.push(item);
            //   }
            // });
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
.img-active {
  border: 3px solid #139d59 !important;
}
.img-active::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-top: 30px solid #139d59;
  border-right: 30px solid transparent;
}
.wraps {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .top {
    height: 50px;
    width: 100%;
    background-color: #2e3033;
    display: flex;
    justify-content: space-between;
    .menu {
      width: 61%;
      display: flex;
      color: #ffffff;
      height: 100%;
      // overflow-x: scroll;
      .menu-item {
        width: 100%;
        height: 100%;
        // padding: 0 25px;
        // line-height: 40px;
        font-size: 26px;
        cursor: pointer;
      }
      .active {
        background-color: #139d59;
      }
    }
    .serach {
      width: 280px;
      background: #4d4d4d;
      margin: 5px 15px 5px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .input {
        //  width: 70%;
        display: flex;
        align-items: center;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        width: 70%;
        background: #4d4d4d;
        &::placeholder {
          color: #cccccc;
        }
      }
      .search {
        color: #cccccc;
        font-size: 24px;
        padding: 0 10px;
      }
      .del {
        width: 20%;
        height: 100%;
        color: #cccccc;
        justify-content: center;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 22px;
        }
      }
    }
  }
  .body {
    display: flex;
    height: calc(100% - 50px);
    .table {
      height: calc(100% - 30px);
      overflow-y: scroll;
      width: 75%;

    }
    .paging {
      text-align: center;
      position: absolute;
      bottom: 10px;
      z-index: 99;
      right: 25%;
    }
    .content-right {
      flex: 2;
      background: #f5f4f1;
      position: relative;
      .title {
        padding: 16px;
        display: flex;
        direction: rtl;
        font-size: 22px;
        .count {
          flex: 1;
          text-align: center;
        }
        .name {
          .icon-edit {
            font-size: 25px;
            padding: 0 16px;
            cursor: pointer;
          }
        }
      }
      .list {
        padding: 4px;
        overflow-y: scroll;
        height: 80%;
        .list-item {
          display: flex;
          font-size: 18px;
          padding: 12px;
          background: #fff;
          border-bottom: 1px solid #ccc;
          &:last-child {
            border-bottom: none;
          }
          img {
            width: 77px;
            height: 77px;
            object-fit: cover;
          }
          .info {
            display: flex;
            flex-direction: column;
            padding: 4px;
            flex: 1;
            align-items: center;
            justify-content: space-around;
          }
          .icon {
            display: flex;
            align-items: center;
            font-size: 23px;
            cursor: pointer;
            .icon-edit {
              padding: 20px;
              font-size: 23px;
            }
            .icon-remove {
              font-size: 23px;
            }
          }
        }
      }
      .btn {
        text-align: center;
        background: #139d59;
        color: white;
        width: 100%;
        height: 60px;
        line-height: 60px;
        margin: 0;
        font-size: 22px;
        cursor: pointer;
        position: absolute;
        bottom: -30px;
      }
    }
  }

  //s提示框
  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .box {
      background-color: #ffffff;
      width: 920px;
      font-size: 30px;
      -webkit-animation: fadelogIn 0.4s;
      animation: fadelogIn 0.4s;
      .title {
        background-color: #e6e6e6;
        color: #1a1a1a;
        padding: 25px 20px;
        position: relative;
        text-align: center;
        .iconfont {
          position: absolute;
          right: 20px;
          font-size: 23px;
          color: #666666;
          cursor: pointer;
        }
      }
      .content {
        padding: 50px 75px 0 75px;
        display: flex;
        justify-content: space-between;
        .items {
          width: 48%;
          .row {
            margin-bottom: 20px;
          }
          .inp {
            display: flex;
            font-size: 20px;
            border: 1px solid #cccccc;
            border-radius: 4px;
            .input-item {
              width: 33.333333%;
              border-right: 1px solid #cccccc;
              &:last-child {
                border-right: none;
              }
              input {
                width: 100%;
                outline: none;
                text-align: center;
              }
              .input {
                text-align: center;
                padding: 7px 0;
                font-size: 20px;
              }
              .lab {
                text-align: center;
                padding: 13px 0;
                background: #f2f2f2;
                border-bottom: 1px solid #cccccc;
              }
            }
          }
          .type {
            display: flex;
            justify-content: space-between;
            .img {
              width: 100%;
              background-image: url("../../static/images/defau.png");
              background-size: 100% 100%;
              cursor: pointer;
              border: 1px solid #cccccc;
              overflow: hidden;
              height: 120px;
              img {
                width: 170px;
                height: 120px;
              }
            }
            .checks {
              width: 48%;
              .check {
                width: 100%;
                border: 1px solid #cccccc;
                color: #666666;
                font-size: 18px;
                text-align: center;
                cursor: pointer;
                margin-bottom: 30px;
                height: 45px;
                line-height: 45px;
                border-radius: 3px;
                &:last-child {
                  margin-bottom: 0;
                }
              }
              .active {
                background-color: #139d59;
                color: #fff;
              }
            }
          }
        }
      }
      .image {
        flex-wrap: wrap;
        justify-content: left;
        .img-item {
          width: 170px;
          height: 120px;
          margin-right: 22px;
          margin-bottom: 20px;
          overflow: hidden;
          cursor: pointer;
          border: 1px solid #999;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .adds {
        display: flex;
        margin: 20px 75px 20px 75px;
        justify-content: space-between;
        .add-btn {
          background-color: #ff9c00;
        }
        .btn {
          width: 48%;
          margin: 0;
        }
        .edit {
          width: 100%;
        }
      }
      .btn {
        background: #139d59;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        padding: 15px 0;
        cursor: pointer;
        margin: 20px 75px 20px 75px;
      }
    }
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 170px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}
.avatar {
  width: 170px !important;
  height: 120px !important;
  display: block;
}
/*弹层动画（从下往上）*/
@keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

@-webkit-keyframes fadelogIn {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
  }
  100% {
    -webkit-transform: none;
  }
}

.food-image {
  object-fit: cover;
}
</style>
