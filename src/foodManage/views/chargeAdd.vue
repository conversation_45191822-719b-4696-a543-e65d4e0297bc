<template>
    <div class="charge-add-page">
        <Title :title="gL('addCharge')" />
        <el-form class="form-box" :model="ruleForm" :rules="rules"
            ref="ruleForm">
            <el-row :gutter="20">
               
                <el-col :span="12">
                    <el-form-item prop="name_ug" :label="gL('chargeNameUg')">
                        <div class="input-item" :style="{ direction: 'rtl' }">
                            <el-input v-model="ruleForm.name_ug" :placeholder="gL('chargeNameUgRequired')"></el-input>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="name_zh" :label="gL('chargeNameZh')">
                        <div class="input-item" :style="{ direction: 'ltr' }">
                            <el-input v-model="ruleForm.name_zh" :placeholder="gL('chargeNameZhRequired')"></el-input>
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item prop="food_category_id" :label="gL('chargeCategorySelectRequired')">
                        <el-select v-model="ruleForm.food_category_id" :placeholder="gL('chargeCategorySelectRequired')">
                            <el-option :label="gL('pleaseSelect')" :value="0"></el-option>
                            <el-option v-for="item in chargeGroupData" :key="item.id" :label="gLang == 1 ? item.name_ug : item.name_zh"
                                :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item prop="price" :label="gL('price')">
                        <el-input type="number" v-currency v-model.number="ruleForm.price" :placeholder="gL('priceRequired')"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="cost_price" :label="gL('costPrice')">
                        <el-input type="number" v-currency v-model.number="ruleForm.cost_price" :placeholder="gL('costPriceRequired')"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item prop="image" :label="gL('image')">
                        <el-upload
                        class="avatar-uploader"
                        :action="uploadUrl"
                        :show-file-list="false"
                        :on-success="handleAvatarSuccess"
                        :headers="imageHeader"
                        :data="imageData"
                        name="image"
                        :before-upload="beforeAvatarUpload">
                        <img v-if="ruleForm.image" :src="ruleForm.image" class="avatar">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item prop="state" :label="gL('state')">
                        <el-radio-group v-model="ruleForm.state"></el-radio-group>
                            <el-radio :label="1">
                                {{ gL('open') }}
                            </el-radio>
                            <el-radio :label="0">
                                {{ gL('off') }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" class="button-box">
                <el-form-item>
                    <el-button type="primary" @click="confirm">{{ gL("confirm") }}</el-button>
                    <el-button @click="cancel">{{ gL("cancel") }}</el-button>
                </el-form-item>
            </el-row>
        </el-form>
    </div>
</template>
<script>
import { Title } from "../components";
import { getFoodChargeGroupAPI, postFoodChargeAPI } from "@/api/index.js";
export default {
    components: {
        Title,
    },
    created() {
        this.gLang = localStorage.getItem("langId");
        this.loadChargeGroupData();
    },
    data() {
        return {
            gLang: 1,
            dialogShow: false,
            chargeGroupData: [],
            uploadUrl : "https://" + localStorage.getItem("ip_adr") + "/api/v1/upload/image",
            imageData: {
                folder: "foods"
            },
            imageHeader: {
                Authorization: "Bearer " + localStorage.getItem("token"),
                MerchantNo: JSON.parse(localStorage.getItem("merchant_info")).merchant_no,
                "accept-language": localStorage.langId == 1 ? "ug-CN" : "zh-CN"
            },
            ruleForm: {
                name_ug: "",
                name_zh: "",
                food_category_id: 0,
                cost_price: null,
                price: null,
                shortcut_code: "",
                image: null,
                state: 1,
            },
            rules: {
                name_ug: [
                    { required: true, message: this.gL("chargeNameUgRequired"), trigger: "blur" }
                ],
                name_zh: [
                    { required: true, message: this.gL("chargeNameZhRequired"), trigger: "blur" }
                ],
                food_category_id: [
                    { type:"integer", required: true, message: this.gL("chargeCategorySelectRequired"), trigger: "blur" }
                ],
                cost_price: [
                    { required: true, message: this.gL("costPriceRequired"), trigger: "blur" }
                ],
                price: [
                    {  required: true, message: this.gL("priceRequired"), trigger: "blur" }
                ],
                image: [
                    { type:"string", required: true, message: this.gL("imageRequired"), trigger: "blur" }
                ],
                state: [
                    { type:"integer", required: true, message: this.gL("stateRequired"), trigger: "blur" }
                ]
            }
        }
    },
    methods: {
        loadChargeGroupData() {
            getFoodChargeGroupAPI().then(res => {
                console.log(res.data);
                this.chargeGroupData = res.data.data;
            });
        },
        handleAvatarSuccess(res, file) {
            console.log(res);
            this.ruleForm.image = res.message;
        },
        beforeAvatarUpload(file) {
            const isJPG = file.type === 'image/jpeg';
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isJPG) {
            this.$message.error('上传头像图片只能是 JPG 格式!');
            }
            if (!isLt2M) {
            this.$message.error('上传头像图片大小不能超过 2MB!');
            }
            return isJPG && isLt2M;
        },
        cancel() {
            this.$router.go(-1);
        },
        confirm() {
            
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    let loading = this.$loading();
                    postFoodChargeAPI(this.ruleForm).then(res => {
                        if (res.status >= 200 && res.status < 300) {
                            this.$message({
                                message: this.gL("successfulOperation"),
                                type: "success",
                                customClass: this.$toastClass(),
                                offset: 120
                            });
                            this.$router.go(-1);
                        }
                    }).finally(() => {
                        loading.close();
                    });
                }
            });
        }
    }
}
</script>
<style scoped>
html[lang="ug"] .charge-add-page {
    direction: rtl;
}

.avatar-uploader {
    .el-upload {
        box-sizing: border-box;
        border: 1px solid #D9D9D9;
        background: rgba(255, 255, 255, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        margin-right: 20px;
    }
}
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
  .button-box {
    margin-top: 20px;
    text-align: center;
    .el-form-item {
        .el-form-item__content {
            display: flex;
            /* flex-direction: row-reverse; */
            justify-content: space-between;
            padding: 20px 0px;
            .el-button {
                flex-grow: 1;
                font-size: 22px;
                text-align: center;
                padding: 15px 0px;
                cursor: pointer;
                &.el-button--primary {
                    background: #139d59;
                    color: #ffffff;
                    margin-inline: 20px;
                }
                &.el-button--default {
                    background: #D9D9D9;
                    margin-left: 0;
                    
                }
            }
        }
    }
  }
</style>