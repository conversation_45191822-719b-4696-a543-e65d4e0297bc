<template>
  <div class="spec-page-box">
    <div class="spec-menu">
      <router-link
        class="menu-item"
        :to="`/foodManage/spec/${item.key}`"
        v-for="(item, index) in menuList"
        :key="index"
      >
        <span>{{ item.name }}</span>
    </router-link>
    </div>

    <!-- 内容 -->
    <div class="content-box">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
import spec from "./spec.vue"
import method from "./method.vue"
import charge from "./chargeList.vue"
import lunchBox from "./lunchBox.vue"
export default {
  components: {
    spec,
    method,
    charge,
    lunchBox
  },
  activated() {
    this.menuActive = this.$route.query.type || "spec";
  },
  data() {
    return {
      menuList: [
        {
          name: this.gL("spec"),
          key: "spec"
        },
        {
          name: this.gL("method"),
          key: "method"
        },
        {
          name: this.gL("charge"),
          key: "charge"
        },
        {
          name: this.gL("lunchBox"),
          key: "lunch-box"
        }
      ],
      menuActive: ""
    };
  },
};
</script>

<style lang="less" scoped>
.spec-page-box {
  height: 100%;
  width: 100%;

  .spec-menu {
    width: 100%;
    height: 55px;
    background-color: #2e3033;
    display: flex;
    color: #6a6a6a;
    align-items: center;
    padding: 0px;
    font-size: 22px;
    .menu-item {
      line-height: 49px;
      margin: 0px 15px;
      padding: 0px 15px;
      cursor: pointer;
      position: relative;
      border-bottom: 6px solid rgba(19, 157, 89, 0);
      &::after {
        content: '';
        display: inline-block;
        background-color: rgba(70, 72, 73, 1);
        width: 2px;
        height: 20px;
        position: absolute;
        top: 50%;
        left: -15px;
        transform: translateY(-50%);
      }
      
      &:first-child {
        &::after {
          display: none;
        }
      }

      &.active-link {
        color: #fff;
        box-sizing: border-box;
        border-bottom: 6px solid rgba(19, 157, 89, 1);
      }
    }
  }

  .content-box {
    padding: 10px 0px;
  }
}
</style>
