<template>
  <div class="spec-box" :class="gLang == 1 ? 'ug-box' : 'zh-box'">
    <div class="btn-line">
      <div class="add-button head-button" @click="addSpec"
        :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }">
        <span> {{ gL("addSpec") }} </span>
        <span class="iconfont icon-jia-copy-copy"></span>
      </div>
      <div class="sort-button head-button" @click="setSorting"
        :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }">
        <span> {{ gL("setSorting") }} </span>
        <i class="el-icon-sort"></i>
      </div>
    </div>

    <!-- 表格 -->
    <div class="table">
      <el-table :data="tableData" header-cell-class-name="headers" :header-cell-style="{ background: '#e6e6e6' }"

        row-class-name="row" v-loading="tableLoading" style="width: 100%">
        <el-table-column :label="gL('sequence')" type="index" align="center" width="130"></el-table-column>
        <el-table-column prop="name_ug" align="center" :label="gL('specName')">
        </el-table-column>
        <el-table-column prop="name_zh" align="center" :label="gL('specName')">
        </el-table-column>
        <el-table-column prop="foods_count" align="center" :label="gL('bindFoodCount')">
          <template slot-scope="scope">
            <span style="color: rgba(19, 157, 89, 1);" >{{ scope.row.foods_count }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="gL('operation')" prop="dosome" align="center" width="280px">
          <template slot-scope="scope">
            <DeleteButton  @click="showDeleteDialog(scope.row.id)"></DeleteButton>
            <PriceButton @click="showSetAmount(scope.row.id, 'click')"> </PriceButton>
            <LinkButton  @click="showFoodBind(scope.row.id)"></LinkButton>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加规格弹出框 -->
    <AddSpec ref="addSpecRef" @confirmAdd="confirmAddHandler" />

    <!-- 删除规格提示弹出框 -->
    <TipsDialog ref="deleteSpecRef" :content="gL('deleteSpecButtonDesc')" @confirm="deleteSpec" />

    <!-- 美食绑定弹出框 -->
    <BindFoodDialog ref="specBindFoodRef" @confirm="specBindFoodConfirm" />

    <!-- 设置美食弹出框 -->
    <SetPrice ref="setPriceRef" @confirm="setPriceConfirm" />

    <!-- 设置排序弹出框 -->
    <SetSorting ref="setSortingRef" @confirm="getData" />
  </div>
</template>

<script>
import { getFoodSpecsAPI, deleteFoodSpecsAPI, getFoodSpecsFoodsAPI } from "./../../api/index.js";
import AddSpec from "./../components/AddSpec.vue";
import TipsDialog from "../../components/dialog/TipsDialog.vue";
import { BindFoodDialog } from "../components";
import SetPrice from "../components/SetPrice.vue";
import SetSorting from "../components/SetSorting.vue";
import {DeleteButton, LinkButton, PriceButton} from "../../components/buttons/";
import { Link } from "element-ui";
export default {
  components: {
    AddSpec,
    TipsDialog,
    BindFoodDialog,
    SetPrice,
    SetSorting,
    DeleteButton,
    LinkButton,
    PriceButton
  },
  activated() { },
  created() {
    this.gLang = localStorage.getItem("langId");
    this.getData();
  },
  data() {
    return {
      gLang: 1,
      tableData: [],
      tableLoading: false,
      deleteId: 0
    };
  },
  methods: {
    // 显示添加规格弹出框
    addSpec() {
      this.$refs.addSpecRef.showDialog();
    },

    // 获取规格列表
    getData() {
      this.tableLoading = true;
      getFoodSpecsAPI()
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.tableData = response.data.data;
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    // 添加规格成功后刷新列表
    confirmAddHandler(type) {
      this.getData();

      if (type == "bind") {
        // 显示绑定美食弹出框
        // this.showFoodBind();
      }
    },

    // 显示删除规格弹出框
    showDeleteDialog(id) {
      this.deleteId = id;
      this.$refs.deleteSpecRef.showDialog();
    },

    // 删除规格
    deleteSpec() {
      if (!this.deleteId) return;
      let loading = this.$loading();
      deleteFoodSpecsAPI(this.deleteId)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.$message({
              message: response.data.message,
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
            this.getData();
          }
        })
        .finally(() => {
          loading.close();
          this.deleteId = 0;
        });
    },

    // 显示美食绑定弹出框
    showFoodBind(id) {
      let loading = this.$loading();
      getFoodSpecsFoodsAPI(id)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            let foods = {};
            response.data.data.forEach(item => {
              foods[item.pid] = item;
            });
            this.$refs.specBindFoodRef.showDialog(id, foods);
          }
        })
        .finally(() => {
          loading.close();
        });
    },

    // 设置规格金额
    showSetAmount(id, type, data) {
      if (type == 'click') {
        let loading = this.$loading();
        getFoodSpecsFoodsAPI(id)
          .then(response => {
            if (response.status >= 200 && response.status < 300) {
              this.$refs.setPriceRef.showDialog(id, response.data.data, type);
            }
          })
          .finally(e => {
            loading.close();
          });
      } else {
        this.$refs.setPriceRef.showDialog(id, data, type);
      }
    },

    // 设置规格金额成功后刷新列表
    setPriceConfirm() {
      this.$refs.specBindFoodRef.cancel();
      this.getData();
    },

    // 绑定美食成功后刷新列表
    specBindFoodConfirm(id, data) {
      this.showSetAmount(id, 'bind', data);
    },
    setSorting(id) {
      this.$refs.setSortingRef.showDialog();
    },
  }
}
</script>

<style lang="less" scoped>
.ug-box {
  .btn-line .head-button {
    margin-left: 10px;
  }
}

.zh-box {
  .btn-line .head-button {
    margin-right: 10px;
  }
}

.btn-line {
  display: flex;
  height: 50px;
  margin-bottom: 10px;

  .head-button {
    padding: 0px 36px;
    color: #fff;
    font-size: 22px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    column-gap: 8px;

    .iconfont {
      font-size: 18px;
    }
  }

  .add-button {
    background: #ff9c00;
  }

  .sort-button {
    background: #d9d9d9;
    color: #333;
  }
}
</style>
