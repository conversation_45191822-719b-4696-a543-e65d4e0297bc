<template>
  <div>
    <Title :title="gL('addMethod')" />
    <el-form class="form-box" :class="gLang == 1 ? 'form-box-ug' : 'form-box-zh'" :model="ruleForm" :rules="rules"
      ref="ruleForm">
      <el-row :gutter="20">
        <!-- 做法名称 中文 -->
        <el-col :span="12">
          <el-form-item prop="name_zh" :label="gL('methodName_zh')">
            <div class="input-item" :style="{ direction: 'ltr' }">
              <el-input v-model="ruleForm.name_zh" :placeholder="gL('methodNamePlaceholder_zh')"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <!-- 做法名称 维吾尔语 -->
        <el-col :span="12">
          <el-form-item prop="name_ug" :label="gL('methodName_ug')">
            <div class="input-item ">
              <el-input v-model="ruleForm.name_ug" :placeholder="gL('methodNamePlaceholder_ug')"></el-input>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <!-- 选择分组 -->
          <el-form-item prop="group_id" :label="gL('methodGroupName')">
            <el-select v-model="ruleForm.group_id" :placeholder="gL('methodGroupName')">
              <el-option :label="gL('pleaseSelect')" :value="0"></el-option>
              <el-option v-for="item in methodGroupData" :key="item.id" :label="gLang == 1 ? item.name_ug : item.name_zh"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="is_surcharge" :label="gL('priceType')">
            <el-radio v-model="ruleForm.is_surcharge" :label="false">{{gL('free')}}</el-radio>
            <el-radio v-model="ruleForm.is_surcharge" :label="true">{{gL('notFree')}}</el-radio>
            <div class="tips">
            {{gL('methodAddPriceNotice')}}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12" v-if="ruleForm.is_surcharge">
          <el-form-item prop="price" :label="gL('methodAddPrice')">
            <el-input type="number" v-currency :placeholder="gL('pleaseInputMethodAddPrice')" v-model.number="ruleForm.price">
              <template slot="append">{{gL('yuan')}}</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <!-- 做法名称 中文 -->
        <el-col :span="12">
          <el-form-item prop="name_zh" :label="gL('methodDescriptionZh')">
            <div class="input-item" :style="{ direction: 'ltr' }">
              <el-input v-model="ruleForm.desc_zh" :placeholder="gL('methodDescriptionPlaceholderZh')"></el-input>
            </div>
          </el-form-item>
        </el-col>
        <!-- 做法名称 维吾尔语 -->
        <el-col :span="12">
          <el-form-item prop="name_ug" :label="gL('methodDescriptionUg')">
            <div class="input-item ">
              <el-input v-model="ruleForm.desc_ug" :placeholder="gL('methodDescriptionPlaceholderUg')"></el-input>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 按钮部分 -->
    <div class="button-box">
      <div class="btn cancel-btn" @click="cancel">
        {{ gL("cancel") }}
      </div>
      <div class="btn confrim-btn btn-center" @click="confirm('confrim')">
        {{ gL("confirm") }}
      </div>
      <!-- <div class="btn confrim-btn" @click="confirm('bind')">
            {{ gL("addSpecButtonText") }}
          </div> -->
    </div>
  </div>
</template>

<script>
import { postFoodMethodAPI, getFoodMethodGroupAPI, getFoodMethodDetailAPI, putFoodMethodAPI } from "@/api/index.js";
import { Title } from "../components";
export default {
  components: {
    Title,
  },
  created() {
    this.gLang = localStorage.getItem("langId");
    try {
      let loading = this.$loading();
      this.loadMethodGroupData();
      if(this.$route.params.id) {
        this.loadMethodDetail();
      }
    } finally {
      this.$loading().close();
    }
  },

  data() {
    return {
      gLang: 1,
      dialogShow: false,
      methodGroupData: [],
      ruleForm: {
        name_ug: "",
        name_zh: "",
        group_id: 0,
        is_surcharge: true,
        price: 0,
        desc_ug: "",
        desc_zh: ""
      },
      rules: {
        name_ug: [
          {
            required: true,
            message: this.gL("methodNamePlaceholder_ug"),
            trigger: "blur"
          }
        ],
        name_zh: [
          {
            required: true,
            message: this.gL("methodNamePlaceholder_ug"),
            trigger: "blur"
          }
        ],
        group_id: [
          {
            required: true,
            message: this.gL("methodGroupSelectRequired"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    loadMethodDetail() {
      let loading = this.$loading();
      getFoodMethodDetailAPI(this.$route.params.id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.ruleForm = response.data.data;
        }else {
          
        }
      })
      .catch(error => {
          this.$router.go(-1);
        })
      .finally(() => {
      });
    },
    loadMethodGroupData() {
      getFoodMethodGroupAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.methodGroupData = response.data.data;
        }
      });
    },

    cancel() {
      // back to method list
      this.$router.push({
        path: `/foodManage/spec/method/list`
      });
    },
    confirm() {
      if(this.ruleForm.is_surcharge) {
        this.ruleForm.price  = 0;
      }
      let request;
      if(this.$route.params.id) {
        request = putFoodMethodAPI(this.$route.params.id, this.ruleForm)
      } else {
        request = postFoodMethodAPI(this.ruleForm)
      }
      let loading = this.$loading();
      request.then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.$message({
              message: response.data.message,
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
            this.$router.push({
              path: `/foodManage/spec/method/list`
            });
          }
        })
        .finally(() => {
          this.$loading().close();
        });
    }
  }
};
</script>

<style>
.el-form-item__label {
  width: 100%;
}

.form-box-ug {
  direction: rtl;
  [class*="el-col-"] {
    float: right;
  }


}
.el-radio__label {
    padding-right: 10px;
  }
.form-box-zh {
  direction: ltr;
}

.form-box-ug .el-form-item__label {
  text-align: right;

  &:after {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }

  &::before {
    display: none;
  }
}

.form-box-zh .el-form-item__label {
  text-align: left;

  &::before {
    margin-left: 4px;
  }
}
</style>
<style lang="less" scoped>
.title {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0px;
  height: 60px;
  line-height: 60px;
  background: rgba(230, 230, 230, 1);
  color: rgba(30, 30, 30, 1);
  font-size: 22px;
  .back {
    width: 130px;
    padding: 0px 15px;
  }
  a.back {
    cursor: pointer;
    color: #4D4D4D;
  }
  .text {
    flex-grow: 1;
    text-align: center;
  }
}
.button-box {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  padding: 20px 0px;
}

.btn {
  flex-grow: 1;
  font-size: 22px;
  text-align: center;
  padding: 15px 0px;
  cursor: pointer;
}

.btn-center {
  margin-inline: 20px;
}

.cancel-btn {
  background-color: #D9D9D9;
}

.confrim-btn {
  background: #139d59;
  color: #ffffff;
}


.tips {
  color: rgba(106, 106, 106, 1);
  padding: 0px 10px; 
  background: rgba(242, 242, 242, 1);
}
.form-box {
  padding: 0px 10px;
}
</style>