<template>
  <div class="method-box" :class="gLang == 1 ? 'ug-box' : 'zh-box'">
    <!--  头部导航开始 -->
    <div class="btn-line">
      <!-- 添加属性 -->
      <div class="add-button head-button" :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }" @click="openAddMethodDialog()">
        <span> {{ gL("addMethod") }} </span>
        <span class="iconfont icon-jia-copy-copy"></span>
      </div>

      <!-- 添加属性分类 -->
      <div class="add-category-button head-button" @click="openAddMethodGroupDialog()"
        :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }">
        <span> {{ gL("addMethodGroupTitle") }} </span>
        <span class="iconfont icon-jia-copy-copy"></span>
      </div>

      <div class="sort-button head-button" @click="openMethodGroupSettingDialog()"
        :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }">
        <span> {{ gL("methodGroupSetting") }} </span>
        <svg viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
          width="22" height="22" fill="none">
          <rect id="svg 2" width="22" height="22" x="0" y="0" />
          <path id="矢量 3"
            d="M25.305 17.192L23.7359 17.192L23.7359 13.1432C23.7359 13.1034 23.732 13.064 23.7242 13.0249C23.7164 12.9858 23.7049 12.9479 23.6897 12.9111C23.6745 12.8744 23.6558 12.8394 23.6337 12.8063C23.6115 12.7732 23.5864 12.7426 23.5582 12.7144C23.5301 12.6862 23.4995 12.6611 23.4663 12.639C23.4332 12.6169 23.3983 12.5982 23.3615 12.5829C23.3247 12.5677 23.2868 12.5562 23.2477 12.5484C23.2087 12.5407 23.1693 12.5368 23.1294 12.5368L14.5695 12.5368L14.5695 10.7135L17.2053 10.7135C17.3118 10.7135 17.4172 10.7031 17.5216 10.6824C17.626 10.6616 17.7273 10.6308 17.8257 10.5901C17.924 10.5494 18.0175 10.4994 18.106 10.4403C18.1945 10.3812 18.2764 10.314 18.3516 10.2387C18.4269 10.1634 18.4941 10.0815 18.5532 9.99303C18.6124 9.90453 18.6623 9.8111 18.703 9.71276C18.7438 9.61442 18.7745 9.51305 18.7953 9.40865C18.8161 9.30425 18.8264 9.19883 18.8264 9.09238L18.8264 2.61186C18.8264 2.50525 18.816 2.39966 18.7952 2.2951C18.7744 2.19054 18.7435 2.08903 18.7027 1.99055C18.6618 1.89208 18.6117 1.79855 18.5524 1.70996C18.4931 1.62136 18.4257 1.53942 18.3502 1.46413C18.2747 1.38883 18.1926 1.32164 18.1039 1.26254C18.0151 1.20345 17.9215 1.15359 17.8229 1.11298C17.7243 1.07236 17.6227 1.04177 17.5181 1.02119C17.4135 1.00062 17.3079 0.990464 17.2013 0.990728L10.7208 0.990728C10.6143 0.990728 10.5089 1.00111 10.4045 1.02188C10.3001 1.04264 10.1987 1.07339 10.1004 1.11413C10.0021 1.15486 9.90864 1.2048 9.82013 1.26394C9.73162 1.32308 9.64974 1.39028 9.57447 1.46555C9.4992 1.54081 9.432 1.6227 9.37286 1.71121C9.31372 1.79971 9.26379 1.89314 9.22305 1.99148C9.18232 2.08982 9.15157 2.19119 9.1308 2.29559C9.11003 2.39999 9.09965 2.50541 9.09965 2.61186L9.09965 9.09038C9.09965 9.985 9.82616 10.7115 10.7208 10.7115L13.3546 10.7115L13.3546 12.5328L4.80065 12.5328C4.76083 12.5328 4.7214 12.5367 4.68234 12.5444C4.64329 12.5522 4.60537 12.5637 4.56858 12.5789C4.5318 12.5942 4.49685 12.6129 4.46374 12.635C4.43063 12.6571 4.4 12.6822 4.37185 12.7104C4.34369 12.7386 4.31855 12.7692 4.29643 12.8023C4.27431 12.8354 4.25563 12.8703 4.24039 12.9071C4.22515 12.9439 4.21365 12.9818 4.20588 13.0209C4.19811 13.0599 4.19423 13.0994 4.19423 13.1392L4.19423 17.19L2.62113 17.19C2.51469 17.19 2.40926 17.2004 2.30486 17.2212C2.20046 17.2419 2.09909 17.2727 2.00075 17.3134C1.90241 17.3542 1.80898 17.4041 1.72048 17.4632C1.63197 17.5224 1.55009 17.5896 1.47482 17.6648C1.39955 17.7401 1.33235 17.822 1.27321 17.9105C1.21407 17.999 1.16414 18.0924 1.1234 18.1908C1.08267 18.2891 1.05192 18.3905 1.03115 18.4949C1.01038 18.5993 1 18.7047 1 18.8112L1 25.2897C1 26.1843 1.72651 26.9088 2.62113 26.9088L9.09965 26.9088C9.20609 26.9088 9.31152 26.8984 9.41592 26.8777C9.52032 26.8569 9.62169 26.8261 9.72003 26.7854C9.81837 26.7447 9.9118 26.6947 10.0003 26.6356C10.0888 26.5765 10.1707 26.5093 10.246 26.434C10.3212 26.3587 10.3884 26.2768 10.4476 26.1883C10.5067 26.0998 10.5566 26.0064 10.5974 25.9081C10.6381 25.8097 10.6689 25.7083 10.6896 25.6039C10.7104 25.4995 10.7208 25.3941 10.7208 25.2877L10.7208 18.8152C10.7208 18.7087 10.7104 18.6033 10.6896 18.4989C10.6689 18.3945 10.6381 18.2931 10.5974 18.1948C10.5566 18.0964 10.5067 18.003 10.4476 17.9145C10.3884 17.826 10.3212 17.7441 10.246 17.6689C10.1707 17.5936 10.0888 17.5264 10.0003 17.4672C9.9118 17.4081 9.81837 17.3582 9.72003 17.3174C9.62169 17.2767 9.52032 17.246 9.41592 17.2252C9.31152 17.2044 9.20609 17.194 9.09965 17.194L5.40707 17.194L5.40707 13.7516L22.523 13.7516L22.523 17.194L18.8224 17.194C18.716 17.194 18.6106 17.2044 18.5062 17.2252C18.4018 17.2459 18.3004 17.2767 18.2021 17.3174C18.1037 17.3582 18.0103 17.4081 17.9218 17.4672C17.8333 17.5264 17.7514 17.5936 17.6761 17.6688C17.6009 17.7441 17.5336 17.826 17.4745 17.9145C17.4154 18.003 17.3654 18.0964 17.3247 18.1948C17.284 18.2931 17.2532 18.3945 17.2325 18.4989C17.2117 18.6033 17.2013 18.7087 17.2013 18.8152L17.2013 25.2937C17.2013 26.1883 17.9278 26.9148 18.8224 26.9148L25.303 26.9148C25.4093 26.9147 25.5146 26.9042 25.6189 26.8833C25.7232 26.8625 25.8245 26.8316 25.9227 26.7908C26.021 26.7501 26.1143 26.7001 26.2026 26.6409C26.291 26.5818 26.3728 26.5146 26.448 26.4393C26.5231 26.364 26.5902 26.2822 26.6493 26.1937C26.7083 26.1053 26.7582 26.0119 26.7989 25.9136C26.8395 25.8153 26.8703 25.714 26.891 25.6097C26.9117 25.5054 26.9221 25.4 26.9221 25.2937L26.9221 18.8132C26.9225 18.7068 26.9125 18.6014 26.892 18.497C26.8716 18.3926 26.8412 18.2912 26.8007 18.1928C26.7602 18.0944 26.7105 18.0009 26.6516 17.9123C26.5926 17.8238 26.5256 17.7418 26.4504 17.6665C26.3753 17.5912 26.2935 17.5239 26.2051 17.4648C26.1167 17.4056 26.0233 17.3557 25.925 17.315C25.8268 17.2743 25.7254 17.2436 25.6211 17.2229C25.5167 17.2022 25.4113 17.1919 25.305 17.192L25.305 17.192Z"
            fill="rgb(106,106,106)" fill-rule="nonzero" />
        </svg>

      </div>

      <div class="sort-button head-button" @click="openMethodSortDialog()"
        :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }">
        <span> {{ gL("setSorting") }} </span>
        <i class="el-icon-sort"></i>
      </div>
    </div>
    <!-- 头部导航结束 -->

    <!-- 内容部分开始 -->
    <div class="content-box">
      <div class="left-box">
        <ul>
          <li class="all" @click="loadMethodData(null)" :class="{ active: currentGroupId == 0 }">{{ gL("all") }}</li>
          <li :class="{ active: currentGroupId == item.id }" v-for="item in groupData" :key="item.id" @click="loadMethodData(item.id)">{{ gLang == 1 ? item.name_ug : item.name_zh }}</li>
        </ul>
      </div>
      <div class="right-box">
        
        <table class="table">
          <thead>
            <tr>
              <th>{{ gL("sequence") }}</th>
              <th>{{ gL("methodGroupName") }}</th>
              <th>{{ gL("methodName") }}</th>
              <th>{{ gL("price") }}</th>
              <th>{{ gL("linkNumber") }}</th>
              <!-- <th>ھالىتى</th> -->
              <th>{{ gL("action") }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in methodData" :key="item.id">
              <td>{{ index + 1 }}</td>
              <td>{{ gLang == 1 ? item.group_name_ug : item.group_name_zh }}</td>
              <td>{{ gLang == 1 ? item.name_ug : item.name_zh }}</td>
              <td>{{ item.price || gL("free") }}</td>
              <td class="link">{{ item.foods_count}}</td>
              <!-- <td>
                <el-switch v-model="value" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
              </td> -->
              <td class="manage">
                <DeleteButton @click="deleteMethodAction(item.id)" />
                <LinkButton @click="linkMehodAction(item.id)" />
                <SettingButton @click="openEditMethodDialog(item)" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <!-- 内容部分结束 -->
    <AddMethodGroupDialog ref="addCategoryDialogRef" @confirm="loadGroupData" />
    <!-- 添加做法 -->
    <!-- <AddMethodDialog ref="addMethodDialogRef" :methodGroupData="groupData" @confirm="loadMethodData(currentGroupId)" /> -->
    <!-- 编辑做法 -->
    <EditMethodDialog ref="editMethodDialogRef" @confirm="loadMethodData(currentGroupId)" />
    <!-- 做法排序 -->
    <MethodSortDialog ref="methodSortDialogRef" @loadGroupData="loadGroupData" @loadMethodData="refreshMethodData" />
    <!-- 做法美食关联 -->
    <BindFoodDialog ref="methodBindFoodRef" @confirm="confirmLinkMethodAction" />
    <!-- 做法分类设置 -->
    <MethodGroupSettingDialog ref="methodGroupSettingDialogRef" :methodGroupData="groupData" @confirm="loadGroupData" @edit="openEditMethodGroupDialog" />
    <!-- 编辑做法分组 -->
    <EditMethodGroupDialog ref="editMethodGroupDialogRef" @confirm="loadGroupData" />
  </div>
</template>

<script>
import {DeleteButton, LinkButton, SettingButton} from "../../components/buttons/index.js";
import {AddMethodGroupDialog, EditMethodDialog, MethodSortDialog, MethodGroupSettingDialog, EditMethodGroupDialog } from "../components/method/index.js";
import { getFoodMethodGroupAPI, getFoodMethodListAPI, deleteFoodMethodAPI, getFoodMethodFoodsAPI, postFoodMethodFoodsAPI} from "../../api/index.js";
import { BindFoodDialog } from "../components/index.js";
export default {
  name: 'MethodList',
  components: {
    DeleteButton,
    LinkButton,
    SettingButton,
    AddMethodGroupDialog,
    EditMethodDialog,
    MethodSortDialog,
    BindFoodDialog,
    MethodGroupSettingDialog,
    EditMethodGroupDialog
  },
  data() {
    return {
      gLang: 1,
      value: true,
      groupData: [],
      methodData: [],
      currentGroupId: 0,
    }
  },
  created() {
    this.gLang = localStorage.getItem("langId");
    this.loadGroupData();
    this.loadMethodData(0);
  },
  methods: {
    openMethodSortDialog() {
      this.$refs.methodSortDialogRef.showDialog(this.groupData);
    },
    loadMethodData(id) {
      if(id == null) {
        id = 0;
      }
    
      this.currentGroupId = id;
      getFoodMethodListAPI(id, '')
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.methodData = response.data.data;
          }
        })
     
    },
    loadGroupData() {
      getFoodMethodGroupAPI()
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.groupData = response.data.data;
          }
        })
    },
    refreshMethodData(id){
      if( id == this.currentGroupId || this.currentGroupId == 0){
        this.loadMethodData(id);
      } 
    },
    deleteMethodAction(id) {
      deleteFoodMethodAPI(id)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.loadMethodData(this.currentGroupId);
          }
        })
    },
    linkMehodAction(id) {
      let loading = this.$loading();
      getFoodMethodFoodsAPI(id)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
              let foods = {};
              response.data.data.forEach(item => {
                foods[item.id] = item;
              });
            this.$refs.methodBindFoodRef.showDialog(id, foods);
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    confirmLinkMethodAction(id, foods) {
      this.loading = this.$loading();
      foods = foods.map(item => {
        return {
          food_id: item.id,
        }
      });
      let data = {
        method_id: id,
        foods: foods
      };
      postFoodMethodFoodsAPI(data)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.loadMethodData(this.currentGroupId);
            this.$message({
              message: response.data.message,
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
          }
        })
        .finally(() => {
          this.loading.close();
        });
    },
    openAddMethodGroupDialog() {
      // this.$refs.addCategoryDialogRef.showDialog();
    },
    openAddMethodDialog() {
      this.$router.push({
        path: `/foodManage/spec/method/add`
      });
    },
    addCategorConfirm() {
      console.log("addCategorConfirm");
      this.loadGroupData();
      this.loadMethodData(this.currentGroupId);
    },
    openEditMethodDialog(data) {
      this.$router.push({
        path: `/foodManage/spec/method/edit/${data.id}123`
      });
    },
    openMethodGroupSettingDialog() {
      this.$refs.methodGroupSettingDialogRef.showDialog(this.groupData);
    },
    openEditMethodGroupDialog(data) {
      this.$refs.editMethodGroupDialogRef.showDialog(data);
    }
  }
}
</script>

<style lang="less" scoped>
.btn-line {
  display: flex;
  height: 50px;
  margin-bottom: 10px;

  .head-button {
    padding: 0px 36px;
    margin-right: 10px;
    color: #fff;
    font-size: 22px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    column-gap: 8px;

    .iconfont {
      font-size: 18px;
    }
  }

  .add-button,
  .add-category-button {
    background: #ff9c00;
  }

  .sort-button {
    background: #d9d9d9;
    color: #333;
  }
}

.content-box {
  display: flex;
  height: 100vh;
  // height: calc(100vh - 100px);

  .left-box {
    width: 255px;
    height: 100%;
    background: rgba(230, 230, 230, 1);

    ul {
      padding: 10px;
      li {
        height: 68px;
        /* 自动布局 */
       text-align: right;
        margin: 0px 40px 10px 0px;
        padding: 21px 15px 21px 25px;
        font-size: 22px;
        font-weight: 400;
        line-height: 100%;
        letter-spacing: 0%;
        color: rgba(106, 106, 106, 1);
        border: 1px solid rgba(0, 0, 0, 0);
        .zh-box & {
          text-align: left;
          margin: 0px 0px 10px 40px;
        }

        &.all {
          margin-right: 0px;
          .zh-box & {
            margin-left: 0px;
          }
        }

        &:hover {
          background: rgba(212, 212, 212, 1);
          color: rgba(0, 0, 0, 1);
        }

        &.active {
          box-sizing: border-box;
          border: 1px solid rgba(19, 157, 89, 1);
          background: rgba(202, 232, 218, 1);
          color: rgba(19, 157, 89, 1);
        }
      }
    }
  }
  .right-box {
    margin:0 10px;
    flex-grow: 1;
    table {
      width: 100%;
      
      thead {
        background: rgba(230, 230, 230, 1);
        
        tr {
          height: 68px;
          
          > th {
            padding: 0 15px;
            height: 68px;
            line-height: 68px;
            font-weight: bold;
            &:first-child{
              width: 118px;
            }
            
            &:nth-child(6) {
              width: 278px;
            }
          }
        }
      }
      tbody {
          tr {

            td {
              text-align: center;
              border-bottom: 1px dashed #D9D9D9 !important;
              height: 66px;
              line-height: 66px;
            
              &.link {
                color: rgba(19, 157, 89, 1);
              }
              button {
                margin-left: 2px;
              }
            }
          }
        }
    }
  }
}
</style>
