<template>
  <div class="wraps food food-manage" :class="gLang == 1 ? 'ug-box' : 'zh-box'">
    <router-view></router-view>
  </div>
</template>
<script>
export default {
  data() {
    return {
      gLang: 1
    }
  },
  created: function() {
    this.gLang = localStorage.getItem("langId");
  },
  methods: {
    addFood() {
      this.$router.push("/foodManage/food/add");
    }
  },
}
</script>
<style scoped lang="less">
.wraps {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
