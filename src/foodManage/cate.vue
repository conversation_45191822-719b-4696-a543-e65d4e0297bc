<template>
    <div class="wraps staff">
        <div class="table">
        <el-table
        :data="tableData"
        header-cell-class-name="headers"
        :header-cell-style="{background:'#e6e6e6'}"
        row-class-name="row"
        v-loading="tableLoading"
        height="100%"
        style="width: 100%">
        <el-table-column
          :label="gL('serialNumber')"
          type="index"
          align="center"
          width="60">
        </el-table-column>
        <el-table-column
          prop="name"
          align="center"
          :label="gL('cateName')">
        </el-table-column>
        <el-table-column
          prop="sort"
          align="center"
          :label="gL('sort')">
        </el-table-column>
        <el-table-column
          align="center"
          :label="gL('state')">
          <template slot-scope="scope">
            <el-switch
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.state"
              @change="changeSwitch(scope.$index, scope.row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column :label="gL('operation')" prop="dosome" align="center" width="130px">
          <template slot-scope="scope">
            <el-button type="text"  icon="iconfont icon-shiliangzhinengduixiang" @click="editData(scope.row)" circle></el-button>
             <span style="padding-right:9px;padding-left:15px">
              <span class='line'></span>
            </span>
            <el-button  type="text" class="danger" @click="delData(scope.row)" icon="iconfont icon-qingkong"  circle></el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>
      <div class="mask" v-if="modal_box">
        <div class="box">
          <div class="title">
            <span></span>
            <span>{{modal_title}}</span>
            <span class="iconfont icon-jia-copy" @click="cancel(2)"></span>
          </div>
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
            <div class="content">
              <div class="items">
                <div class="row">
                  <el-form-item prop="name_ug">
                    <el-input  v-model="ruleForm.name_ug" ref="input" :placeholder="gL('cateName')+gL('ug')" class="input-ug" :class="gLang==1?'uy-input':'zh-input'" maxlength="20"></el-input>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="name_zh">
                    <el-input  v-model="ruleForm.name_zh" :placeholder="gL('cateName')+gL('zh')" :class="gLang==1?'uy-input':'zh-input'"  maxlength="20"></el-input>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="sort">
                    <el-input placeholder="" v-model="ruleForm.sort" :placeholder="gL('sort')" type="number" oninput="if(value.length>3)value=value.slice(0,3)" @keyup.enter.native="confirm(2)">
                      <template slot="append">{{gL('sort')}}</template>
                    </el-input>
                  </el-form-item>
                </div>
                <div class="row type">
                  <div class="check" :class="ruleForm.state==0?'active':''" @click="ruleForm.state=0">{{gL('off')}}</div>
                  <div class="check" :class="ruleForm.state==1?'active':''" @click="ruleForm.state=1">{{gL('open')}}</div>
                </div>
              </div>
            </div>
          </el-form>
          <div class="adds">
            <div class="btn add-btn" @click="confirm(1)"  v-if="addBox">
                {{gL('continueAdd')}}
            </div>
            <div class="btn" @click="confirm(2)" :class="!addBox?'edit':''">
                {{gL('confirm')}}
            </div>
          </div>
        </div>
      </div>
      <modal :number_box="confirm_box"
                 :modal_content="modal_content"
                 :modal_title="modals_title"
                 @cancel="cancels" @confirm="confirmBox"></modal>
    </div>
</template>

<script>
import modal from '../components/modal.vue'
import { getFoodCategoriesListAPI, putFoodCategoriesStateAPI, postFoodCategoriesAddAPI, putFoodCategoriesUpdateAPI, deleteFoodCategoriesAPI } from './../api/index.js'
var self;
export default {
  created: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.getData();
  },
  data() {
    return {
      gLang:1,
      tableData:[],
      modal_box:false,
      modal_title:this.gL('addCate'),
      ruleForm:{
        state:1,
        sort:'',
        name_ug:'',
        name_zh:'',
      },
      rules: {
          sort: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' },
          ],
          name_ug: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          name_zh: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
        },
      addBox:true,
      confirm_box:false,
      modals_title:this.gL('add'),
      modal_content:this.gL('add'),
      tableLoading: false
    };
  },
  methods: {
    //添加
    addCate(){
        self.modal_box=true;
        self.addBox=true;
        if(self.tableData.length>0){
          self.ruleForm.sort = Math.max.apply(Math, self.tableData.map(function(o) {return o.sort}))+1;
        }
        setTimeout(() => {
          self.$refs['input'].focus();
        }, 100);
    },
    //获取数据
    getData(){
      this.tableLoading = true;
      getFoodCategoriesListAPI().then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.tableData = response.data.data;
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },
     //开关
    changeSwitch(index,row){
      putFoodCategoriesStateAPI(row.id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.$message({
            message:self.gL('successfulOperation'),
            type: "success",
            customClass:self.$toastClass(),
            offset: 120
          });
        }
      }).catch(err => {
        this.getData();
      });
    },
    //编辑
    editData(row){
      self.id = row.id;
      self.addBox = false;
      self.modal_box = true;
      self.ruleForm.name_ug = row.name_ug;
      self.ruleForm.name_zh = row.name_zh;
      self.ruleForm.state = row.state;
      self.ruleForm.sort = row.sort;
      self.modal_title=self.gL('editCate');
    },
    cancel(e){
      if(e==2){
        self.modal_box = false;
      }else{
        self.$refs['input'].focus();
        self.ruleForm.sort = self.ruleForm.sort+1;
      }
      self.ruleForm.state = 1;
      self.ruleForm.name_ug = '';
      self.ruleForm.name_zh = '';
      self.modal_title=self.gL('addCate');
    },
    confirm(e){
        if(self.addBox){
          self.$refs.ruleForm.validate((valid) => {
            if (valid) {
              postFoodCategoriesAddAPI(self.ruleForm).then((response) => {
                if(response.status >= 200 && response.status < 300){
                  self.$message({
                    message:self.gL('successfulOperation'),
                    type: "success",
                    customClass:self.$toastClass(),
                    offset: 120
                  });
                  self.getData();
                  self.cancel(e);
                }
              });
          } else {
            return false;
          }
        });
       }else{
        self.$refs.ruleForm.validate((valid) => {
        if (valid) {
          putFoodCategoriesUpdateAPI(self.id, self.ruleForm).then((response) => {
            if(response.status >= 200 && response.status < 300){
              self.$message({
                message:self.gL('successfulOperation'),
                type: "success",
                customClass:self.$toastClass(),
                offset: 120
              });
              self.getData();
              self.cancel(e);
            }
          });
        }else {
            return false;
          }
        });
      }
    },
    //删除
    delData(row){
      self.id = row.id;
      self.confirm_box = true;
      if(self.gLang==1){
        self.modal_content="《"+row.name+"》"+self.gL('confirs')+self.gL('confirmdelete');
      }else{
        self.modal_content=self.gL('confirmdelete')+"《"+row.name+"》"+self.gL('confirs');
      }
      self.modals_title=self.gL('tips');
    },
    cancels(){
      self.confirm_box = false;
    },
    confirmBox(){
      self.cancels();
      self.delRowData();
    },
    delRowData(){
      deleteFoodCategoriesAPI(this.id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.$message({
            message:self.gL('successfulOperation'),
            type: "success",
            customClass:self.$toastClass(),
            offset: 120
          });
          self.getData();
        }
      });
    },
  },
   components:{
    modal,
  }
};
</script>

<style lang="less" scoped>
.wraps{
    width: 100%;
    height: 100%;
    .table{
      height: 100%;
      overflow-y: scroll;
    }
      //s提示框
   .mask{
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      background-color: rgba(0,0,0,.5);
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 1366px;
      min-height: 768px;
      .box{
        background-color: #ffffff;
        width: 600px;
        font-size: 30px;
        .title{
          background-color: #e6e6e6;
          color:#1a1a1a;
          padding: 25px 20px;
          position: relative;
          text-align: center;
          .iconfont{
            position: absolute;
            right: 20px;
            font-size: 23px;
            color:#666666;
            cursor: pointer;
          }
        }
        .content{
          padding: 50px 75px 5px 75px;
          justify-content: space-between;
          .items{
            .row{
              margin-bottom: 20px;
            }
            .type{
              display: flex;
              justify-content: space-between;
              .check{
                width: 48%;
                border:1px solid #cccccc;
                color:#666666;
                font-size: 18px;
                text-align: center;
                cursor: pointer;
                height: 40px;
                line-height: 40px;
              }
              .active{
                background-color: #139d59;
                color: #fff;
              }
            }
          }
        }
        .adds{
          display: flex;
          margin: 20px 75px 20px 75px;
          justify-content: space-between;
          .add-btn{
            background-color: #ff9c00;
          }
          .btn{
            width: 48%;
            margin: 0;
          }
          .edit{
            width: 100%;
          }
        }
        .btn{
          background: #139d59;
          color:#ffffff;
          font-size: 26px;
          text-align: center;
          padding: 15px 0;
          cursor: pointer;
          margin: 20px 75px 20px 75px;
        }
      }
   }
  }
</style>
