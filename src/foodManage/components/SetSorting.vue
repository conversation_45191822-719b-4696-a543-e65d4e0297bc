<template>
  <CommonDialog :dialogShow.sync="show" :title="gL('setSorting')" width="700px" :direction="direction">
    <div class="spec-bind-content-box" :class="gLang == 1 ? 'content-box-ug' : 'content-box-zh'">
      <div class="food-box">
        <el-table class="table" ref="multipleTable" :data="getSpecList" tooltip-effect="dark" style="width: 100%"
          :row-key="row => row.id">
          <el-table-column :label="gL('sequence')" type="index" width="100" align="center" :reserve-selection="true">
          </el-table-column>
          <el-table-column :prop="gLang == 1 ? 'name_ug' : 'name_zh'" :label="gL('specName')" align="center" />

          <el-table-column :label="gL('sort')" width="130">
            <template slot-scope="scope">
              <input type="number" :value="scope.row.sort" @input="e => onSortChange(e, scope.row)"
                class="sort-input" />
            </template>
          </el-table-column>

        </el-table>
      </div>
      <div class="button-box">
        <div class="btn cancel-btn btn-center" @click="cancel">
          {{ gL("cancel") }}
        </div>
        <div class="btn confrim-btn" @click="confirm">
          {{ gL("confirm") }}
        </div>
      </div>
    </div>
  </CommonDialog>
</template>

<script>
import CommonDialog from "@/components/CommonDialog.vue";
import { postFoodSpecsSortAPI, getFoodSpecsAPI } from "@/api/index.js";
import { debounce, deepClone } from "@/utils/utils.js";
export default {
  components: {
    CommonDialog
  },
  created() {
    this.gLang = localStorage.getItem("langId");
  },
  data() {
    return {
      show: false,
      gLang: 1,
      getSpecList: [],
      type: "",
      id: 0
    };
  },
  computed: {
    direction() {
      return this.gLang == 1 ? 'rtl' : 'ltr';
    }
  },
  methods: {
    // 显示弹出框
    showDialog() {
      this.show = true;
      this.getSpecData();
    },

    // 加载规格规格数据
    getSpecData() {
      let loading = this.$loading();
      getFoodSpecsAPI()
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.getSpecList = response.data.data.sort((a, b) => a.sort - b.sort);
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    // 关闭弹出框
    cancel() {
      this.show = false;
    },

    // 确认
    confirm() {
      let loading = this.$loading();
      let data = this.getSpecList.map(item => {
        return {
          "id": item.id,
          "sort": item.sort
        }
      })
      postFoodSpecsSortAPI({ items: data })
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.$message({
              message: response.data.message,
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
            this.cancel();
            this.$emit("confirm");
          }
        })
        .finally(() => {
          loading.close();

        });

    },
    onSortChange(event, row) {
      let value = event.target.value;
      let sort = Number(value);

      if (sort < 1) {
        sort = 1;
        event.target.value = 1;
      }
      if (sort > this.getSpecList.length) {
        sort = this.getSpecList.length;
        event.target.value = this.getSpecList.length;
      }
      let oldSort = Number(row.sort);
      if (oldSort == sort) {
        return;
      }
      debounce(() => {
        this.onSortChangeDebounce(sort, row)
      }, 1000);
    },
    onSortChangeDebounce(sort, row) {
      let list = deepClone(this.getSpecList.filter(item => item.id != row.id));
      console.log('list', list.map(item => item.name_ug));
      list.splice(sort - 1, 0, row);
      list = list.map((v, i) => {
        return {
          ...v,
          sort: i + 1
        }
      });

      console.log('list', list);
      this.getSpecList = list;
    }

  }
};
</script>

<style>
/* .content-box-ug {

  .el-table th,
  .el-table td {
    text-align: right;
  }
} */

.spec-bind-content-box .table.el-table th {
  background-color: #f2f2f2;
}
</style>
<style lang="less" scoped>
.spec-bind-content-box {
  height: 500px;

  &.content-box-ug {
    direction: rtl;
  }

  &.content-box-zh {
    direction: ltr;
  }

  .food-box {
    flex-shrink: 1;
    flex-grow: 1;
    height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;

    .table {
      width: 100%;
    }
  }

  .button-box {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    padding: 10px 0px 20px;
    border-top: 1px solid #d9d9d9;
  }

  .btn {
    flex-grow: 1;
    font-size: 26px;
    text-align: center;
    padding: 15px 0px;
    cursor: pointer;
  }

  .btn-center {
    margin-inline: 20px;
  }

  .cancel-btn {
    background-color: #d9d9d9;
  }

  .confrim-btn {
    background: #139d59;
    color: #ffffff;
  }
}

input.sort-input {
  text-align: center;
  box-sizing: border-box;
  border: 1px solid rgba(230, 230, 230, 1);
  line-height: 36px;
  font-size: 20px;
  width: 100px;
  transition: all 0.3s ease;

  &:focus {

    border: 1px solid rgba(19, 157, 89, 1);
    outline: none;
  }
}
</style>