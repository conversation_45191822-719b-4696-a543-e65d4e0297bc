<template>
  <CommonDialog
    :dialogShow.sync="show"
    :title="gL('bindFoodTitle')"
    width="700px"
    :direction="direction"
  >
    <div
      class="spec-bind-content-box"
      :class="gLang == 1 ? 'content-box-ug' : 'content-box-zh'"
    >
      <div class="food-box">
        <el-table
          class="table"
          ref="multipleTable"
          :data="foodsList"
          tooltip-effect="dark"
          style="width: 100%"
          :row-key="row => row.id"
        >
          <el-table-column type="index" width="55" :reserve-selection="true">
          </el-table-column>
          <el-table-column
            :prop="gLang == 1 ? 'food_name_ug' : 'food_name_zh'"
            :label="gL('foodName')"
          ></el-table-column>
          <el-table-column
            :prop="gLang == 1 ? 'category_name_ug' : 'category_name_zh'"
            :label="gL('foodCate')"
          ></el-table-column>
          <el-table-column :label="gL('specPrice')">
            <template slot-scope="scope">
              <el-input type="number" v-model="scope.row.price"></el-input>
            </template>
          </el-table-column>
          <el-table-column :label="gL('specVipPrice')">
            <template slot-scope="scope">
              <el-input type="number" v-model="scope.row.vip_price"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="button-box">
        <div class="btn cancel-btn btn-center" @click="cancel">
          {{ gL("cancel") }}
        </div>
        <div class="btn confrim-btn" @click="confirm">
          {{ gL("confirm") }}
        </div>
      </div>
    </div>
  </CommonDialog>
</template>

<script>
import CommonDialog from "../../components/CommonDialog.vue";
import { postFoodSpecsFoodsAPI } from "./../../api/index.js";
export default {
  components: {
    CommonDialog
  },
  created() {
    this.gLang = localStorage.getItem("langId");
  },
  data() {
    return {
      show: false,
      gLang: 1,
      foodsList: [],
      type: "",
      id: 0
    };
  },
  computed: {
    direction() {
      return this.gLang == 1 ? 'rtl' : 'ltr';
    }
  },
  methods: {
    // 显示弹出框
    showDialog(id, data, type) {
      this.foodsList = data;
      this.type = type;
      this.id = id;
      this.show = true;
    },

    // 关闭弹出框
    cancel() {
      this.show = false;
    },

    // 确认
    confirm() {
      const data = [];
      let keyLock = false;
      for (let i = 0; i < this.foodsList.length; i++) {
        const item = this.foodsList[i];
        console.log("--- item -", item);
        if (item.price == "") {
          this.$message({
            message: this.gL("specPriceEmpty").replace(
              "%s",
              this.gLang == 1 ? item.food_name_ug : item.food_name_zh
            ),
            type: "warning",
            customClass: this.$toastClass(),
            offset: 120
          });
          keyLock = true;
          return;
        }
        if (item.vip_price == "") {
          this.$message({
            message: this.gL("specVipPriceEmpty").replace(
              "%s",
              this.gLang == 1 ? item.food_name_ug : item.food_name_zh
            ),
            type: "warning",
            customClass: this.$toastClass(),
            offset: 120
          });
          keyLock = true;
          return;
        }
        data.push({
          pid: item.pid,
          price: parseFloat(item.price),
          vip_price: parseFloat(item.vip_price),
          cost_price: parseFloat(item.cost_price)
        });
      }

      if (keyLock) return;
      postFoodSpecsFoodsAPI({
        spec_id: this.id,
        foods: data
      }).then(res => {
        if (res.status >= 200 && res.status < 300) {
          this.$message({
            message: res.data.message,
            type: "success",
            customClass: this.$toastClass(),
            offset: 120
          });
          this.cancel();
          this.$emit("confirm");
        }
      });
    }
  }
};
</script>

<style>


.spec-bind-content-box .table.el-table th {
  background-color: #f2f2f2;
}
</style>
<style lang="less" scoped>
.spec-bind-content-box {
  height: 500px;

  &.content-box-ug {
    direction: rtl;
  }

  &.content-box-zh {
    direction: ltr;
  }

  .food-box {
    flex-shrink: 1;
    flex-grow: 1;
    height: 400px;
    overflow-y: auto;

    .table {
      width: 100%;
    }
  }

  .button-box {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    padding: 10px 0px 20px;
    border-top: 1px solid #d9d9d9;
  }
  .btn {
    flex-grow: 1;
    font-size: 26px;
    text-align: center;
    padding: 15px 0px;
    cursor: pointer;
  }
  .btn-center {
    margin-inline: 20px;
  }
  .cancel-btn {
    background-color: #d9d9d9;
  }
  .confrim-btn {
    background: #139d59;
    color: #ffffff;
  }
}
</style>
