<template>
  <CommonDialog :dialogShow.sync="dialogShow" :title="gL('addMethodGroupTitle')" width="668px">

    <el-form class="form-box" :class="gLang == 1 ? 'form-box-ug' : 'form-box-zh'" :model="ruleForm" :rules="rules"
      ref="ruleForm">
      <el-form-item prop="name_ug" :label="gL('methodGroupTitle_ug')">
        <div class="input-item ">
          <el-input v-model="ruleForm.name_ug" :placeholder="gL('addMethodGroupPlaceholder_ug')"></el-input>
        </div>
      </el-form-item>
      <el-form-item prop="name_zh" :label="gL('methodGroupTitle_zh')">
        <div class="input-item" :style="{ direction: 'ltr' }">
          <el-input v-model="ruleForm.name_zh" :placeholder="gL('addMethodGroupPlaceholder_zh')"></el-input>
        </div>
      </el-form-item>
    </el-form>

    <!-- 按钮部分 -->
    <div class="button-box">
      <div class="btn cancel-btn" @click="cancel">
        {{ gL("cancel") }}
      </div>
      <div class="btn confrim-btn btn-center" @click="confirm('confrim')">
        {{ gL("confirm") }}
      </div>
      <!-- <div class="btn confrim-btn" @click="confirm('bind')">
          {{ gL("addSpecButtonText") }}
        </div> -->
    </div>
  </CommonDialog>
</template>

<script>
import CommonDialog from "./../../../components/CommonDialog.vue";
import { postFoodMethodGroupAPI } from "../../../api/index.js";

export default {
  name: "AddMethodGroupDialog",
  components: {
    CommonDialog
  },
  created() {
    this.gLang = localStorage.getItem("langId");
  },
  data() {
    return {
      gLang: 1,
      dialogShow: false,
      ruleForm: {
        name_ug: "",
        name_zh: ""
      },
      rules: {
        name_ug: [
          {
            required: true,
            message: this.gL("addMethodGroupPlaceholder_ug"),
            trigger: "blur"
          }
        ],
        name_zh: [
          {
            required: true,
            message: this.gL("addMethodGroupPlaceholder_zh"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    showDialog() {
      this.dialogShow = true;
    },
    cancel() {
      this.ruleForm.name_ug = "";
      this.ruleForm.name_zh = "";
      this.dialogShow = false;
    },
    confirm() {
      postFoodMethodGroupAPI(this.ruleForm)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.$emit("confirm");
            this.cancel();
            this.$message({
              message: response.data.message,
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
          }
        })
    }
  }
};
</script>

<style>
.el-form-item__label {
  width: 100%;
}

.form-box-ug .el-form-item__label {
  text-align: right;

  &:after {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }

  &::before {
    display: none;
  }
}

.form-box-zh .el-form-item__label {
  text-align: left;

  &::before {
    margin-left: 4px;
  }
}
</style>
<style lang="less" scoped>
.button-box {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  padding: 20px 0px;
}

.btn {
  flex-grow: 1;
  font-size: 26px;
  text-align: center;
  padding: 15px 0px;
  cursor: pointer;
}

.btn-center {
  margin-inline: 20px;
}

.cancel-btn {
  background-color: #D9D9D9;
}

.confrim-btn {
  background: #139d59;
  color: #ffffff;
}
</style>
