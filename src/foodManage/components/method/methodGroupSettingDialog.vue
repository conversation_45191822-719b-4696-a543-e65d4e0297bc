    <template>
    <CommonDialog :direction="direction" class="method-group-setting-dialog" :dialogShow.sync="dialogShow" :title="gL('methodGroupSetting')" width="768px" @close="close">
       <div class="content-box">
        <Table :data="methodGroupData" :columns="columns" table-class="method-group-setting-table">
            <template #cell-id="{ row, rowIndex }">
                {{ rowIndex + 1 }}
            </template>
            <template #cell-action="{ row }">   
                <SettingButton @click="edit(row)" />
                <DeleteButton @click="del(row)" />
            </template>
        </Table>
       
       </div>
    </CommonDialog>
</template>

<script>
import {CommonDialog} from '@/components';
import Table from "@/components/table/index.js";
import { SettingButton, DeleteButton } from '@/components/buttons';
import { deleteFoodMethodGroupAPI } from '@/api';
export default {
    name: 'MethodGroupSettingDialog',
    components: {
        CommonDialog,
        Table,
        Setting<PERSON>utton,
        Delete<PERSON>utton
    },
    created() {
        this.gLang = localStorage.getItem("langId");
    },
    props: {
        methodGroupData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            gLang: 1,
            dialogShow: false,
            columns: [
                {
                    key: "id",
                    title: this.gL('sequence'),
                    dataIndex: 'id',
                    style: {
                        width: '140px',
                        textAlign: 'center'
                    }
                },
                {
                    key: this.gLang == 1 ? 'name_ug' : 'name_zh',
                    title: this.gL('methodGroupName'),
                    dataIndex: 'name',
                    style: {
                        textAlign: 'center'
                    }
                },
                {
                    key: 'action',
                    title: this.gL('action'),
                    dataIndex: 'action',
                    style: {
                        width: '90px',
                        textAlign: 'center'
                    }
                }
            ]
        }
    },  
    computed: {
        direction() {
            return this.gLang == 1 ? 'rtl' : 'ltr';
        }
    },
    methods: {
        showDialog() {
            
            this.dialogShow = true;
        },
        close() {
            this.dialogShow = false;
        },
        confirm() {
            this.showDialog = false;
        },
        edit(row) {
            this.$emit('edit', row);
        },
        del(row) {
            this.$confirm(this.gL('confirmDeleteMethodGroup'), this.gL('confirm'), {
                confirmButtonText: this.gL('confirm'),
                cancelButtonText: this.gL('cancel'),
                type: 'warning'
            }).then(() => {
                let loading = this.$loading();
                deleteFoodMethodGroupAPI(row.id).then(res => {
                    if (res.status == 200) {
                        this.$message({
                            message: res.data.message,
                            type: "success",
                            customClass: this.$toastClass(),
                            offset: 120
                            });
                        this.methodGroupData = this.methodGroupData.filter(item => item.id != row.id);
                        this.$emit('confirm');
                    }
                }).finally(() => {
                    loading.close();
                });
            });
        },
    }
};  
</script>

<style scoped>
.method-group-setting-dialog {
    .dialog-content {
        padding-bottom: 15px !important;
        
        .content-box {
        
            height: 200px;
            overflow-y: auto;
            scrollbar-width: thin;
        }
    }
}

</style>
