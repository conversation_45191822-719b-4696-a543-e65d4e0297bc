<template>
    <CommonDialog :dialogShow.sync="dialogShow" :title="gL('setSorting')" width="768px"
        :direction="gLang == 1 ? 'rtl' : 'ltr'">

        <!-- 标签页导航 -->
        <ul class="tab-navigation">
            <li class="tab-item" :class="{ active: activeTab === tabs.methodGroup }"
                @click="onTabChanged(tabs.methodGroup)">
                {{ gL('methodGroup') }}
            </li>
            <li class="tab-item" :class="{ active: activeTab === tabs.method }" @click="onTabChanged(tabs.method)">
                {{ gL('method') }}
            </li>
        </ul>
        <!-- 主要内容区域 -->
        <div class="content-area">
            <div class="method-box" v-if="activeTab === tabs.method">
                <div class="method-groupd-select">
                    <el-select v-model="selectedMethodGroupId" @change="onMethodGroupChanged"
                        :placeholder="gL('methodGroupName')">
                        <el-option :style="{ textAlign: gLang == 1 ? 'right' : 'left' }" v-for="item in methodGroupData"
                            :key="item.id" :label="gLang == 1 ? item.name_ug : item.name_zh"
                            :value="item.id"></el-option>
                    </el-select>

                </div>
                <Table :data="methodTableData" :columns="methodTableColumns" >
                    <template #cell-id="{ row, rowIndex }">
                        {{ rowIndex + 1 }}
                    </template>
                    <template #cell-sort="{ row }">
                        <input :value="row.sort" class="sort-input" @input="e => onMethodSortChange(e, row)" />
                    </template>
                </Table>
            </div>
            <div class="method-group-box" v-if="activeTab === tabs.methodGroup">
                <Table :data="methodGroupTableData" :columns="methodGroupTableColumns">
                    <template #cell-id="{ row, rowIndex }">
                        {{ rowIndex + 1 }}
                    </template>
                    <template #cell-sort="{ row }">
                        <input :value="row.sort" class="sort-input" @input="e => onSortChange(e, row)" />
                    </template>
                </Table>
            </div>

        </div>
        <!-- 主要内容区域结束 -->

        <!-- 按钮部分 -->
        <div class="button-box">
            <div class="btn cancel-btn" @click="cancel">
                {{ gL("cancel") }}
            </div>
            <div class="btn confrim-btn btn-center" @click="confirm('confrim')">
                {{ gL("confirm") }}
            </div>
            <!-- <div class="btn confrim-btn" @click="confirm('bind')">
            {{ gL("addSpecButtonText") }}
          </div> -->
        </div>
    </CommonDialog>
</template>

<script>
import CommonDialog from "./../../../components/CommonDialog.vue";
import { getFoodMethodListAPI, postFoodMethodGroupSortAPI, postFoodMethodSortAPI } from "../../../api/index.js";
import Table from "@/components/table";
import { debounce, deepClone } from "@/utils/utils.js";
// 标签页
const tabs = {
    method: 'method',
    methodGroup: 'methodGroup'
}

export default {
    components: {
        CommonDialog,
        Table
    },
    created() {
        this.gLang = localStorage.getItem("langId");
    },
    props: {
    },
    data() {
        return {
            gLang: 1,
            dialogShow: false,
            activeTab: tabs.method,
            tabs: tabs,
            methodData: [],
            methodTableData: [],
            selectedMethodGroupId: 0,
            methodGroupData: [],
            methodGroupTableData: [],
            methodGroupTableColumns: [
                {
                    key: 'id',
                    title: this.gL('sequence'),
                    dataIndex: 'id',
                    style: {
                        width: '140px',
                        textAlign: 'center'
                    }
                },
                {
                    key: this.gLang == 1 ? 'name_ug' : 'name_zh',
                    title: this.gL('methodGroupName'),
                    dataIndex: 'id',
                    headerStyle: {
                        textAlign: 'center'
                    },
                    style: {
                        textAlign: 'center'
                    }
                },
                {
                    key: 'sort',
                    title: this.gL('sort'),
                    dataIndex: 'id',
                    headerStyle: {
                        width: '100px',
                        textAlign: 'center'
                    },
                    style: {
                        textAlign: 'center'
                    }
                }
            ],
            methodTableColumns: [
                {
                    key: 'id',
                    title: this.gL('sequence'),
                    dataIndex: 'id',
                    style: {
                        width: '140px',
                        textAlign: 'center'
                    }
                },
                {
                    key: this.gLang == 1 ? 'name_ug' : 'name_zh',
                    title: this.gL('methodName'),
                    dataIndex: 'id',
                    headerStyle: {
                        textAlign: 'center'
                    },
                    style: {

                        textAlign: 'center'
                    }
                },
                {
                    key: 'sort',
                    title: this.gL('sort'),
                    dataIndex: 'id',
                    headerStyle: {
                        width: '100px',
                        textAlign: 'center'
                    },
                    style: {
                        textAlign: 'center'
                    }
                },
            ]
        };
    },
    methods: {
        onTabChanged(tab) {
            this.activeTab = tab;
            if (tab == tabs.methodGroup) {
                this.methodGroupTableData = deepClone(this.methodGroupData).sort((a, b) => a.sort - b.sort);
            }
        },
        showDialog(methodGroupData) {
            this.methodGroupData = methodGroupData;
            this.selectedMethodGroupId = methodGroupData[0].id;
            this.onMethodGroupChanged(this.selectedMethodGroupId);
            this.dialogShow = true;
        },
        onMethodGroupChanged(groupId) {
            getFoodMethodListAPI(groupId, "").then(res => {
                if (res.status == 200) {
                    this.methodData = res.data.data;
                    this.methodTableData = deepClone(this.methodData).sort((a, b) => a.sort - b.sort);
                }
            })
        },
        onMethodSortChange(event, row) {
            let value = event.target.value;
            let sort = Number(value);
            
            if (sort < 1) {
                sort = 1;
                event.target.value = 1;
            }
            if (sort > this.methodTableData.length) {
                sort = this.methodTableData.length;
                event.target.value = this.methodTableData.length;
            }
            let oldSort = Number(row.sort);
            if (oldSort == sort) {
                return;
            }
            debounce(() => {
                this.onMethodSortChangeDebounce(sort, row)
            }, 1000);
        },
        onMethodSortChangeDebounce(sort, row) {
            
            // 从老的位置删除并加载新的位置
            let methodData = this.methodTableData.filter(item => item.id != row.id);
            methodData.splice(sort - 1, 0, row);
            methodData = methodData.map((item, index) => {
                return {
                    ...item,
                    sort: index + 1
                }
            });
            console.log('methodData', methodData);
            this.methodTableData = methodData;
        },
        onSortChange(event, row) {
            let value = event.target.value;
            let sort = Number(value);
            let oldSort = Number(row.sort);
            
            if (sort < 1) {
                sort = 1;
                event.target.value = 1;
            }
            if (sort > this.methodGroupTableData.length) {
                sort = this.methodGroupTableData.length;
                event.target.value = this.methodGroupTableData.length;
            }
            if (oldSort == sort) {
                return;
            }
            debounce(() => {
                this.onSortChangeDebounce(sort, row)
            }, 1000);
        },
        onSortChangeDebounce(sort, row) {
            
            // 从老的位置删除并加载新的位置
            let groupData = this.methodGroupTableData.filter(item => item.id != row.id);
            groupData.splice(sort - 1, 0, row);
            groupData = groupData.map((item, index) => {
                return {
                    ...item,
                    sort: index + 1
                }
            });
            this.methodGroupTableData = groupData;
        },
        cancel() {
            if (this.activeTab == tabs.methodGroup) {
                this.methodGroupTableData = deepClone(this.methodGroupData).sort((a, b) => a.sort - b.sort);
            } else if (this.activeTab == tabs.method) {
                this.methodTableData = deepClone(this.methodData).sort((a, b) => a.sort - b.sort);
            }
        },
        confirm() {
            if (this.activeTab == tabs.methodGroup) {
                this.saveMethodGroupSort();
            } else if (this.activeTab == tabs.method) {
                this.saveMethodSort();
            }
        },
        saveMethodSort() { // 保存做法排序
            let data = this.methodTableData.map(item => {
                return {
                    "id": item.id,
                    "sort": item.sort
                }
            });
            postFoodMethodSortAPI({
                "items": data
            }).then(res => {
                if (res.status == 200) {
                    this.methodData = deepClone(this.methodTableData);
                    this.$message({
                        message: res.data.message,
                        type: "success",
                        customClass: this.$toastClass(),
                        offset: 120
                    });
                    // 刷新主页数据
                    this.$emit('loadMethodData', this.selectedMethodGroupId);
                }
            })
        },
        saveMethodGroupSort() { // 保存做法分组排序\
            let data = this.methodGroupTableData.map(group => {
                return {
                    "id": group.id,
                    "sort": group.sort
                }
            });
            postFoodMethodGroupSortAPI({
                "items": data
            }).then(res => {
                if (res.status == 200) {
                    this.methodGroupData = deepClone(this.methodGroupTableData);
                    this.$message({
                        message: res.data.message,
                        type: "success",
                        customClass: this.$toastClass(),
                        offset: 120
                    });
                }
            })
        }
    }
};
</script>
<style>
.zh-box {
    .el-input--suffix .el-input__inner {
        text-align: left;
    }
}

.ug-box {
    .el-input--suffix .el-input__inner {
        padding-right: 15px;
        padding-left: 30px;
    }

    .el-input__suffix {
        right: auto;
        left: 5px;
    }
}
</style>

<style scoped>
ul.tab-navigation {
    display: flex;
    flex-direction: row;
    justify-content: start;
    height: 48px;

    li {
        display: inline-block;
        text-align: center;
        cursor: pointer;
        line-height: 40px;
        box-sizing: border-box;
        border-bottom: 6px solid rgba(217, 217, 217, 1);
        padding: 0 25px;
        font-size: 22px;
        min-width: 200px;
        color: rgba(155, 155, 155, 1);
        &.active {
            border-bottom: 6px solid rgba(19, 157, 89, 1);
            color: #2E3033;
        }
    }
}

.content-area {
    height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;

    .method-groupd-select {
        margin: 8px 0;
    }

    .method-group-box {
        table {
            tbody {
                tr {
                    td {
                        
                    }
                }
            }
        }
    }
}
input.sort-input {
    text-align: center;
    box-sizing: border-box;
    border: 1px solid rgba(230, 230, 230, 1);
    line-height: 36px;
    font-size: 20px;
    width: 100px;
    transition: all 0.3s ease;

    &:focus {

        border: 1px solid rgba(19, 157, 89, 1);
        outline: none;
    }
}
.button-box {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    padding: 20px 0px;
}

.btn {
    flex-grow: 1;
    font-size: 26px;
    text-align: center;
    padding: 15px 0px;
    cursor: pointer;
}

.btn-center {
    margin-inline: 20px;
}

.cancel-btn {
    background-color: #D9D9D9;
}

.confrim-btn {
    background: #139d59;
    color: #ffffff;
}

/* .el-select {
    width: 100%;
} */
</style>