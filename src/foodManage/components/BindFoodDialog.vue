<template>
  <CommonDialog :dialogShow.sync="show" :title="gL('bindFoodTitle')">
    <div
      class="spec-bind-content-box"
      :class="gLang == 1 ? 'content-box-ug' : 'content-box-zh'"
    >
      <div class="foods-list">
        <div class="cate-box">
          <div
            class="cate-item"
            :class="cateActive == item.id ? 'active' : ''"
            v-for="item in cateList"
            :key="item.id"
            @click="clickCateItem(item.id)"
          >
            {{ gLang == 1 ? item.name_ug : item.name_zh }}
          </div>
        </div>
        <div class="food-box">
          <el-table
            
            class="table"
            ref="multipleTable"
            :data="foodsList"
            tooltip-effect="dark"
            style="width: 100%;font-size: 22px;line-height: 22px;"
            :row-key="row => row.id"
            @selection-change="handleSelectionChange"
            @row-click="changeFoodRow"
          >
            <el-table-column
              type="selection"
              width="55"
              :reserve-selection="true"
              :selectable="settingSelectable"
            >
            </el-table-column>
            <el-table-column
              :prop="gLang == 1 ? 'name_ug' : 'name_zh'"
              :label="gL('names')"
            >
              <template slot="default" slot-scope="scope">
                <div class="table-header-name-box">
                  <div :style="{direction: gLang == 1 ? 'rtl' : 'ltr'}">
                  {{  gLang == 1 ? scope.row.name_ug : scope.row.name_zh }}
                </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="food-check">
        <div class="food-title">{{ gL("selectedFoods") }} :</div>
        <div class="food-item-box">
          <template v-for="item in tableSelection">
            <div class="food-item" v-if="!item.hasSpec">
              <span>{{ gLang == 1 ? item.name_ug : item.name_ug }}</span>
              <i class="el-icon-circle-close" @click="deleteFood(item)"></i>
            </div>
          </template>
         
        </div>
      </div>
      <div class="button-box">
        <!-- <div class="btn chooise-btn" @click="chooiseAll">
          {{ gL("chooiseAll") }}
        </div> -->
        <div class="btn cancel-btn btn-center" @click="cancel">
          {{ gL("cancel") }}
        </div>
        <div class="btn confrim-btn" @click="confirm">
          {{ gL("confirm") }}
        </div>
      </div>
    </div>
  </CommonDialog>
</template>

<script>
import CommonDialog from "../../components/CommonDialog.vue";
import {
  getFoodCategoriesAPI,
  getFoodsParentsAPI
} from "../../api/index.js";
export default {
  name: "BindFoodDialog",
  components: {
    CommonDialog
  },
  created() {
    this.gLang = localStorage.getItem("langId");
  },
  data() {
    return {
      show: false,
      gLang: 1,
      specFoodsList: [],
      cateList: [],
      foodsList: [],
      loading: null,
      cateActive: 0,
      tableSelection: [],
      id: 0,
      selectedFoods: []
    };
  },
  methods: {
    // 显示弹出框
    showDialog(id, selectedFoods) {
      this.loading = this.$loading();
      this.id = id;
      this.specFoodsList = selectedFoods;
      this.getFoodCategory();
    },

    // 关闭弹出框
    closeDialog() {
      this.show = false;
    },

    // 获取美食分类
    getFoodCategory() {
      
      getFoodCategoriesAPI()
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.cateList = response.data.data;
            this.cateList.unshift({
              id: 0,
              name_ug: this.gL("all"),
              name_zh: this.gL("all")
            });
            this.getFoodList();
          }
        })
        .catch(e => {
          this.loading.close();
        });
    },

    // 获取美食列表
    getFoodList() {
      getFoodsParentsAPI(this.cateActive)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            
            let items = [];
            let selectedItems = [];
            response.data.data.forEach(item => {
              if (this.specFoodsList[item.pid]) {
                item.hasSpec = true
                selectedItems.push(item)
              } else {
                item.hasSpec = false
              }
              items.push(item);
            })
            this.foodsList = items;
            
            

           if (this.show) {
              // 选中已关联的美食
              selectedItems.forEach(item => {
                this.$refs.multipleTable.toggleRowSelection(item);
              });
           } else {
            this.show = true;
            this.$nextTick(() => {
              // 选中已关联的美食
              selectedItems.forEach(item => {
                this.$refs.multipleTable.toggleRowSelection(item);
              });
            });
           }
          }
        })
        .finally(e => {
          this.loading.close();
        });
    },

    // 点击分类
    clickCateItem(id) {
      this.cateActive = id;
      this.getFoodList();
    },

    // 点击美食
    changeFoodRow(row) {
      if (row.hasSpec) {
        return false;
      }
      this.$refs.multipleTable.toggleRowSelection(row);
    },

    // 清空全部已选状态
    chooiseAll() {
      this.$refs.multipleTable.clearSelection();
    },

    // 删除已选美食
    deleteFood(row) {
      this.$refs.multipleTable.toggleRowSelection(row, false);
    },

    // 表格选中函数
    handleSelectionChange(selection) {
      this.tableSelection = selection;
    },

    // 是否能选中行
    settingSelectable(row, index) {
      return !row.hasSpec;
      return true;
    },

    // 关闭弹出框
    cancel() {
      this.show = false;
    },

    // 确认绑定
    confirm() {
      
      const data = this.tableSelection.filter(item => !item.hasSpec).map(item => {
        return {
          id: item.id,
          pid: item.pid,
          food_name_ug: item.name_ug,
          food_name_zh: item.name_zh,
          category_name_ug: item.category_name_ug,
          category_name_zh: item.category_name_zh,
          price: 0,
          vip_price: 0,
          cost_price: 0
        };
      });
      if (data.length == 0) {
        this.$message({
          message: this.gL("chooiseFood"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }
      this.$emit("confirm", this.id, data);
    }
  }
};
</script>

<style>


.spec-bind-content-box .table.el-table th {
  background-color: #f2f2f2;
}
</style>
<style lang="less" scoped>
.spec-bind-content-box {
  height: 600px;

  &.content-box-ug {
    direction: rtl;

    .cate-box {
      margin-right: 10px;
    }
  }

  &.content-box-zh {
    direction: ltr;

    .cate-box {
      margin-left: 10px;
    }
  }

  .foods-list {
    height: 360px;
    display: flex;
    flex-direction: row-reverse;

    .cate-box {
      flex-grow: 0;
      flex-shrink: 0;
      height: 100%;
      overflow-y: auto;
      background-color: #e6e6e6;
      padding: 10px;

      .cate-item {
        padding: 10px;
        margin-bottom: 8px;
        cursor: pointer;
        font-size: 22px;
        line-height: 22px;
        border: 1px solid transparent;

        &.active {
          border-color: #139d59;
          background-color: #cae8da;
          color: #139d59;
        }
      }
    }

    .food-box {
      flex-shrink: 1;
      flex-grow: 1;
      height: 100%;
      overflow-y: auto;

      .table {
        width: 100%;
      }
    }
  }

  &.content-box-ug .food-check .food-item-box .food-item {
    margin-right: 6px;
  }

  &.content-box-zh .food-check .food-item-box .food-item {
    margin-left: 6px;
  }

  .food-check {
    height: 140px;
    border-top: 1px solid #d9d9d9;
    margin-top: 10px;
    padding: 10px 0px;
    box-sizing: border-box;
    display: flex;

    .food-title {
      font-size: 18px;
      margin-top: 10px;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .food-item-box {
      padding: 0px 10px;
      font-size: 18px;
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      height: 100%;
      overflow-y: auto;
    }

    .food-item {
      padding: 8px 10px;
      background-color: #f2f2f2;
      color: #6a6a6a;
      margin-bottom: 6px;
    }

    .el-icon-circle-close {
      color: #cecece;
      cursor: pointer;
    }
  }

  .button-box {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    padding: 10px 0px 20px;
    border-top: 1px solid #d9d9d9;
  }
  .btn {
    flex-grow: 1;
    font-size: 26px;
    text-align: center;
    padding: 15px 0px;
    cursor: pointer;
  }
  .btn-center {
    margin-inline: 20px;
  }
  .chooise-btn {
    background-color: #ffe4e4;
    color: #ff5252;
  }
  .cancel-btn {
    background-color: #d9d9d9;
  }
  .confrim-btn {
    background: #139d59;
    color: #ffffff;
  }
}
</style>
