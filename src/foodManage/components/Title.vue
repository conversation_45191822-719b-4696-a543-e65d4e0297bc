<template>
    <div class="title">
        <a class="back" @click="$router.back()">
            <el-icon name="arrow-left"></el-icon> {{gL('back')}}
        </a>
        <div class="text">{{title}}</div>
        <div class="back"></div>
    </div>
</template>
<script>
export default {
    props: {
        title: {
            type: String,
            default: ''
        }
    }
}
</script>
<style scoped>
.title {
  direction: ltr;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0px;
  height: 60px;
  line-height: 60px;
  background: rgba(230, 230, 230, 1);
  color: rgba(30, 30, 30, 1);
  font-size: 22px;
  .back {
    width: 130px;
    padding: 0px 15px;
  }
  a.back {
    cursor: pointer;
    color: #4D4D4D;
  }
  .text {
    flex-grow: 1;
    text-align: center;
  }
}
</style>