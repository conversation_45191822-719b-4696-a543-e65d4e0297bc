<script>
export default {
  name: "food-spec",
  props: {
    item: {
      type: Object,
      default: {}
    },
    index: {
      type: Number,
      default: 0
    },
    showDelete: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      specs: [],
      specRules: {
        spec_id: {
          type: "number",
          required: true,
          message: this.gL("plaeseInput"),
          trigger: "change"
        },
        price: {
          type: "number",
          required: true,
          message: this.gL("plaeseInput"),
          trigger: "blur"
        },
        vip_price: {
          type: "number",
          required: true,
          message: this.gL("plaeseInput"),
          trigger: "blur"
        },
        cost_price: {
          type: "number",
          required: true,
          message: this.gL("plaeseInput"),
          trigger: "blur"
        }
      }
    };
  },
  methods: {
    specChanged(item, e) {
      const spec = this.specs.find(item => item.id === e);
      item.name = this.localeName(spec);
    },
  }
};
</script>

<template>
  <div class="spec-item">
    <div class="header">
      <div>{{ item.name }}</div>
      <el-button type="text" icon="el-icon-delete" v-if="showDelete" @click="deleteSpec(index)">{{ gL('delete') }}</el-button>
    </div>
    <div class="spec-content">
      <el-form-item :prop="`specs[${index}].spec_id`" :label="gL('specName')" :rules="specRules.spec_id" label-width="auto">
        <el-select
          class="food-input w400"
          v-model.number="item.spec_id"
          :placeholder="gL('specName')"
          @change="$e => specChanged(item, $e)"
        >
          <el-option
            v-for="(item, index) in specs"
            :key="index"
            :label="localeName(item)"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <div class="divider"></div>
      <div class="prices">
        <el-form-item :prop="`specs[${index}].price`" :label="gL('price')" :rules="specRules.price">
          <el-input
            class="food-input"
            v-model.number="item.price"
            :placeholder="gL('price')"
          >
            <template slot="append">{{ gL('rmb') }}</template>
          </el-input>
        </el-form-item>
        <el-form-item :prop="`specs[${index}].vip_price`" :label="gL('vipPrice')" :rules="specRules.vip_price">
          <el-input
            class="food-input"
            v-model.number="item.vip_price"
            :placeholder="gL('vipPrice')"
          >
            <template slot="append">{{ gL('rmb') }}</template>
          </el-input>
        </el-form-item>
        <el-form-item :prop="`specs[${index}].cost_price`" :label="gL('costPrice')" :rules="specRules.cost_price">
          <el-input
            class="food-input"
            v-model.number="item.const_price"
            :placeholder="gL('costPrice')"
          >
            <template slot="append">{{ gL('rmb') }}</template>
          </el-input>
        </el-form-item>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

.spec-item {
  margin-bottom: 20px;
  border: 1px solid #D9D9D9;
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  line-height: 40px;
  background: #D9D9D9;
  color: rgba(30, 30, 30, 1);
  font-size: 18px;
  padding: 0px 15px;
  cursor: pointer;

  .el-button {
    margin-left: 10px;
  }
}

.spec-content {
  padding: 20px;
}
</style>
<style lang="less">
.specs .spec-item {
  .divider {
    height: 1px;
    background: #D9D9D9;
    margin: 20px -20px;
  }
  .prices {
    display: flex;
    align-content: space-between;
    .el-form-item {
      flex: 1;

      & + .el-form-item {
        margin-left: 20px;
      }
    }

    .el-form-item__label {
      width: auto !important;
      float: none;
      display: block;
      text-align: start;
    }

    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>
