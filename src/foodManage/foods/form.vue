<template>
  <div class="food-form">
    <div class="title">
      <a class="back" @click="$router.back()">
        <el-icon name="arrow-left"></el-icon>
        {{ gL('back') }}
      </a>
      <h2 class="text">{{ gL("addFood") }}</h2>
      <div class="back"></div>
    </div>
    <div class="form-content">
      <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="210px">
        <el-row :gutter="50">
          <el-col :span="12">
            <el-form-item prop="name_ug" class="ug-input-box" :label="gL('nameUg')">
              <el-autocomplete
                class="food-input ug-input"
                v-model="form.name_ug"
                :fetch-suggestions="querySearch"
                :placeholder="gL('inputFoodNameUg')"
                value-key="name_ug"
                @input="getCategoryImage"
                :trigger-on-focus="false"
              ></el-autocomplete>
            </el-form-item>
            <el-form-item prop="food_category_id" class="ug-input-box" :label="gL('foodCate')">
              <el-select
                class="food-input"
                v-model="form.food_category_id"
                :placeholder="gL('foodCate')"
              >
                <el-option
                  v-for="(item, index) in categories"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="sort" :label="gL('sort')">
              <el-input
                :placeholder="gL('sort')"
                v-model="form.sort"
                type="number"
                :min="0"
                oninput="if(value.length>3)value=value.slice(0,3)"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="name_zh" :label="gL('nameZh')">
              <el-input
                v-model="form.name_zh"
                :placeholder="gL('inputFoodNameZh')"
                :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                maxlength="40"
              ></el-input>
            </el-form-item>
            <el-form-item prop="format_id" :label="gL('format')">
              <el-select
                class="food-input"
                v-model="form.format_id"
                :placeholder="gL('format')"
              >
                <el-option
                  v-for="(item, index) in formats"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="shortcut_code" :label="gL('shortcut')">
              <el-input
                class="food-input"
                v-model.number="form.shortcut_code"
                :placeholder="gL('inputShortcutCode')"
                :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                :maxlength="4"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="image" :label="gL('state')">
              <el-radio-group v-model="form.state">
                <el-radio :label="1">{{ gL('open') }}</el-radio>
                <el-radio :label="0">{{ gL('off') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="description" :label="gL('img')">
            </el-form-item>
            <div class="el-form-item is-required">
              <label for="description" class="el-form-item__label" style="width: 210px;">{{
                  gL('foodSpecTitle')
                }}</label>
              <div class="el-form-item__content" style="margin-left: 210px;">
                <div class="specs">
                  <spec-item :item.sync="specItem" :index="index" :show-delete="form.specs.length > 1" v-for="(specItem, index) in form.specs" :key="index" />
                  <el-button class="add-btn" icon="el-icon-plus" @click="addSpec">
                    {{ gL('addSpec') }}
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="footer">
      <el-button type="primary" @click="submit">{{ gL('submit') }}</el-button>
      <el-button @click="$router.back()">{{ gL('cancel') }}</el-button>
    </div>
  </div>
</template>
<script>
import {debounce} from "@/utils/utils";
import SpecItem from "./module/SpecItem";
import {
  getFoodCategoriesListAPI,
  getFoodsFormatAPI,
  getFoodSpecsAPI,
  getLibraryFoodsImageAPI,
  getLibraryFoodsListAPI
} from "@/api";

export default {
  name: 'FoodForm',
  components: {
    SpecItem
  },
  data() {
    return {
      categories: [],
      formats: [],
      specs: [],
      imageList: [],
      form: {
        name_ug: '',
        name_zh: '',
        food_category_id: null,
        format_id: null,
        sort: 1,
        shortcut_code: '',
        state: 1,
        price: 0,
        description: '',
        image: '',
        specs: [
          {
            id: null,
            spec_id: null,
            name: null,
            price: null,
            vip_price: null,
            cost_price: null,
          }
        ]
      },

      rules: {
        format_id: [
          {required: true, message: this.gL("plaeseInput"), trigger: "blur"}
        ],
        sort: [
          {required: true, message: this.gL("plaeseInput"), trigger: "blur"}
        ],
        shortcut_code: [
          {type: "number", message: this.gL("inputShortcutCodeNumber")}
        ],
        food_category_id: [
          {
            required: true,
            message: this.gL("selectFoodType"),
            trigger: "change"
          }
        ],
        name_zh: [
          {
            required: true,
            message: this.gL("inputFoodNameZh"),
            trigger: "blur"
          }
        ],
        name_ug: [
          {
            required: true,
            message: this.gL("inputFoodNameUg"),
            trigger: "blur"
          }
        ],
        cost_price: [
          {required: true, message: this.gL("plaeseInput"), trigger: "blur"}
        ],
        vip_price: [
          {required: true, message: this.gL("plaeseInput"), trigger: "blur"}
        ],
        price: [
          {required: true, message: this.gL("plaeseInput"), trigger: "blur"}
        ],
        image: [
          {required: true, message: this.gL("uploadImg"), trigger: "blur"}
        ],

      },
    }
  },
  computed: {
    gLang() {
      return this.$store.state.lang;
    }
  },
  created() {
    this.getCatList();
    this.getSpecList();
    this.getFormatList();
  },
  methods: {
    //获取角色列表
    getCatList() {
      getFoodCategoriesListAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.categories = response.data.data;
        }
      });
    },
    getSpecList() {
      getFoodSpecsAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.specs = response.data.data;
        }
      });
    },
    getFormatList() {
      getFoodsFormatAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.formats = response.data.data;
        }
      });
    },
    getCategoryImage() {
      debounce(() => {
        console.log("getCategoryImage");
        getLibraryFoodsImageAPI({
          word: this.form.name_ug
        }).then(response => {
          console.log("response1", response);
          if (response.status >= 200 && response.status < 300) {
            this.imageList = response.data.data.images;
          }
        });
      });
    },
    querySearch(value, callback) {
      const param = {
        page: 1,
        q: value
      };
      debounce(() => {
        getLibraryFoodsListAPI(param).then(response => {
          if (response.status >= 200 && response.status < 300) {
            console.log(response.data.data.data, "ssss");
            callback(response.data.data.data);
          }
        });
      }, 500);
    },
    addSpec() {
      this.form.specs.push({
        id: null,
        spec_id: null,
        name: null,
        price: null,
        vip_price: null,
        cost_price: null,
      });
    },
    deleteSpec(index) {
      this.form.specs.splice(index, 1);
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log("submit", this.form);
          this.$message.success("提交成功");
        }
      });
    },
  },
}
</script>
<style scoped lang="less">
.food-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  overflow-y: auto;
  .title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 60px;
    line-height: 60px;
    background: rgba(230, 230, 230, 1);
    color: rgba(30, 30, 30, 1);
    font-size: 22px;

    .back {
      width: 130px;
      padding: 0px 15px;
    }

    a.back {
      cursor: pointer;
      color: #4D4D4D;
    }

    .text {
      flex-grow: 1;
      text-align: center;
    }
  }

  .form-content {
    padding: 35px;
    flex: 1;

    .food-input {
      width: 100%;
      height: 40px;
    }

    .ug-input-box {
      margin-bottom: 20px;
    }

    .zh-input {
      text-align: left;
      font-size: 16px;
    }

    .uy-input {
      text-align: right;
      font-size: 16px;
    }
  }

  .specs {
    width: 800px;
    display: flex;
    flex-direction: column;

    .add-btn{
      text-align: center;
      color : #139d59;
      font-weight: bold;
      border: none;
      border-radius: 0;
      background: rgba(230, 230, 230, 1);
    }
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    padding: 20px;
    background: #F5F5F5;
    border-top: 1px solid #D9D9D9;
    height: 60px;
    line-height: 60px;
    .el-button {
      margin-left: 20px;
    }
  }
}
.w400 {
  width: 400px !important;
}


</style>
<style lang="less">
.specs .spec-item {
  .divider {
    height: 1px;
    background: #D9D9D9;
    margin: 20px -20px;
  }
  .prices {
    display: flex;
    align-content: space-between;
    .el-form-item {
      flex: 1;

      & + .el-form-item {
        margin-left: 20px;
      }
    }

    .el-form-item__label {
      width: auto !important;
      float: none;
      display: block;
      text-align: start;
    }

    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>
