<template>
  <div class="food-form">
    <div class="title">
      <a class="back" @click="$router.back()">
        <el-icon name="arrow-left"></el-icon>
        {{ gL('back') }}
      </a>
      <h2 class="text">{{ gL("addFood") }}</h2>
      <div class="back"></div>
    </div>
    <div class="form-content">
      <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="210px">
        <el-row :gutter="50">
          <el-col :span="12">
            <el-form-item prop="name_ug" class="ug-input-box" :label="gL('nameUg')">
              <el-autocomplete
                class="food-input"
                :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                v-model="form.name_ug"
                :fetch-suggestions="querySearch"
                :placeholder="gL('inputFoodNameUg')"
                value-key="name_ug"
                @input="getCategoryImage"
                :trigger-on-focus="false"
              ></el-autocomplete>
            </el-form-item>
            <el-form-item prop="food_category_id" class="ug-input-box" :label="gL('foodCate')">
              <el-select
                class="food-input"
                v-model="form.food_category_id"
                :placeholder="gL('foodCate')"
              >
                <el-option
                  v-for="(item, index) in categories"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="sort" :label="gL('sort')">
              <el-input
                :placeholder="gL('sort')"
                v-model="form.sort"
                type="number"
                :min="0"
                oninput="if(value.length>3)value=value.slice(0,3)"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="name_zh" :label="gL('nameZh')">
              <el-input
                v-model="form.name_zh"
                :placeholder="gL('inputFoodNameZh')"
                :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                maxlength="40"
              ></el-input>
            </el-form-item>
            <el-form-item prop="format_id" :label="gL('format')">
              <el-select
                class="food-input"
                v-model="form.format_id"
                :placeholder="gL('format')"
              >
                <el-option
                  v-for="(item, index) in formats"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="shortcut_code" :label="gL('shortcut')">
              <el-input
                class="food-input"
                v-model.number="form.shortcut_code"
                :placeholder="gL('inputShortcutCode')"
                :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                :maxlength="4"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="image" :label="gL('state')">
              <el-radio-group v-model="form.state">
                <el-radio :label="1">{{ gL('open') }}</el-radio>
                <el-radio :label="0">{{ gL('off') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="description" :label="gL('img')">
            </el-form-item>
            <div class="el-form-item">
              <label for="description" class="el-form-item__label" style="width: 210px;">{{
                  gL('foodSpecTitle')
                }}</label>
              <div class="el-form-item__content" style="margin-left: 210px;">
                <div class="specs">
                  <div class="spec-item" v-for="(specItem, index) in form.specs">
                    <div class="header">
                      <div>{{ specItem.name }}</div>
                      <el-button type="text" icon="el-icon-delete" v-if="form.specs.length > 1" @click="deleteSpec(index)">{{ gL('delete') }}</el-button>
                    </div>
                    <div class="spec-content">
                      <el-form-item :prop="`specs[${index}].id`" :label="gL('specName')" label-width="auto">
                        <el-select
                          class="food-input w400"
                          v-model="specItem.spec"
                          :placeholder="gL('specName')"
                          @change="$e => specChanged(specItem, $e)"
                        >
                          <el-option
                            v-for="(item, index) in specs"
                            :key="index"
                            :label="localeName(item)"
                            :value="item.id"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <div class="divider"></div>
                      <div class="prices">
                        <el-form-item :prop="`specs[${index}].price`" :label="gL('price')">
                          <el-input
                            class="food-input"
                            v-model.number="specItem.price"
                            :placeholder="gL('price')"
                            :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                          >
                            <template slot="append">{{ gL('rmb') }}</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item :prop="`specs[${index}].vip_price`" :label="gL('vipPrice')">
                          <el-input
                            class="food-input"
                            v-model.number="specItem.vip_price"
                            :placeholder="gL('vipPrice')"
                            :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                          >
                            <template slot="append">{{ gL('rmb') }}</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item :prop="`specs[${index}].cost_price`" :label="gL('costPrice')">
                          <el-input
                            class="food-input"
                            v-model.number="specItem.const_price"
                            :placeholder="gL('costPrice')"
                            :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                          >
                            <template slot="append">{{ gL('rmb') }}</template>
                          </el-input>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                  <el-button class="add-btn" icon="el-icon-plus" @click="addSpec">
                    {{ gL('addSpec') }}
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="footer">
      <el-button type="primary" @click="submit">{{ gL('submit') }}</el-button>
      <el-button @click="$router.back()">{{ gL('cancel') }}</el-button>
    </div>
  </div>
</template>
<script>
import {debounce} from "@/utils/utils";
import {
  getFoodCategoriesListAPI,
  getFoodsFormatAPI,
  getFoodSpecsAPI,
  getLibraryFoodsImageAPI,
  getLibraryFoodsListAPI
} from "@/api";

export default {
  name: 'FoodForm',
  data() {
    return {
      categories: [],
      formats: [],
      specs: [],
      imageList: [],
      form: {
        name_ug: '',
        name_zh: '',
        food_category_id: null,
        format_id: null,
        sort: 1,
        shortcut_code: '',
        state: 1,
        price: 0,
        description: '',
        image: '',
        specs: [
          {
            id: null,
            spec_id: null,
            name: null,
            price: null,
            vip_price: null,
            cost_price: null,
          }
        ]
      },

      rules: {
        format_id: [
          {required: true, message: this.gL("plaeseInput"), trigger: "blur"}
        ],
        sort: [
          {required: true, message: this.gL("plaeseInput"), trigger: "blur"}
        ],
        shortcut_code: [
          {type: "number", message: this.gL("inputShortcutCodeNumber")}
        ],
        food_category_id: [
          {
            required: true,
            message: this.gL("selectFoodType"),
            trigger: "change"
          }
        ],
        name_zh: [
          {
            required: true,
            message: this.gL("inputFoodNameZh"),
            trigger: "blur"
          }
        ],
        name_ug: [
          {
            required: true,
            message: this.gL("inputFoodNameUg"),
            trigger: "blur"
          }
        ],
        cost_price: [
          {required: true, message: this.gL("plaeseInput"), trigger: "blur"}
        ],
        vip_price: [
          {required: true, message: this.gL("plaeseInput"), trigger: "blur"}
        ],
        price: [
          {required: true, message: this.gL("plaeseInput"), trigger: "blur"}
        ],
        image: [
          {required: true, message: this.gL("uploadImg"), trigger: "blur"}
        ],
        specs: {
          type: "array",
          required: true,
          message: this.gL("plaeseInput"),
          fields: {
            spec_id: {
              type: "number",
              required: true,
              message: this.gL("plaeseInput"),
              trigger: "blur"
            },
            price: {
              type: "number",
              required: true,
              message: this.gL("plaeseInput"),
              trigger: "blur"
            },
            vip_price: {
              type: "number",
              required: true,
              message: this.gL("plaeseInput"),
              trigger: "blur"
            },
            cost_price: {
              type: "number",
              required: true,
              message: this.gL("plaeseInput"),
              trigger: "blur"
            }
          }
        },
      },
    }
  },
  computed: {
    gLang() {
      return this.$store.state.lang;
    }
  },
  created() {
    this.getCatList();
    this.getSpecList();
    this.getFormatList();
  },
  methods: {
    //获取角色列表
    getCatList() {
      getFoodCategoriesListAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.categories = response.data.data;
        }
      });
    },
    getSpecList() {
      getFoodSpecsAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.specs = response.data.data;
        }
      });
    },
    getFormatList() {
      getFoodsFormatAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.formats = response.data.data;
        }
      });
    },
    getCategoryImage() {
      debounce(() => {
        console.log("getCategoryImage");
        getLibraryFoodsImageAPI({
          word: this.form.name_ug
        }).then(response => {
          console.log("response1", response);
          if (response.status >= 200 && response.status < 300) {
            this.imageList = response.data.data.images;
          }
        });
      });
    },
    querySearch(value, callback) {
      const param = {
        page: 1,
        q: value
      };
      debounce(() => {
        getLibraryFoodsListAPI(param).then(response => {
          if (response.status >= 200 && response.status < 300) {
            console.log(response.data.data.data, "ssss");
            callback(response.data.data.data);
          }
        });
      }, 500);
    },
    specChanged(specItem, e) {
      const spec = this.specs.find(item => item.id === e);
      specItem.name = this.localeName(spec);
    },
    addSpec() {
      this.form.specs.push({
        id: null,
        spec_id: null,
        name: null,
        price: null,
        vip_price: null,
        cost_price: null,
      });
    },
    deleteSpec(index) {
      this.form.specs.splice(index, 1);
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log("submit", this.form);
          this.$message.success("提交成功");
        }
      });
    },
  },
}
</script>
<style scoped lang="less">
.food-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  overflow-y: auto;
  .title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 60px;
    line-height: 60px;
    background: rgba(230, 230, 230, 1);
    color: rgba(30, 30, 30, 1);
    font-size: 22px;

    .back {
      width: 130px;
      padding: 0px 15px;
    }

    a.back {
      cursor: pointer;
      color: #4D4D4D;
    }

    .text {
      flex-grow: 1;
      text-align: center;
    }
  }

  .form-content {
    padding: 35px;
    flex: 1;

    .food-input {
      width: 100%;
      height: 40px;
    }

    .ug-input-box {
      margin-bottom: 20px;
    }

    .zh-input {
      font-size: 16px;
    }

    .uy-input {
      font-size: 16px;
    }
  }

  .specs {
    width: 800px;
    display: flex;
    flex-direction: column;

    .spec-item {
      margin-bottom: 20px;
      border: 1px solid #D9D9D9;
    }

    .header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      line-height: 40px;
      background: #D9D9D9;
      color: rgba(30, 30, 30, 1);
      font-size: 18px;
      padding: 0px 15px;
      cursor: pointer;

      .el-button {
        margin-left: 10px;
      }
    }

    .spec-content {
      padding: 20px;
    }
    .add-btn{
      text-align: center;
      color : #139d59;
      font-weight: bold;
      border: none;
      border-radius: 0;
      background: rgba(230, 230, 230, 1);
    }
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    padding: 20px;
    background: #F5F5F5;
    border-top: 1px solid #D9D9D9;
    height: 60px;
    line-height: 60px;
    .el-button {
      margin-left: 20px;
    }
  }
}
.w400 {
  width: 400px !important;
}


</style>
<style lang="less">
.specs .spec-item {
  .divider {
    height: 1px;
    background: #D9D9D9;
    margin: 20px -20px;
  }
  .prices {
    display: flex;
    align-content: space-between;
    .el-form-item {
      flex: 1;

      & + .el-form-item {
        margin-left: 20px;
      }
    }

    .el-form-item__label {
      width: auto !important;
      float: none;
      display: block;
      text-align: start;
    }

    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>
