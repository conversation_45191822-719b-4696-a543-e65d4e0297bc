<template>
<div class="food-list">
  <div
    class="adds"
    @click="addFood"
  >
    <span class="iconfont icon-jia-copy-copy"></span>
    <span>{{ gL("addFood") }}</span>
  </div>
  <div class="top topp">
    <div class="menu" :class="gLang == 2 ? 'menu-zh' : ''">
      <el-tabs
        v-model="activeName"
        type="card"
        class="menu-item"
        @tab-click="clickMenu"
      >
        <el-tab-pane
          v-for="item in categories"
          :key="item.id"
          :label="
              item.foods_count == undefined || Number.isNaN(item.foods_count)
                ? (gLang == 1 ? item.name_ug : item.name_zh)
                : (gLang == 1 ? item.name_ug : item.name_zh) + ' (' + item.foods_count + ')'
            "
          :name="item.item"
          :id="item.id"
        >
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="search">
      <div class="input">
        <span class="iconfont icon-search"></span>
        <el-input
          type="text"
          oninput="if(value.length>40)value=value.slice(0,40)"
          :placeholder="gL('shortcut') + '/' + gL('names')"
          v-model="query.keyword"
          v-on:input="getData"
          @keyup.enter="getData"
          clearable
        />
      </div>
    </div>
  </div>
  <div class="table">
    <el-table
      :data="foodList"
      header-cell-class-name="headers"
      :header-cell-style="{ background: '#e6e6e6' }"
      :cell-style="cell"
      row-class-name="row"
      v-loading="tableLoading"
      style="width: 100%"
      height="100%"
    >
      <el-table-column
        :label="gL('serialNumber')"
        type="index"
        align="center"
        width="60"
      >
      </el-table-column>
      <el-table-column align="center" width="80px" :label="gL('img')">
        <template slot-scope="{row}">
          <img
            :src="row.image + '?x-oss-process=style/w20'"
            width="50px"
            height="50px"
            class="food-image"
            alt=""
            style="border: 1px solid #cccccc"
          />
        </template>
      </el-table-column>
      <el-table-column prop="name" align="center" :label="gL('foodName')">
      </el-table-column>
      <el-table-column
        prop="shortcut_code"
        align="center"
        width="95px"
        :label="gL('shortcut')"
      >
      </el-table-column>
      <el-table-column
        prop="food_category_name"
        align="center"
        :label="gL('type')"
      >
      </el-table-column>
      <el-table-column
        prop="price"
        width="85px"
        align="center"
        :label="gL('price')"
      >
      </el-table-column>
      <el-table-column
        prop="vip_price"
        width="90px"
        align="center"
        :label="gL('vipPrice')"
      >
      </el-table-column>
      <el-table-column
        prop="cost_price"
        align="center"
        width="100px"
        :label="gL('costPrice')"
      >
      </el-table-column>
      <el-table-column
        prop="sort"
        align="center"
        width="80px"
        :label="gL('sort')"
      >
      </el-table-column>
      <el-table-column
        align="center"
        width="120"
        :label="gL('foodScanSwitch')"
      >
        <template slot-scope="{$index,row}">
          <el-switch
            active-color="#13ce66"
            inactive-color="#ff4949"
            v-model="row.support_scan_order"
            @change="changeSwitchScanOrder($index, row)"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column align="center" width="70" :label="gL('state')">
        <template slot-scope="{$index,row}">
          <el-switch
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
            v-model="row.state"
            @change="changeSwitch($index, row)"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
        :label="gL('operation')"
        prop="dosome"
        align="center"
        width="120px"
      >
        <template slot-scope="{row}">
          <el-button
            type="text"
            icon="iconfont icon-shiliangzhinengduixiang"
            @click="editData(row)"
            circle
          ></el-button>
          <span style="padding-right: 9px; padding-left: 15px">
              <span class="line"></span>
            </span>
          <el-button
            type="text"
            class="danger"
            @click="delData(row)"
            icon="iconfont icon-qingkong"
            circle
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <modal
    :number_box="deleteConfirm.show"
    :modal_content="deleteConfirm.content"
    :modal_title="gL('tips')"
    @cancel="deleteConfirm.show = false"
    @confirm="delRowData"
  ></modal>
</div>
</template>
<script>
import {
  deleteFoodAPI,
  getChangeSupportScanOrderAPI,
  getFoodCategoriesListAPI,
  getFoodListAPI,
  putFoodStateAPI
} from "@/api";
import modal from "@/components/modal.vue";

export default {
  components: {modal},
  data() {
    return {
      gLang: 1,
      tableLoading: false,
      activeName: "first",
      categories: [],
      foodList: [],
      foodTempList: [],
      query: {
        food_category_id: "",
        keyword: "",
      },
      deleteConfirm: {
        show: false,
        content: ""
      }
    }
  },
  created() {
    this.gLang = localStorage.getItem("langId");
  },
  mounted() {
    this.getCatList();
    this.getData();
  },
  methods: {
    addFood() {
      this.$router.push("/foodManage/food/add");
    },
    getCatList() {
      this.cate_list = [];
      getFoodCategoriesListAPI().then(response => {
        if (response.status >= 200 && response.status < 300) {
          var count = 0;
          response.data.data.forEach(item => {
            count += item.foods_count;
          });
          var all = {
            name_ug: this.gL("all"),
            name_zh: this.gL("all"),
            item: "first",
            id: 0,
            foods_count: count
          };
          response.data.data.unshift(all);
          this.categories = response.data.data;
          response.data.data.forEach(item => {
            if (item.id != 0) {
              this.cate_list.push(item);
            }
          });
        }
      });
    },
    //获取数据
    getData() {
      this.tableLoading = true;
      getFoodListAPI(this.query)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.foodList = response.data.data;
            if (!this.foodTempList.length || !this.activeId) {
              this.foodTempList = response.data.data;
            }
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    cell({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 2) {
        if (this.gLang == 1) {
          return "direction: rtl";
        }
      }
    },
    //点击点菜
    clickMenu(e) {
      if (e.$attrs.id !== 0) {
        this.query.food_category_id = e.$attrs.id;
      }
      this.getData();
    },
    //开关
    changeSwitch(index, row) {
      putFoodStateAPI(row.id)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.$message({
              message: this.gL("successfulOperation"),
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
          }
        })
        .catch(err => {
          this.getData();
        });
    },
    // 扫码点菜小程序下单switch
    changeSwitchScanOrder(index, row) {
      getChangeSupportScanOrderAPI(row.id)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.$message({
              message: this.gL("successfulOperation"),
              type: "success",
              customClass: this.$toastClass(),
              offset: 120
            });
          }
        })
        .catch(err => {
          this.getData();
        });
    },
    //删除
    delData(row) {
      this.id = row.id;
      this.deleteConfirm.show = true;
      const name = `《${row.name}》`
      if (this.gLang == 1) {
        this.deleteConfirm.content =
          name +
          this.gL("confirs") +
          this.gL("confirmdelete");
      } else {
        this.deleteConfirm.content =
          this.gL("confirmdelete") +
          name +
          this.gL("confirs");
      }
    },
    delRowData() {
      deleteFoodAPI(this.id).then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.$message({
            message: this.gL("successfulOperation"),
            type: "success",
            customClass: this.$toastClass(),
            offset: 120
          });
          this.getData();
        }
      }).finally(() => {
        this.deleteConfirm.show = false;
      });
    },
  }
}

</script>
<style scoped lang="less">
.food-list {
  position: relative;
  height: 100%;
}
.adds {
  padding: 0 36px;
  color: #fff;
  font-size: 22px;
  background: #ff9c00;
  cursor: pointer;
  display: inline-block;
  justify-content: center;
  height: 48px;
  line-height: 48px;
  position: absolute;
  top: -53px;
  right: 5px;

  .iconfont {
    font-size: 18px;
  }
}
.top {
  height: 50px;
  width: 100%;
  background-color: #2e3033;
  display: flex;
  justify-content: space-between;
  .menu {
    width: 82%;
    display: flex;
    color: #fff;
    height: 100%;
    overflow: hidden;
    .el-tabs {
      width: 100%;
    }
  }
  .search {
    width: 280px;
    background: #4d4d4d;
    margin: 5px 15px 5px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .input {
      //  width: 70%;
      display: flex;
      align-items: center;
    }
    input {
      outline: none;
      font-size: 22px;
      color: #ffffff;
      width: 70%;
      background: #4d4d4d;
      &::placeholder {
        color: #cccccc;
      }
    }
    .icon-search {
      color: #cccccc;
      font-size: 24px;
      padding: 0 10px;
    }
    .del {
      width: 20%;
      height: 100%;
      color: #cccccc;
      justify-content: center;
      display: flex;
      align-items: center;
      cursor: pointer;
      .iconfont {
        font-size: 22px;
      }
    }
  }
}
.table{
  height: 93%;
  overflow-y: scroll;
}
</style>
