<template>
  <div class="wrap" :class="gLang == 2 ? 'wrap-zh' : ''">
    <el-form
      class="form"
      :class="gLang == 1 ? 'form-ug' : ''"
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      :label-width="gLang == 1 ? '250px' : '180px'"
    >
      <div class="form-item">
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="name_ug"
          :label="gL('shopName')"
        >
          <el-input
            v-model="ruleForm.name_ug"
            :placeholder="gL('inputNameug')"
            :class="gLang == 1 ? 'uy-input' : 'zh-input'"
            maxlength="60"
          ></el-input>
        </el-form-item>
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="name_zh"
          :label="gL('shopName')"
        >
          <el-input
            v-model="ruleForm.name_zh"
            :placeholder="gL('inputName')"
            :class="gLang == 1 ? 'uy-input' : 'zh-input'"
            maxlength="60"
          ></el-input>
        </el-form-item>
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="phone"
          :label="gL('phoneNumber')"
        >
          <el-input
            v-model="ruleForm.phone"
            :placeholder="gL('phoneNumber')"
            type="text"
            maxlength="12"
            @input="testig"
          ></el-input>
        </el-form-item>
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="address_ug"
          :label="gL('shopAdrUg')"
        >
          <el-input
            type="textarea"
            :class="gLang == 1 ? 'uy-input' : 'zh-input'"
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-model="ruleForm.address_ug"
            :placeholder="gL('inputShopAdr')"
            maxlength="60"
          ></el-input>
        </el-form-item>
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="address_zh"
          :label="gL('shopAdrZh')"
        >
          <el-input
            type="textarea"
            :class="gLang == 1 ? 'uy-input' : 'zh-input'"
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-model="ruleForm.address_zh"
            :placeholder="gL('inputShopAdrUg')"
            maxlength="60"
          ></el-input>
        </el-form-item>
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="logo"
          :label="gL('shopLogo')"
        >
          <div class="img-item">
            <el-upload
              class="avatar-uploader"
              :action="url"
              name="image"
              :data="imageData"
              :headers="imageHeader"
              :on-success="handleLogoSuccess"
              :on-error="handleImageError"
            >
              <img v-if="ruleForm.logo" :src="ruleForm.logo" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>
      </div>
      <div class="form-item">
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="category_id"
          :label="gL('rescate')"
        >
          <el-select
            v-model="ruleForm.category_id"
            :placeholder="gL('rescate')"
            style="width: 100%"
          >
            <el-option
              v-for="item in cate_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="lng"
          :label="gL('lng')"
        >
          <el-input
            v-model="ruleForm.lng"
            :placeholder="gL('lng')"
            oninput="if(value.length>18)value=value.slice(0,18)"
          ></el-input>
        </el-form-item>
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="lat"
          :label="gL('lat')"
        >
          <el-input
            v-model="ruleForm.lat"
            :placeholder="gL('lat')"
            oninput="if(value.length>18)value=value.slice(0,18)"
          ></el-input>
        </el-form-item>
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="business_license"
          :label="gL('license')"
        >
          <el-input
            type="textarea"
            :class="gLang == 1 ? 'uy-input' : 'zh-input'"
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-model="ruleForm.business_license"
            :placeholder="gL('license')"
            maxlength="18"
          ></el-input>
        </el-form-item>
        <el-form-item
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          :label="gL('businessCertificate')"
        >
          <div class="items">
            <div class="img-item">
              <el-upload
                class="avatar-uploader"
                :action="url"
                name="image"
                :data="imageData"
                :headers="imageHeader"
                :on-success="handleAvatarSuccess1"
                :on-error="handleImageError"
              >
                <img
                  v-if="ruleForm.business_certificate1"
                  :src="ruleForm.business_certificate1"
                  class="avatar"
                />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </div>
            <div class="img-item">
              <el-upload
                class="avatar-uploader"
                :action="url"
                name="image"
                :data="imageData"
                :headers="imageHeader"
                :on-success="handleAvatarSuccess2"
                :on-error="handleImageError"
              >
                <img
                  v-if="ruleForm.business_certificate2"
                  :src="ruleForm.business_certificate2"
                  class="avatar"
                />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </div>
            <div class="img-item">
              <el-upload
                class="avatar-uploader"
                :action="url"
                name="image"
                :data="imageData"
                :headers="imageHeader"
                :on-success="handleAvatarSuccess3"
                :on-error="handleImageError"
              >
                <img
                  v-if="ruleForm.business_certificate3"
                  :src="ruleForm.business_certificate3"
                  class="avatar"
                />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <!-- <el-form
      class="form-bottom"
      :class="gLang == 1 ? 'form-ug' : ''"
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      :label-width="gLang == 1 ? '400px' : '180px'"
    >
      <div class="form-bottom">
        <el-form-item
          :label="gL('passBack')"
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="cancel_password"
        >
          <el-col :span="6" :class="gLang == 2 ? 'col-zh' : 'col-ug'">
            <el-input
              type="password"
              v-model="ruleForm.cancel_password"
              :class="gLang == 1 ? 'uy-input' : 'zh-input'"
              maxlength="6"
              auto-complete="off"
            ></el-input>
          </el-col>
          <el-button
            class="pass-btn"
            :style="{
              marginLeft: gLang == 2 ? '150px' : '',
              marginRight: gLang == 1 ? '150px' : '',
            }"
            @click="clearCancelPassword"
            >{{ gL("cancelPass") }}</el-button
          >
        </el-form-item>
        <el-form-item
          :label="gL('endOrderPassBack')"
          :class="gLang == 2 ? 'item-zh' : 'item-ug'"
          prop="refund_password"
        >
          <el-col :span="6" :class="gLang == 2 ? 'col-zh' : 'col-ug'">
            <el-input
              type="password"
              v-model="ruleForm.refund_password"
              :class="gLang == 1 ? 'uy-input' : 'zh-input'"
              maxlength="6"
              auto-complete="off"
            ></el-input>
          </el-col>
          <el-button
            class="pass-btn"
            :style="{
              marginLeft: gLang == 2 ? '150px' : '',
              marginRight: gLang == 1 ? '150px' : '',
            }"
            @click="clearRefundPassword"
            >{{ gL("cancelPass") }}</el-button
          >
        </el-form-item>
      </div>
    </el-form> -->
    <div class="save" @click="save">{{ gL("save") }}</div>
  </div>
</template>

<style scoped lang="less">
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.form-bottom {
  padding: 0px 32px 10px;
}
.col-zh {
  float: left !important;
}
.col-ug {
  float: right !important;
}
.pass-btn {
  font-size: 22px !important;
  background: #ff9c00;
  color: #ffffff;
}
.wrap-zh {
  direction: rtl;
}
.item-zh {
  direction: ltr;
}
.item-ug {
  direction: rtl;
}
.wrap {
  height: 93%;
  overflow-y: scroll;
  .form {
    display: flex;
    justify-content: space-between;
    padding: 30px;
    padding-left: 0;
    flex-direction: row-reverse;
    .form-item {
      width: 48%;
    }
    .img-item {
      width: 125px;
      height: 90px;
      overflow: hidden;
      cursor: pointer;
      border: 1px solid #3333;
      &:last-child {
        margin-right: 0;
      }
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .items {
      display: flex;
      column-gap: 20px;
      // border: 1px solid #cccc;
      // border-radius:3px;
    }
    .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 125px;
      height: 90px;
      line-height: 90px;
      text-align: center;
    }
    .avatar {
      width: 125px !important;
      height: 90px !important;
      display: block;
    }
  }
  .save {
    width: 40%;
    padding: 15px 0;
    margin: 20px auto;
    text-align: center;
    background: #ff9c00;
    font-size: 22px;
    color: #ffffff;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
<script>
import { postClearPasswordAPI, getMerchantCategoriesAPI, getMerchantInfoAPI, putMerchatUpdateAPI, getProfileMeAPI } from "./../api/index.js"
var self;
export default {
  created: function () {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.url = "https://" + localStorage.getItem("ip_adr") + "/api/v1/upload/image";
    self.getCate();
    self.getData();
    this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
    this.imageHeader.MerchantNo = this.storeInfo.merchant_no;
  },
  data() {
    return {
      gLang: 1,
      ruleForm: {
        category_id: "",
        no: "",
        name_ug: "",
        name_zh: "",
        phone: "",
        address_ug: "",
        address_zh: "",
        business_license: "",
        lng: "",
        lat: "",
        business_certificate1: "",
        business_certificate2: "",
        business_certificate3: "",
        logo: "",
        // cancel_password: "",
        // refund_password: "",
      },
      rules: {
        category_id: [
          {
            required: true,
            message: this.gL("plaeseChooise"),
            trigger: "change",
          },
        ],
        name_ug: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        name_zh: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        phone: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        address_ug: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        address_zh: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        business_license: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        lng: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        lat: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        business_certificate1: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        business_certificate2: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        business_certificate3: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        business_certificate4: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
      },
      imageData: {
        folder: "foods",
      },
      imageHeader: {
        Authorization: "Bearer " + localStorage.getItem("token"),
        MerchantNo: "",
        "accept-language": localStorage.langId == 1 ? 'ug-CN' : 'zh-CN'
      },
      cate_list: [],
      url: "",
      storeInfo: {},
    };
  },
  methods: {
    /**
     * 清除退菜密码
     */
    clearCancelPassword() {
      postClearPasswordAPI({
        password_type: 1,
        MerchantNo: this.storeInfo.merchant_no,
      }).then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.getData();
          self.$setCookie("order_cancel_password", false);
        }
      });
    },
    /**
     * 清除结账后退菜密码
     */
    clearRefundPassword() {
      postClearPasswordAPI({
        password_type: 2,
        MerchantNo: this.storeInfo.merchant_no,
      }).then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.getData();
          self.$setCookie("order_refund_password", false);
        }
      });
    },
    getCate() {
      getMerchantCategoriesAPI().then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.cate_list = response.data.data;
        }
      });
    },
    getData() {
      let loading = this.$loading();
      getMerchantInfoAPI().then((response) => {
          console.log(response);
          if (response.status >= 200 && response.status < 300) {
            self.ruleForm.category_id = response.data.data.category_id;
            self.ruleForm.no = response.data.data.no;
            self.ruleForm.name_ug = response.data.data.name_ug;
            self.ruleForm.name_zh = response.data.data.name_zh;
            self.ruleForm.phone = response.data.data.phone;
            self.ruleForm.name_ug = response.data.data.name_ug;
            self.ruleForm.address_ug = response.data.data.address_ug;
            self.ruleForm.address_zh = response.data.data.address_zh;
            self.ruleForm.business_license =
              response.data.data.business_license;
            self.ruleForm.lng = response.data.data.lng;
            self.ruleForm.lat = response.data.data.lat;
            self.ruleForm.business_certificate1 =
              response.data.data.business_certificate1;
            self.ruleForm.business_certificate2 =
              response.data.data.business_certificate2;
            self.ruleForm.business_certificate3 =
              response.data.data.business_certificate3;
            self.ruleForm.logo = response.data.data.logo;
            // self.ruleForm.cancel_password =
            //   response.data.data.order_cancel_password;
            // self.ruleForm.refund_password =
            //   response.data.data.order_refund_password;
          }
        }).finally(() => {
          loading.close();
        });
    },
    handleAvatarSuccess1(res, file) {
      if (res.message) {
        self.ruleForm.business_certificate1 = res.message;
      } else {
        self.$message({
          message: self.gL("uploadError"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    handleAvatarSuccess2(res, file) {
      if (res.message) {
        self.ruleForm.business_certificate2 = res.message;
      } else {
        self.$message({
          message: self.gL("uploadError"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    handleAvatarSuccess3(res, file) {
      if (res.message) {
        self.ruleForm.business_certificate3 = res.message;
      } else {
        self.$message({
          message: self.gL("uploadError"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    handleLogoSuccess(res, file) {
      if (res.message) {
        self.ruleForm.logo = res.message;
      } else {
        self.$message({
          message: self.gL("uploadError"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    handleImageError(err) {
      const messages = JSON.parse(err.message);
      if (messages && messages.message) {
        this.$message({
          message: messages.message,
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
      }
    },
    save() {
      self.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // var postData = JSON.parse(JSON.stringify(self.ruleForm));
          // if(postData.refund_password == null ||  postData.refund_password == '' || postData.refund_password.length > 20){
          //   delete postData.refund_password
          // }
          // if(postData.cancel_password == null ||  postData.cancel_password == '' || postData.cancel_password.length > 20){
          //   delete postData.cancel_password
          // }

          putMerchatUpdateAPI(this.ruleForm).then((response) => {
              console.log(response);
              if (response.status >= 200 && response.status < 300) {
                self.$message({
                  message: self.gL("successfulOperation"),
                  type: "success",
                  customClass: self.$toastClass(),
                  offset: 120
                });

                getProfileMeAPI().then(userInfo => {
                  if(userInfo.data.data.id) {
                    const currentInfo = JSON.parse(localStorage.getItem("merchant_info"))
                    const userInfoData = {
                      ...currentInfo,
                      ...userInfo.data.data,
                    }
                    localStorage.setItem("merchant_info", JSON.stringify(userInfoData));
                    if (userInfoData.api_token)
                    localStorage.setItem("token", userInfoData.api_token);
                  }
                }).catch(userErr => {
                  console.log("获取用户数据失败", userErr)
                })
                // self.getData();
                // if (self.ruleForm.cancel_password != null) {
                //   self.$setCookie("order_cancel_password", true);
                //   localStorage.removeItem("token");
                //   localStorage.removeItem('merchant_info')
                //   localStorage.setItem("shouldRefresh", true);
                //   self.$router.push({ path: "/login" });
                // }
                // if (self.ruleForm.refund_password != null) {
                //   self.$setCookie("order_refund_password", true);
                //   localStorage.removeItem("token");
                //   localStorage.removeItem('merchant_info')
                //   localStorage.setItem("shouldRefresh", true);
                //   self.$router.push({ path: "/login" });
                // }
                // if (self.gLang == 1) {
                //   self.$setCookie("merchant_name", self.ruleForm.name_ug);
                // } else {
                //   self.$setCookie("merchant_name", self.ruleForm.name_zh);
                // }
              }
            });
        } else {
          return false;
        }
      });
    },
    //手机号加空格
    testig() {
      // self.$toPhone(self.ruleForm.phone);
    },
  },
  props: {},
};
</script>
