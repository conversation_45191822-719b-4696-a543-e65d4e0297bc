<template>
  <div class="wrap">
    <div class="download-box">
      <div class="qr-area">
        <qrcode-vue :value="downloadAddress" size="300" />
      </div>
      <div class="info-area">
        <div class="info">
          <label>{{ gL("app_name") }}</label>
          <span class="val">{{ appName }}</span>
        </div>
        <div class="info">
          <label>{{ gL("version") }}</label>
          <span class="val">{{ version }}</span>
        </div>
        <div class="info">
          <label>{{ gL("download_address") }}</label>
          <span class="val" style="user-select: all;">{{
            downloadAddress
          }}</span>
        </div>
        <div class="info">
          <div class="description" v-html="description"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.wrap {
  .download-box {
    display: flex;
    margin: 40px;
    .qr-area {
      flex: 0;
    }
    .info-area {
      flex: 1;
      padding-left: 40px;
      font-size: 22px;
      .info {
        margin-bottom: 10px;
        label {
          display: inline-block;
          width: 166px;
          text-align-last: justify;
          &::after {
            content: " : ";
          }
        }
        .description {
          max-width: 600px;
          background: #f7f7f7;
          padding: 20px;
          word-wrap: break-word;
          line-height: 36px;
        }
      }
    }
  }
}
</style>
<script>
let self;
import QrcodeVue from "qrcode.vue";
import { getAppUpdateAPI } from "./../api/index.js";
export default {
  data() {
    return {
      gLang: 1,
      appName: "",
      version: "",
      description: "",
      downloadAddress: ""
    };
  },
  created() {
    String.prototype.replaceAll = function(s1, s2) {
      return this.replace(new RegExp(s1, "gm"), s2);
    };
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.getData();
  },
  methods: {
    getData() {
      let loading = this.$loading();
      getAppUpdateAPI({
        os_type: 1,
        terminal_type: 2
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          self.appName =
            self.gLang === 1 ? response.data.name_ug : response.data.name_zh;
          self.version = response.data.version;
          self.description = response.data.description.replace(/\n/g, "<br/>");
          self.downloadAddress = response.data.html_url;
        }
      }).finally(() => {
        loading.close();
      });
    }
  },
  components: {
    QrcodeVue
  }
};
</script>
