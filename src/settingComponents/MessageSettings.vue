<template>
  <div class="wraps" :class="gLang == 1 ? 'ug-box' : 'zh-box'">
    <!-- 上方充值筛选部分 -->
    <div class="top">
      <div class="search">
        <el-date-picker
          v-model="picker"
          prefix-icon="el-icon-date"
          type="datetimerange"
          :editable="false"
          range-separator="~"
          value-format="yyyy-MM-dd HH:mm:ss"
          :start-placeholder="gL('startTime')"
          :end-placeholder="gL('endTime')"
          :picker-options="pickerBeginDateBefore"
          :popper-class="gLang == 1 ? 'uy-date' : 'zh-date'"
          :default-time="['00:00:00', '23:59:59']"
          @change="changeDateRange"
        >
        </el-date-picker>
        <!-- <div class="btn-search" @click="search">{{ gL("search") }}</div> -->
      </div>
    </div>
    <div class="top message-recharge">
      <div class="price-count">
        <div class="price">
          <span>{{ gL("sumMessageCount") }} :</span>
          <span class="message-num"> {{ messageInfo.sms_count }} </span>
          <span>{{ gL("short") }}</span>
        </div>
        <div class="count">
          <span>{{ gL("messageUnitPrice") }} :</span>
          <span class="message-num"> {{ messageInfo.sms_price }} </span>
          <span>{{ gL("rmb") }}</span>
        </div>
      </div>
      <div class="recharge-box">
        <el-input
          class="recharge-input"
          v-model.number="rechargeCount"
          :placeholder="gL('rechargeInputPlaceholder')"
          type="number"
        ></el-input>
        <div class="recharge-btn" @click="getPayCode">
          {{ gL("rechargeMessageBtn") }}
        </div>
      </div>
    </div>

    <!-- 表格部分 -->
    <div class="table">
      <el-table
        :data="tableData"
        header-cell-class-name="headers"
        :header-cell-style="{ background: '#e6e6e6' }"
        row-class-name="row"
        :cell-style="cell"
        v-loading="loading"
        style="width: 100%"
        height="100%"
      >
        <el-table-column
          :label="gL('operatorAdmin')"
          prop="admin"
          align="center"
        >
        </el-table-column>
        <el-table-column prop="type_name" align="center" :label="gL('state')">
        </el-table-column>
        <el-table-column prop="send_count" align="center" :label="gL('count')">
        </el-table-column>
        <el-table-column
          prop="left_count"
          align="center"
          :label="gL('residueCount')"
        >
        </el-table-column>
        <el-table-column prop="order_no" align="center" :label="gL('orderNum')">
        </el-table-column>
        <el-table-column
          prop="mobile"
          align="center"
          :label="gL('phoneNumber')"
        >
        </el-table-column>
        <el-table-column
          prop="created_at"
          align="center"
          :label="gL('operatorDate')"
        >
        </el-table-column>
      </el-table>
      <div class="paging">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
          layout="prev, pager, next"
          :total="totalCount"
          :page-size="currentPageSize"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 支付弹窗 -->
    <div class="pay-modal">
      <el-dialog
        :title="gL('messagePayDialogTitle')"
        :visible.sync="showPayModal"
        width="400px"
        center
        @closed="closedPayModalHandler"
      >
        <div class="qrcode-box">
          <div class="qrcode-svg" v-html="qrcodeDom"></div>
          <p class="pay-price" :class="gLang == 1 ? 'ug-price' : 'zh-price'">
            <span>{{ totalPrice }}</span>
            <span>{{ gL("rmb") }}</span>
          </p>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import QRCode from "qrcode";
import { getSmsListAPI, getSmsQueryAPI, getSmsPayAPI } from "./../api/index.js"
let timer = null;
export default {
  data() {
    return {
      gLang: 1,
      picker: [
        moment()
          .add(-1, "month")
          .add(1, "days")
          .format("YYYY-MM-DD HH:mm:ss"),
        moment().format("YYYY-MM-DD HH:mm:ss")
      ],
      pickerBeginDateBefore: {
        disabledDate(time) {
          return time.getTime() >= moment().hour(23).minute(59).second(59).millisecond(999).valueOf();
        }
      },
      rechargeCount: "",
      tableData: [],
      messageInfo: {},
      loading: false,
      totalCount: 0,
      currentPage: 1,
      currentPageSize: 10,
      showPayModal: false,
      payData: {},
      qrcodeDom: "",
    };
  },
  created() {
    this.gLang = localStorage.getItem("langId");
    this.getDataList();
  },
  destroyed() {
    this.closedPayModalHandler();
  },
  computed: {
    totalPrice() {
      return Math.round((this.messageInfo.sms_price * this.rechargeCount) * 100) / 100;
    }
  },
  methods: {
    // 获取表格数据
    getDataList() {
      this.loading = true;
      getSmsListAPI({
        page: this.currentPage,
        limit: this.currentPageSize,
        begin_at: this.picker[0],
        end_at: this.picker[1]
      }).then(response => {
        if (response.status >= 200 && response.status < 300) {
          this.tableData = response.data.data;
          this.messageInfo = response.data.info;
          this.totalCount = response.data.meta.total;
          this.currentPageSize = response.data.meta.per_page;
        }
      })
      .finally(() => {
        this.loading = false;
      });
    },

    // 分页
    handleSizeChange(val) {
      console.log("handleSizeChange", val)
      // this.CurrentChange = val;
      // this.getData();
    },

    // 点击分页按钮
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataList();
    },

    // 表格国际化样式
    cell({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        if (this.gLang == 1) {
          return "direction: rtl";
        }
      }
    },

    // 选择时间
    changeDateRange() {
      console.log("prickker", this.picker);
      if (this.picker != null) {
        this.getDataList();
      } else {
        this.$message({
          message: this.gL("pleaceChooiseTime"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
      }
    },

    // 生成二维码并打开弹窗
    showPayModalHandler(loading) {
      QRCode.toString(
        this.payData.pay_url,
        {
          errorCorrectionLevel: "L",
          width: 250,
          height: 250,
          margin: 1
        },
        (error, qrcode) => {
          if (error != null) {
            console.log("create qrcode error", error);
            return;
          }
          this.qrcodeDom = qrcode;
          this.showPayModal = true;
          loading.close();
          this.queryPay();
        }
      );
    },

    // 每三秒查询支付状态
    queryPay() {
      clearTimeout(timer);
      timer = setTimeout(() => {
        getSmsQueryAPI({
          id: this.payData.id
        }).then(response => {
            if (response.status >= 200 && response.status < 300) {
              console.log("response", response);
              if (response.data.pay_state == 0) {
                this.queryPay();
              } else {
                clearTimeout(timer);
                timer = null;
                this.$message({
                  message: this.gL("messagePaySuccess"),
                  type: "success",
                  customClass: this.$toastClass(),
                  offset: 120
                });
                this.showPayModal = false;
                this.picker = [
                  moment()
                    .add(-1, "month")
                    .add(1, "days")
                    .format("YYYY-MM-DD HH:mm:ss"),
                  moment().format("YYYY-MM-DD HH:mm:ss")
                ];
                this.getDataList();
              }
            }
          })
          .catch(err => {
            if (err.response.status >= 400) {
              clearTimeout(timer);
              timer = null;
            }
          });
      }, 3000);
    },

    // 二维码弹出框关闭后停止定时器
    closedPayModalHandler() {
      this.showPayModal = false;
      clearTimeout(timer);
      timer = null;
    },

    // 点击充值按钮
    getPayCode(e) {
      if (!this.rechargeCount) {
        this.$message({
          message: this.gL("rechargeInputPlaceholder"),
          type: "error",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      const loading = this.$loading();
      getSmsPayAPI({
        sms_count: this.rechargeCount
      }).then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.payData = response.data;
            this.showPayModalHandler(loading);
          }
        })
        .catch(err => {
          loading.close();
        });
    }
  }
};
</script>

<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;

.wraps {
  width: 100%;
  height: 100%;
  z-index: 1;
  .top {
    display: flex;
    padding: 10px 20px;
    background-color: #2e3033;
    color: #ffffff;
    align-items: center;
    border-bottom: 1px solid #666666;
    justify-content: space-between;
    font-size: 22px;
    .search {
      display: flex;
      .btn-search {
        padding: 0 20px;
        background: #139d59;
        display: flex;
        align-items: center;
        margin-left: 15px;
        font-size: 22px;
        cursor: pointer;
        border-radius: 4px;
      }
    }

    &.message-recharge {
      justify-content: space-between;
      height: 60px;
    }

    .price-count {
      display: flex;
      justify-content: flex-start;
      column-gap: 30px;
    }
    .message-num {
      color: #ff9c00;
    }

    .recharge-box {
      display: flex;
      justify-content: flex-start;
      .recharge-input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        background: #4d4d4d;
        height: 40px;
        padding: 0 10px;
        border-radius: 4px;
        margin: 0px 10px;
        &::placeholder {
          color: #cccccc;
        }
      }
      .recharge-btn {
        background-color: #ff9c00;
        font-size: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 40px;
        padding: 0px 15px;
        border-radius: 4px;
        flex-grow: 0;
        flex-shrink: 0;
        cursor: pointer;
      }
    }
  }

  .table {
    height: calc(100% - 200px);
  }

  .pay-modal {
    .qrcode-box {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .pay-price {
        color: #f56c6c;
        font-size: 26px;
        margin-top: 10px;
      }
      .ug-price {
        direction: rtl;
      }
      .zh-price {
        direction: ltr;
      }
    }
  }
}
</style>
