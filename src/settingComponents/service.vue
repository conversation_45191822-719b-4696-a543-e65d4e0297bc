<template>
  <div class="wraps staff">
    <div class="top">
      <div class="serach-btn">
        <div class="serach"></div>
        <div
          class="btn"
          @click="addService()"
          :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
        >
          <span>{{ gL("add") }}</span>
          <span class="iconfont icon-jia-copy-copy"></span>
        </div>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        header-cell-class-name="headers"
        :header-cell-style="{ background: '#e6e6e6' }"
        row-class-name="row"
        v-loading="tableLoading"
        style="width: 100%"
      >
        <el-table-column
          :label="gL('serialNumber')"
          type="index"
          width="70px"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="service_name"
          align="center"
          :label="gL('names')"
        >
          <template slot-scope="scope">
            {{
              gLang === 1
                ? scope.row.service_name_ug
                : scope.row.service_name_zh
            }}
          </template>
        </el-table-column>
        <el-table-column prop="image" align="center" :label="gL('img')">
          <template slot-scope="scope">
            <el-image
              :src="scope.row.image"
              fit="cover"
              lazy
              style="width: 40px; height: 40px; overflow: hidden"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="sort" align="center" :label="gL('sort')">
        </el-table-column>
        <el-table-column align="center" :label="gL('state')">
          <template slot-scope="scope">
            <el-switch
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.state"
              @change="changeSwitch(scope.$index, scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          :label="gL('operation')"
          prop="dosome"
          align="center"
          width="130px"
        >
          <template slot-scope="scope">
            <!-- <el-button  type="success" @click="editData(scope.row)" class="iconfont icon-shiliangzhinengduixiang"  circle></el-button>
            <el-button  type="danger" @click="delData(scope.row)" class="iconfont icon-qingkong"  circle></el-button> -->
            <el-button
              type="text"
              icon="iconfont icon-shiliangzhinengduixiang"
              @click="editData(scope.row)"
              circle
            ></el-button>
            <span style="padding-right: 9px; padding-left: 15px">
              <span class="line"></span>
            </span>
            <el-button
              type="text"
              class="danger"
              @click="delData(scope.row)"
              icon="iconfont icon-qingkong"
              circle
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="mask" v-if="modal_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ modal_titles }}</span>
          <span class="iconfont icon-jia-copy" @click="cancels"></span>
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <div class="content">
            <div class="items" :class="gLang == 1 ? 'items-ug' : ''">
              <div class="row-left">
                <div class="row">
                  <el-form-item prop="service_name_ug">
                    <el-input
                      v-model="ruleForm.service_name_ug"
                      :placeholder="gL('serviceName') + gL('ug')"
                      class="input-ug"
                      :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                      maxlength="20"
                    ></el-input>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="service_name_zh">
                    <el-input
                      v-model="ruleForm.service_name_zh"
                      :placeholder="gL('serviceName') + gL('zh')"
                      :class="gLang == 1 ? 'uy-input' : 'zh-input'"
                      maxlength="20"
                    ></el-input>
                  </el-form-item>
                </div>
                <div class="row type">
                  <div
                    class="check"
                    :class="ruleForm.state == 0 ? 'active' : ''"
                    @click="ruleForm.state = 0"
                  >
                    {{ gL("off") }}
                  </div>
                  <div
                    class="check"
                    :class="ruleForm.state == 1 ? 'active' : ''"
                    @click="ruleForm.state = 1"
                  >
                    {{ gL("open") }}
                  </div>
                </div>
              </div>
              <div class="row-right">
                <div class="row">
                  <el-form-item prop="sort">
                    <el-input
                      placeholder=""
                      v-model="ruleForm.sort"
                      type="number"
                      oninput="if(value.length>3)value=value.slice(0,3)"
                      @keyup.enter.native="confirm"
                    >
                      <template slot="append">{{ gL("sort") }}</template>
                    </el-input>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="image">
                    <el-upload
                      class="avatar-uploader"
                      :action="upload_image_url"
                      name="image"
                      :data="imageData"
                      :headers="imageHeader"
                      :on-success="handleAvatarSuccess"
                      :on-error="handleAvatarError"
                    >
                      <img
                        v-if="ruleForm.image"
                        :src="ruleForm.image"
                        class="avatar"
                      />
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </div>
              </div>
            </div>
            <div class="items" style="width: 0"></div>
          </div>
        </el-form>
        <div class="btn" @click="confirm">
          {{ gL("confirm") }}
        </div>
      </div>
    </div>
    <modal
      :number_box="confirm_box"
      :modal_content="modal_content"
      :modal_title="modal_title"
      @cancel="cancel"
      @confirm="confirmBox"
    ></modal>
  </div>
</template>

<script>
import modal from "../components/modal.vue";
import { getServiceListAPI, postServiceUpdateAPI, postServiceAddAPI, postServiceDeleteAPI } from "./../api/index.js"
var self;
export default {
  created: function () {
    self = this;
    self.gLang = parseInt(localStorage.getItem("langId"));
    self.upload_image_url = "https://" + localStorage.getItem("ip_adr") + "/api/v1/upload/image";
    self.getData();

    this.storeInfo = JSON.parse(localStorage.getItem("merchant_info"));
    this.imageHeader.MerchantNo = this.storeInfo.merchant_no;
  },
  data() {
    return {
      gLang: 1,
      tableData: [],
      activeMneu: 2,
      modal_box: false,
      confirm_box: false,
      modal_titles: this.gL("addService"),
      ruleForm: {
        state: 1,
        service_name_zh: "",
        service_name_ug: "",
        sort: "",
        image: "",
      },
      rules: {
        name_zh: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        name_ug: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        sort: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
        ],
        image: [
          {
            required: true,
            message: this.gL("plaeseChooise"),
            trigger: "blur",
          },
        ],
      },
      addBox: true,
      modal_content: "",
      modal_title: "",
      upload_image_url: "",
      imageData: {
        folder: "foods",
      },
      imageHeader: {
        Authorization: "Bearer " + localStorage.getItem("token"),
        MerchantNo: "",
        "accept-language": localStorage.langId == 1 ? 'ug-CN' : 'zh-CN'
      },
      tableLoading: false,
      storeInfo: {},
    };
  },
  methods: {
    addService() {
      self.modal_box = true;
      self.addBox = true;
      if (self.tableData.length > 0) {
        self.ruleForm.sort =
          Math.max.apply(
            Math,
            self.tableData.map(function (o) {
              return o.sort;
            })
          ) + 1;
      }
    },
    //获取数据
    getData() {
      this.tableLoading = true;
      getServiceListAPI().then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.tableData = response.data.data;
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    handleAvatarSuccess(res, file) {
      if (res.message) {
        self.ruleForm.image = res.message;
        self.img_box = false;
      } else {
        self.$message({
          message: self.gL("uploadError"),
          type: "warning",
          customClass: self.$toastClass(),
          offset: 120
        });
      }
    },
    handleAvatarError(err) {
      const messages = JSON.parse(err.message);
      if (messages && messages.message) {
        this.$message({
          message: messages.message,
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
      }
    },
    //开关
    changeSwitch(index, row) {
      let data = {
        id: row.id,
        service_name_ug: row.service_name_ug,
        service_name_zh: row.service_name_zh,
        state: row.state,
        sort: row.sort,
        image: row.image,
      };
      postServiceUpdateAPI(data).then((response) => {
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
        }
      })
      .catch((err) => {
        self.getData();
      });
    },
    //编辑
    editData(row) {
      self.id = row.id;
      self.addBox = false;
      self.modal_box = true;
      self.ruleForm.service_name_ug = row.service_name_ug;
      self.ruleForm.service_name_zh = row.service_name_zh;
      self.ruleForm.sort = row.sort;
      self.ruleForm.state = row.state;
      self.ruleForm.image = row.image;
      self.modal_titles = self.gL("editService");
    },
    cancels() {
      self.modal_box = false;
      this.ruleForm = {
        state: 1,
        service_name_zh: "",
        service_name_ug: "",
        sort: "",
        image: "",
      }
      self.modal_titles = self.gL("addService");
    },
    confirm() {
      self.ruleForm.password_confirmation = self.ruleForm.password;
      if (self.addBox) {
        self.$refs.ruleForm.validate((valid) => {
          if (valid) {
            postServiceAddAPI(self.ruleForm).then((response) => {
                if (response.status >= 200 && response.status < 300) {
                  self.$message({
                    message: self.gL("successfulOperation"),
                    type: "success",
                    customClass: self.$toastClass(),
                    offset: 120
                  });
                  self.getData();
                  self.cancels();
                }
              });
          } else {
            return false;
          }
        });
      } else {
        self.$refs.ruleForm.validate((valid) => {
          if (valid) {
            self.ruleForm["id"] = self.id;
            postServiceUpdateAPI(self.ruleForm).then((response) => {
                if (response.status >= 200 && response.status < 300) {
                  self.$message({
                    message: self.gL("successfulOperation"),
                    type: "success",
                    customClass: self.$toastClass(),
                    offset: 120
                  });
                  self.getData();
                  self.cancels();
                }
              });
          } else {
            return false;
          }
        });
      }
    },
    //删除
    delData(row) {
      self.id = row.id;
      self.confirm_box = true;
      let serviceName = "";
      if (self.gLang == 1) {
        self.modal_content =
          "《" +
          row.service_name_ug +
          "》" +
          self.gL("confirs") +
          self.gL("confirmdelete");
      } else {
        self.modal_content =
          self.gL("confirmdelete") +
          "《" +
          row.service_name_zh +
          "》" +
          self.gL("confirs");
      }
      self.modal_title = self.gL("tips");
    },
    cancel() {
      self.confirm_box = false;
    },
    confirmBox() {
      self.cancel();
      self.delRowData();
    },
    delRowData() {
      let data = {
        id: self.id,
      };
      postServiceDeleteAPI(data).then((response) => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
            self.getData();
          }
        });
    },
  },
  components: {
    modal,
  },
};
</script>

<style lang="less" scoped>
.wraps {
  width: 100%;
  height: 100%;
  .top {
    display: flex;
    justify-content: space-between;
    padding-right: 20px;
    background-color: #2e3033;
    color: #ffffff;
    height: 7%;
    align-items: center;
    border-bottom: 1px solid #666666;
    flex-direction: row-reverse;
    .btn {
      background-color: #ff9c00;
      width: 150px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25px;
      height: 90%;
      font-size: 26px;
      cursor: pointer;
      .iconfont {
        font-size: 19px;
      }
    }
    .serach-btn {
      display: flex;
      height: 100%;
      align-items: center;
    }
  }
  .table {
    height: 93%;
    overflow-y: scroll;
  }
  //s提示框
  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .box {
      background-color: #ffffff;
      width: 820px;
      font-size: 30px;
      .title {
        background-color: #e6e6e6;
        color: #1a1a1a;
        padding: 25px 20px;
        position: relative;
        text-align: center;
        .iconfont {
          position: absolute;
          right: 20px;
          font-size: 23px;
          color: #666666;
          cursor: pointer;
        }
      }
      .content {
        padding: 50px 75px 0 75px;
        display: flex;
        justify-content: space-between;
        .items {
          width: 100%;
          display: flex;
          .row-left{
            flex: 1;
            padding-right: 32px;
          }
          .row-right{
            flex: 1;
          }
          .row {
            margin-bottom: 20px;
          }
          .type {
            display: flex;
            justify-content: space-between;
            .check {
              width: 48%;
              border: 1px solid #cccccc;
              color: #666666;
              font-size: 18px;
              text-align: center;
              cursor: pointer;
              padding: 10px;
            }
            .active {
              background-color: #139d59;
              color: #fff;
            }
          }

        }
      }
      .btn {
        margin: 20px 75px 20px 75px;
        background: #139d59;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        padding: 15px 0;
        cursor: pointer;
      }
    }
  }
  .avatar-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 132px;
    height: 118px;
  }
  .avatar-uploader .el-upload {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 132px;
    line-height: 118px;
    text-align: center;
  }
  .avatar {
    width: 132px;
    height: 118px;
    display: block;
  }
}
</style>
