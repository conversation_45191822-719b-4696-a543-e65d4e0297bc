<template>
  <div class="wraps staff">
    <div class="top">
      <div class="serach-btn">
        <div class="serach" v-if="activeMneu == 2">
          <div class="input">
            <span class="iconfont icon-search search"></span>
            <input
              type="text"
              oninput="if(value.length>25)value=value.slice(0,25)"
              :placeholder="gL('phone')"
              v-model="serachText"
              v-on:input="inputSearch"
            />
          </div>
          <div class="del" v-if="delete_text" @click="removeSerach">
            <span class="iconfont icon-jia-copy"></span>
          </div>
        </div>
        <div
          class="btn"
          @click="addBoxs"
          :style="{ flexDirection: gLang == 1 ? 'row' : 'row-reverse' }"
        >
          <span>{{ gL("add") }}</span>
          <span class="iconfont icon-jia-copy-copy"></span>
        </div>
      </div>
      <div class="menu">
        <div
          class="menu-item"
          :class="activeMneu == 2 ? 'active' : ''"
          @click="clickMenu(2)"
        >
          {{ gL("staffTab") }}
        </div>
        <div
          class="menu-item"
          :class="activeMneu == 3 ? 'active' : ''"
          @click="clickMenu(3)"
        >
          {{ gL("role") }}
        </div>
      </div>
    </div>

    <!-- 员工列表 -->
    <div class="table" v-show="activeMneu == 2">
      <el-table
        :data="tableData"
        header-cell-class-name="headers"
        :header-cell-style="{ background: '#e6e6e6' }"
        row-class-name="row"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column
          :label="gL('serialNumber')"
          type="index"
          align="center"
          width="60"
        >
        </el-table-column>
        <el-table-column
          prop="no"
          align="center"
          width="100px"
          :label="gL('jobNumber')"
        >
        </el-table-column>
        <el-table-column
          :prop="gLang == 1 ? 'name_ug' : 'name_zh'"
          align="center"
          :label="gL('name')"
        >
        </el-table-column>
        <el-table-column prop="phone" align="center" :label="gL('phoneNumber')">
        </el-table-column>
        <el-table-column align="center" :label="gL('role')">
          <template slot-scope="scope">
            <template v-if="scope.row.is_owner">
              <span>{{ gL("owner") }}</span>
            </template>
            <template v-if="!scope.row.is_owner">
              <span v-for="(item, index) in scope.row.roles">{{
                gLang == 1
                  ? index == 0
                    ? item.name_ug
                    : `، ${item.name_ug}`
                  : index == 0
                  ? item.name_cn
                  : `, ${item.name_cn}`
              }}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column align="center" width="70" :label="gL('state')">
          <template slot-scope="scope">
            <el-switch
              v-if="!scope.row.is_owner"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.state"
              @change="changeSwitch(scope.$index, scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          :label="gL('operation')"
          prop="dosome"
          align="center"
          width="100px"
        >
          <template slot-scope="scope">
            <el-button
              v-if="!scope.row.is_owner"
              type="text"
              icon="iconfont icon-shiliangzhinengduixiang"
              @click="editData(scope.row)"
              circle
            ></el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-box">
        <el-pagination
          background
          layout="total, prev, pager, next"
          :total="pageMeta.total"
          @size-change="sizeChange"
          @current-change="currentChange"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="table" v-show="activeMneu == 3">
      <el-table
        :data="roleData"
        header-cell-class-name="headers"
        :header-cell-style="{ background: '#e6e6e6' }"
        row-class-name="row"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column
          :label="gL('serialNumber')"
          type="index"
          align="center"
          width="60"
        >
        </el-table-column>
        <el-table-column
          :prop="gLang == 1 ? 'name_ug' : 'name_zh'"
          align="center"
          :label="gL('name')"
        >
        </el-table-column>
        <el-table-column
          prop="updated_at"
          align="center"
          :label="gL('lastLoginTime')"
        >
        </el-table-column>
        <el-table-column align="center" :label="gL('detail')">
          <template slot-scope="scope">
            <el-button
              type="text"
              class="text-btn-detail"
              @click="showDetailHandler(scope.row)"
              >{{ gL("detail") }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column align="center" width="70" :label="gL('state')">
          <template slot-scope="scope">
            <el-switch
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.state"
              @change="changeRoleSwitch(scope.$index, scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          :label="gL('operation')"
          prop="dosome"
          align="center"
          width="100px"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="iconfont icon-shiliangzhinengduixiang"
              @click="editData(scope.row)"
              circle
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="mask" v-if="modal_box">
      <div class="box">
        <div class="title">
          <span></span>
          <span>{{ modal_title }}</span>
          <span class="iconfont icon-jia-copy" @click="cancel(2)"></span>
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <div class="content">
            <div class="items">
              <div class="row">
                <el-form-item prop="no">
                  <el-input
                    v-model="ruleForm.no"
                    ref="input"
                    :placeholder="gL('accountName')"
                    type="number"
                    oninput="if(value.length>5)value=value.slice(0,5)"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="row">
                <el-form-item prop="name_ug">
                  <el-input
                    v-model="ruleForm.name_ug"
                    :placeholder="gL('uyNamePlaceholder')"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="row">
                <el-form-item prop="name_zh">
                  <el-input
                    v-model="ruleForm.name_zh"
                    :placeholder="gL('zhNamePlaceholder')"
                  ></el-input>
                </el-form-item>
              </div>
            </div>
            <div class="items">
              <div class="row">
                <el-form-item prop="phone">
                  <el-input
                    v-model="ruleForm.phone"
                    type="number"
                    oninput="if(value.length>11)value=value.slice(0,11)"
                    @keyup.enter.native="confirm(2)"
                    :placeholder="gL('phoneNumber')"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="row">
                <el-form-item prop="role_ids">
                  <el-select
                    class="rule-select"
                    v-model="ruleForm.role_ids"
                    multiple
                    :placeholder="gL('selectRule')"
                  >
                    <el-option
                      v-for="item in type_list"
                      :key="item.id"
                      :label="gLang == 1 ? item.name_ug : item.name_zh"
                      :value="item.id"
                      :disabled="item.state == 0"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="row type">
                <div
                  class="check"
                  :class="ruleForm.state == 0 ? 'active' : ''"
                  @click="ruleForm.state = 0"
                >
                  {{ gL("off") }}
                </div>
                <div
                  class="check"
                  :class="ruleForm.state == 1 ? 'active' : ''"
                  @click="ruleForm.state = 1"
                >
                  {{ gL("open") }}
                </div>
              </div>
            </div>
          </div>
        </el-form>
        <div class="adds">
          <!-- <div class="btn add-btn" @click="confirm(1)"  v-if="addBox">
                {{gL('continueAdd')}}
            </div> -->
          <div class="btn" @click="confirm(2)" :class="!addBox ? 'edit' : ''">
            {{ gL("confirm") }}
          </div>
        </div>
      </div>
    </div>

    <RoleDialog
      ref="roleDialog"
      :show="showRoleDialog"
      :title="modal_title"
      :data="permissionData"
      @confirm="confirmRole"
      @cancel="cancelRole"
    />
  </div>
</template>

<script>
import {
  getStaffUsersAPI,
  getStaffUserRolesAPI,
  putStaffUserStateAPI,
  postStaffUserAddAPI,
  putStaffUserUpdateAPI,
  getPermissionAPI,
  postRolesAPI,
  putRolesAPI,
  getRolesDetailAPI,
  putRolesStatusAPI
} from "./../api/index.js";
import RoleDialog from "./../components/dialog/RoleDialog.vue";
var self;
export default {
  components: {
    RoleDialog
  },
  created: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.activeMneu = 2;
    self.getData();
  },
  data() {
    return {
      gLang: 1,
      tableData: [],
      serachText: "", //搜索内容
      delete_text: "", //搜索框删除按钮
      activeMneu: 2,
      modal_box: false,
      type_list: [],
      modal_title: this.gL("cashier"),
      roleData: [],
      showRoleDialog: false,
      permissionData: [],
      ruleForm: {
        state: 1,
        no: "",
        name_ug: "",
        name_zh: "",
        phone: "",
        role_ids: []
      },
      rules: {
        phone: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
          { min: 11, max: 11, message: this.gL("mobile"), trigger: "blur" }
        ],
        no: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" },
          { min: 3, max: 5, message: this.gL("accountLength"), trigger: "blur" }
        ],
        name_ug: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" }
        ],
        name_zh: [
          { required: true, message: this.gL("plaeseInput"), trigger: "blur" }
        ],
        role_ids: [
          { required: true, message: this.gL("plaeseChooise"), trigger: "blur" }
        ]
      },
      addBox: true,
      loading: false,
      double_click: true, //双击添加
      page: 1,
      limit: 10,
      pageMeta: {}
    };
  },
  methods: {
    sizeChange(value) {
      this.limit = value;
      this.page = 1;
      this.getData();
    },
    currentChange(value) {
      this.page = value;
      this.getData();
    },
    //搜索输入框输入时候
    inputSearch() {
      if (self.serachText.length != 0) {
        self.delete_text = true;
      } else {
        self.delete_text = false;
      }
      self.getData();
    },
    //删除搜索内容
    removeSerach() {
      self.serachText = "";
      self.delete_text = false;
      self.getData();
    },

    //点击菜单
    clickMenu(e) {
      self.activeMneu = e;
      if (e == 2) {
        this.getData();
      } else if (e == 3) {
        this.getRoleData();
      }
    },

    // 获取角色列表
    getRoleData() {
      this.loading = true;
      getStaffUserRolesAPI()
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.roleData = response.data.data;
          }
        })
        .finally(() => {
          self.loading = false;
        });
    },

    //获取数据
    getData() {
      this.loading = true;
      getStaffUsersAPI({
        page: this.page,
        limit: this.limit,
        mobile: this.serachText
      })
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.tableData = response.data.data;
            this.pageMeta = response.data.meta;
          }
        })
        .finally(() => {
          self.loading = false;
        });
    },
    //开关
    changeSwitch(index, row) {
      putStaffUserStateAPI(row.id, { state: row.state })
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
          }
        })
        .finally(() => {
          this.getData();
        });
    },
    // 角色开关
    changeRoleSwitch(index, row) {
      putRolesStatusAPI(row.id, { state: row.state })
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            self.$message({
              message: self.gL("successfulOperation"),
              type: "success",
              customClass: self.$toastClass(),
              offset: 120
            });
          }
        })
        .finally(() => {
          this.getRoleData();
        });
    },
    // 获取角色列表
    getStaffRoleList(fn) {
      const loading = this.$loading();
      getStaffUserRolesAPI()
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.type_list = response.data.data;
            fn();
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    // 获取权限列表
    getPermissionList(fn) {
      const loading = this.$loading();
      getPermissionAPI()
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            this.permissionData = response.data.data;
            fn();
          }
        })
        .finally(() => {
          loading.close();
        });
    },

    // 获取角色详情
    getRoleDetail(id, fn) {
      const loading = this.$loading();
      getRolesDetailAPI(id)
        .then(response => {
          if (response.status >= 200 && response.status < 300) {
            fn(response.data.data);
          }
        })
        .finally(() => {
          loading.close();
        });
    },

    addBoxs() {
      if (this.activeMneu == 2) {
        this.modal_title = this.gL("addStaffTitle");
        this.getStaffRoleList(() => {
          this.modal_box = true;
          this.addBox = true;
        });
      } else {
        this.modal_title = this.gL("addRoleTitle");
        this.getPermissionList(() => {
          this.showRoleDialog = true;
          this.addBox = true;
        });
      }
    },
    //编辑
    editData(row) {
      if (this.activeMneu == 2) {
        this.modal_title = this.gL("eidtStaffTitle");
        this.getStaffRoleList(() => {
          this.id = row.id;
          this.addBox = false;
          this.modal_box = true;
          this.ruleForm.name_ug = row.name_ug;
          this.ruleForm.name_zh = row.name_zh;
          this.ruleForm.no = row.no;
          this.ruleForm.phone = row.phone;
          this.ruleForm.state = row.state;
          this.ruleForm.role_ids = row.roles.map(item => item.id);
        });
      } else {
        this.modal_title = this.gL("editRoleTitle");
        this.getRoleDetail(row.id, detail => {
          this.getPermissionList(() => {
            this.showRoleDialog = true;
            this.addBox = false;
            this.id = row.id;
            this.$nextTick(() => {
              this.$refs.roleDialog.editForm(detail);
            });
          });
        });
      }
    },

    // 打开详情弹出框
    showDetailHandler(row) {
      this.modal_title = this.gL("detailRoleTitle");
      this.getRoleDetail(row.id, detail => {
        this.getPermissionList(() => {
          this.showRoleDialog = true;
          this.addBox = false;
          this.id = row.id;
          this.$nextTick(() => {
            this.$refs.roleDialog.editForm(detail, "detail");
          });
        });
      });
    },

    cancel(e) {
      if (e == 2) {
        self.modal_box = false;
      } else {
        self.$refs["input"].focus();
      }
      this.ruleForm.name_ug = "";
      this.ruleForm.name_zh = "";
      this.ruleForm.no = "";
      this.ruleForm.phone = "";
      this.ruleForm.state = 1;
      this.ruleForm.role_ids = [];
    },
    confirm(e) {
      if (self.addBox) {
        self.$refs.ruleForm.validate(valid => {
          if (valid) {
            if (self.double_click) {
              self.double_click = false;
              postStaffUserAddAPI(self.ruleForm)
                .then(response => {
                  if (response.status >= 200 && response.status < 300) {
                    self.$message({
                      message: self.gL("successfulOperation"),
                      type: "success",
                      customClass: self.$toastClass(),
                      offset: 120
                    });
                    self.getData();
                    self.cancel(e);
                  }
                })
                .finally(() => {
                  self.double_click = true;
                });
            }
          } else {
            return false;
          }
        });
      } else {
        self.$refs.ruleForm.validate(valid => {
          if (valid) {
            putStaffUserUpdateAPI(self.id, self.ruleForm).then(response => {
              if (response.status >= 200 && response.status < 300) {
                self.$message({
                  message: self.gL("successfulOperation"),
                  type: "success",
                  customClass: self.$toastClass(),
                  offset: 120
                });
                self.getData();
                self.cancel(e);
              }
            });
          } else {
            return false;
          }
        });
      }
    },

    // 角色弹出框确认
    confirmRole(data) {
      if (this.addBox) {
        if (self.double_click) {
          self.double_click = false;
          postRolesAPI(data)
            .then(response => {
              if (response.status >= 200 && response.status < 300) {
                self.$message({
                  message: self.gL("successfulOperation"),
                  type: "success",
                  customClass: self.$toastClass(),
                  offset: 120
                });
                this.showRoleDialog = false;
                this.getRoleData();
              }
            })
            .finally(() => {
              self.double_click = true;
            });
        }
      } else {
        if (self.double_click) {
          self.double_click = false;
          putRolesAPI(this.id, data)
            .then(response => {
              if (response.status >= 200 && response.status < 300) {
                self.$message({
                  message: self.gL("successfulOperation"),
                  type: "success",
                  customClass: self.$toastClass(),
                  offset: 120
                });
                this.showRoleDialog = false;
                this.getRoleData();
              }
            })
            .finally(() => {
              self.double_click = true;
            });
        }
      }
    },

    // 关闭弹出框
    cancelRole() {
      this.showRoleDialog = false;
    }
  }
};
</script>

<style lang="less" scoped>
.wraps {
  width: 100%;
  height: 100%;
  .top {
    display: flex;
    justify-content: space-between;
    padding-right: 20px;
    background-color: #2e3033;
    color: #ffffff;
    height: 7%;
    align-items: center;
    border-bottom: 1px solid #666666;
    flex-direction: row-reverse;
    .serach {
      width: 280px;
      background: #4d4d4d;
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 90%;
      .input {
        //  width: 70%;
        display: flex;
        align-items: center;
      }
      input {
        outline: none;
        font-size: 22px;
        color: #ffffff;
        width: 70%;
        background: #4d4d4d;
        &::placeholder {
          color: #cccccc;
        }
      }
      .search {
        color: #cccccc;
        font-size: 24px;
        padding: 0 10px;
      }
      .del {
        width: 20%;
        height: 100%;
        color: #cccccc;
        justify-content: center;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
          font-size: 22px;
        }
      }
    }
    .btn {
      background-color: #ff9c00;
      width: 150px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25px;
      height: 90%;
      font-size: 26px;
      cursor: pointer;
      .iconfont {
        font-size: 19px;
      }
    }
    .serach-btn {
      display: flex;
      height: 100%;
      align-items: center;
    }
    .menu {
      display: flex;
      font-size: 22px;
      height: 100%;
      .menu-item {
        display: flex;
        height: 100%;
        align-items: center;
        padding: 0 30px;
        cursor: pointer;
        border-bottom: 5px solid #2e3033;
      }
      .active {
        border-color: #139d59;
        color: #139d59;
      }
    }
  }
  .table {
    height: 93%;
    overflow-y: scroll;
  }
  //s提示框
  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1366px;
    min-height: 768px;
    .box {
      background-color: #ffffff;
      width: 920px;
      font-size: 30px;
      .title {
        background-color: #e6e6e6;
        color: #1a1a1a;
        padding: 25px 20px;
        position: relative;
        text-align: center;
        .iconfont {
          position: absolute;
          right: 20px;
          font-size: 23px;
          color: #666666;
          cursor: pointer;
        }
      }
      .content {
        padding: 50px 75px 5px 75px;
        display: flex;
        justify-content: space-between;
        .items {
          width: 48%;
          .row {
            margin-bottom: 20px;
          }
          .type {
            display: flex;
            justify-content: space-between;
            .check {
              width: 48%;
              border: 1px solid #cccccc;
              color: #666666;
              font-size: 18px;
              text-align: center;
              cursor: pointer;
              height: 40px;
              line-height: 40px;
              border-radius: 3px;
            }
            .active {
              background-color: #139d59;
              color: #fff;
            }
          }
        }
      }
      .adds {
        display: flex;
        margin: 20px 75px 20px 75px;
        justify-content: space-between;
        .add-btn {
          background-color: #ff9c00;
        }
        .btn {
          width: 100%;
          margin: 0;
        }
        .edit {
          width: 100%;
        }
      }
      .btn {
        background: #139d59;
        color: #ffffff;
        font-size: 26px;
        text-align: center;
        padding: 15px 0;
        cursor: pointer;
        margin: 20px 75px 20px 75px;
      }
    }
  }

  .pagination-box {
    margin-block: 20px;
    text-align: right;
  }

  .text-btn-detail {
    font-size: 20px;
  }
}
</style>
