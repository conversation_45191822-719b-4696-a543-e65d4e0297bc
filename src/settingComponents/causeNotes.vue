<template>
    <div class="wraps staff">
        <div class="top">
            <div class="serach-btn">
              <div class="serach">

              </div>
              <div class="btn" @click="addCause()" :style="{flexDirection:gLang==1?'row':'row-reverse'}">
                  <span>{{gL('add')}}</span>
                  <span class="iconfont icon-jia-copy-copy"></span>
              </div>
            </div>
            <div class="menu">
              <div class="menu-item" v-for="item in menu_list" :key="item.id" :class="activeMneu==item.id?'active':''" @click="clickMenu(item)">{{item.name}}</div>
            </div>
        </div>
        <div class="table">
        <el-table
        :data="tableData"
        header-cell-class-name="headers"
        :header-cell-style="{background:'#e6e6e6'}"
        row-class-name="row"
        v-loading="tableLoading"
        style="width: 100%">
        <el-table-column
          :label="gL('serialNumber')"
          type="index"
          align="center"
          width="60">
        </el-table-column>
        <el-table-column
          align="center"
          :label="gL('type')">
          <template slot-scope="scope">
            <div class="menu-item" v-for="item in menu_list" :key="item.id" v-if="scope.row.tag==item.tag">{{item.name}}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          align="center"
          :label="gL('remarkContent')">
        </el-table-column>
        <el-table-column
          prop="sort"
          align="center"
          :label="gL('sort')">
        </el-table-column>
        <el-table-column
          align="center"
          width="70"
          :label="gL('state')">
          <template slot-scope="scope">
            <el-switch
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.state"
              @change="changeSwitch(scope.$index, scope.row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column :label="gL('operation')" prop="dosome" align="center" width="130px">
          <template slot-scope="scope">
            <!-- <el-button  type="success" @click="editData(scope.row)" class="iconfont icon-shiliangzhinengduixiang"  circle></el-button>
            <el-button  type="danger" @click="delData(scope.row)" class="iconfont icon-qingkong"  circle></el-button> -->
            <el-button type="text"  icon="iconfont icon-shiliangzhinengduixiang" @click="editData(scope.row)" circle></el-button>
             <span style="padding-right:9px;padding-left:15px">
              <span class='line'></span>
            </span>
            <el-button  type="text" class="danger" @click="delData(scope.row)" icon="iconfont icon-qingkong"  circle></el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>
      <div class="mask" v-if="modal_box">
        <div class="box">
          <div class="title">
            <span></span>
            <span v-if="gLang==1">{{modal_title+' '+modal_titles}}</span>
            <span v-if="gLang==2">{{modal_titles+modal_title}}</span>
            <span class="iconfont icon-jia-copy" @click="cancels(2)"></span>
          </div>
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
            <div class="content">
              <div class="items">
                <div class="row">
                  <el-form-item prop="name_ug">
                    <el-input v-model="ruleForm.name_ug" ref="input" :placeholder="gL('remark')+gL('ug')" class="input-ug"  :class="gLang==1?'uy-input':'zh-input'" maxlength="30"></el-input>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="name_zh">
                    <el-input v-model="ruleForm.name_zh"  :placeholder="gL('remark')+gL('zh')" :class="gLang==1?'uy-input':'zh-input'" maxlength="30"></el-input>
                  </el-form-item>
                </div>
              </div>
              <div class="items">
                <div class="row">
                  <el-form-item prop="sort">
                    <el-input placeholder="" v-model="ruleForm.sort" type="number" oninput="if(value.length>3)value=value.slice(0,3)" @keyup.enter.native="confirm(2)">
                      <template slot="append">{{gL('sort')}}</template>
                    </el-input>
                  </el-form-item>
                </div>
                <div class="row type">
                  <div class="check" :class="ruleForm.state==0?'active':''" @click="ruleForm.state=0">{{gL('off')}}</div>
                  <div class="check" :class="ruleForm.state==1?'active':''" @click="ruleForm.state=1">{{gL('open')}}</div>
                </div>
              </div>
            </div>
          </el-form>
          <div class="adds">
            <div class="btn add-btn" @click="confirm(1)"  v-if="addBox">
                {{gL('continueAdd')}}
            </div>
            <div class="btn" @click="confirm(2)" :class="!addBox?'edit':''">
                {{gL('confirm')}}
            </div>
          </div>
        </div>
      </div>
      <modal :number_box="confirm_box"
              :modal_content="modal_content"
              :modal_title="modals_title"
              @cancel="cancel" @confirm="confirmBox"></modal>
    </div>
</template>

<script>
import modal from '../components/modal.vue'
import { getRemarksListAPI, getRamarkCategoryListAPI, getRemarksStateAPI, postRemarksAddAPI, putRemarksUpdateAPI, deleteRemarksDeleteAPI } from "./../api/index.js"
var self;
export default {
  created: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.getMenuList();
  },
  data() {
    return {
      gLang:1,
      tableData:[],
      activeMneu:1,
      tag:'',
      modal_box:false,
      confirm_box:false,
      modal_content:'',
      type_list:[],
      menu_list:[],
      modal_title:'',
      modals_title:this.gL('add'),
      modal_titles:this.gL('add'),
      ruleForm:{
        state:1,
        sort:'',
        name_ug:'',
        name_zh:'',
        tag:''
      },
      rules: {
          sort: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' },
          ],
          name_ug: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          name_zh: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
        },
      addBox:true,
      tableLoading: false
    };
  },
  methods: {
    addCause(){
      self.modal_box=true;
      self.addBox=true;
      if(self.tableData.length>0){
        self.ruleForm.sort = Math.max.apply(Math, self.tableData.map(function(o) {return o.sort}))+1;
      }
    },
    //点击点菜
    clickMenu(e){
      self.activeMneu=e.id;
      self.ruleForm.tag = e.tag
      self.getData();
      self.modal_title = e.name;
    },
    //获取数据
    getData(){
      this.tableLoading = true;
      getRemarksListAPI({
        tag:self.ruleForm.tag
      }).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.tableData = response.data.data;
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    //获取角色列表
    getMenuList(){
      this.tableLoading = true;
      getRamarkCategoryListAPI().then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.menu_list = response.data.data;
          self.activeMneu = response.data.data[0].id;
          self.ruleForm.tag = response.data.data[0].tag;
          self.modal_title = response.data.data[0].name;
          self.getData();
        }
      });
    },
     //开关
    changeSwitch(index,row){
      getRemarksStateAPI(row.id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.$message({
            message:self.gL('successfulOperation'),
            type: "success",
            customClass:self.$toastClass(),
            offset: 120
          });
        }
      }).catch(err => {
        this.getMenuList();
      });
    },
    //编辑
    editData(row){
      self.id = row.id;
      self.addBox = false;
      self.modal_box = true;
      self.ruleForm.sort = row.sort;
      self.ruleForm.name_zh = row.name_zh;
      self.ruleForm.name_ug = row.name_ug;
      self.ruleForm.state = row.state;
      self.modal_titles = self.gL('edit');
    },
    cancels(e){
      if(e==2){
        self.modal_box = false;
      }else{
        self.$refs['input'].focus();
        self.ruleForm.sort = self.ruleForm.sort+1;
      }
      self.ruleForm.state = 1;
      self.ruleForm.name_zh = '';
      self.ruleForm.name_ug = '';
    },
    confirm(e){
        if(self.addBox){
          self.$refs.ruleForm.validate((valid) => {
            if (valid) {
              postRemarksAddAPI(self.ruleForm).then((response) => {
                if(response.status >= 200 && response.status < 300){
                  self.$message({
                    message:self.gL('successfulOperation'),
                    type: "success",
                    customClass:self.$toastClass(),
                    offset: 120
                  });
                  self.getData();
                  self.cancels(e);
                }
              });
          } else {
            return false;
          }
        });
       }else{
        self.$refs.ruleForm.validate((valid) => {
        if (valid) {
          putRemarksUpdateAPI(self.id, self.ruleForm).then((response) => {
            if(response.status >= 200 && response.status < 300){
              self.$message({
                message:self.gL('successfulOperation'),
                type: "success",
                customClass:self.$toastClass(),
                offset: 120
              });
              self.getData();
              self.cancels(e);
            }
          });
        }else {
            return false;
          }
        });
      }
    },
    //删除
    delData(row){
      self.id = row.id;
      self.confirm_box = true;
      if(self.gLang==1){
        self.modal_content="《"+row.name+"》"+self.gL('confirs')+self.gL('confirmdelete');
      }else{
        self.modal_content=self.gL('confirmdelete')+"《"+row.name+"》"+self.gL('confirs');
      }
      self.modals_title=self.gL('tips');
    },
    cancel(){
      self.confirm_box = false;
    },
    confirmBox(){
      self.cancel();
      self.delRowData();
    },
    delRowData(){
      deleteRemarksDeleteAPI(self.id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.$message({
            message:self.gL('successfulOperation'),
            type: "success",
            customClass:self.$toastClass(),
            offset: 120
          });
          self.getData();
        }
      });
    },
  },
  components:{
    modal,
  }
};
</script>

<style lang="less" scoped>
.wraps{
    width: 100%;
    height: 100%;
     .top{
        display: flex;
        justify-content: space-between;
        padding-right:20px;
        background-color: #2e3033;
        color:#ffffff;
        height: 7%;
        align-items: center;
        border-bottom: 1px solid #666666;
        flex-direction: row-reverse;
     .btn{
        background-color: #ff9c00;
        width: 150px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 25px;
        height: 90%;
        font-size: 26px;
        cursor: pointer;
        .iconfont{
            font-size: 19px;
        }
      }
      .serach-btn{
        display: flex;
        height: 100%;
        align-items: center;
        padding-left: 20px;
      }
      .menu{
        display: flex;
        font-size: 22px;
        height: 100%;
        .menu-item{
          display: flex;
          height: 100%;
          align-items: center;
          padding: 0 30px;
          cursor: pointer;
          border-bottom: 5px solid #2e3033;
        }
        .active{
          border-color:#139d59;
          color:#139d59;
        }
      }
    }
    .table{
      height: 93%;
      overflow-y: scroll;
    }
      //s提示框
   .mask{
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      background-color: rgba(0,0,0,.5);
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 1366px;
      min-height: 768px;
      .box{
        background-color: #ffffff;
        width: 920px;
        font-size: 30px;
        .title{
          background-color: #e6e6e6;
          color:#1a1a1a;
          padding: 25px 20px;
          position: relative;
          text-align: center;
          .iconfont{
            position: absolute;
            right: 20px;
            font-size: 23px;
            color:#666666;
            cursor: pointer;
          }
        }
        .content{
          padding: 50px 75px 5px 75px;
          display: flex;
          justify-content: space-between;
          .items{
            width: 48%;
            .row{
              margin-bottom: 20px;
            }
            .type{
              display: flex;
              justify-content: space-between;
              .check{
                width: 48%;
                border:1px solid #cccccc;
                color:#666666;
                font-size: 18px;
                text-align: center;
                cursor: pointer;
                height: 40px;
                line-height: 40px;
                border-radius: 3px;
              }
              .active{
                background-color: #139d59;
                color:#fff;
              }
            }
          }
        }
        .adds{
          display: flex;
          margin: 20px 75px 20px 75px;
          justify-content: space-between;
          .add-btn{
            background-color: #ff9c00;
          }
          .btn{
            width: 48%;
            margin: 0;
          }
          .edit{
            width: 100%;
          }
        }
        .btn{
          background: #139d59;
          color:#ffffff;
          font-size: 26px;
          text-align: center;
          padding: 15px 0;
          cursor: pointer;
          margin: 20px 75px 20px 75px;
        }
      }
   }
  }
</style>
