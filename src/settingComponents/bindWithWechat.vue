<template>
  <div class="wrap">
    <el-row>
      <el-col :span="8">
        <div class="card blue" @click="openPwdBox(1)">
          <div class="icon">
            <i class="iconfont icon-people_fill"></i>
          </div>
          <div class="title">{{ gL("bind_manager") }}</div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="card green" @click="openPwdBox(3)">
          <div class="icon">
            <i class="iconfont icon-zj-"></i>
          </div>
          <div class="title">{{ gL("bind_order_manager") }}</div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="card yellow" @click="openPwdBox(2)">
          <div class="icon">
            <i class="iconfont icon-order_fill"></i>
          </div>
          <div class="title">
            <div class="row">{{ gL("bind_financial_manager") }}</div>
            <div class="row" v-if="bindInfo.length > 0">{{ gL("wechat") }}:{{bindInfo[0].mobile}}</div>
          </div>
        </div>
      </el-col>
    </el-row>
    <password-box :shown="passwordBoxShown" :title="t_login_password" @cancel="closePwdBox" @confirm="toBind" />
    <qr-preview :image="sunCode" :shown="qrPreviewBoxShown" @cancel="closeQrPreviewBox"/>
  </div>
</template>
<style scoped lang="less">
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.wrap {
  .card {
    min-height: 600px;
    background: #e5e5e5;
    margin: 40px;
    padding: 40px;
    text-align: center;
    border-radius: 8px;
    box-sizing: border-box;
    &:first-child {
      margin-left: 20px;
    }
    &.blue {
      background: #439af7;
    }
    &.green {
      background: #50be5d;
    }
    &.yellow {
      background: #e0b10b;
    }
    .icon {
      margin-top: 20px;
      color: #ffffff66;
      i {
        font-size: 140px;
      }
    }
    .title {
      font-size: 26px;
      margin-top: 40px;
      color: white;
      .row {
        font-size: 26px;
        margin-bottom: 20px;
      }
    }
  }
}
</style>
<script>
let self;
import PasswordBox from "../components/passwordBox.vue";
import QrPreview from '../components/qrPreview.vue';
import { getSmartListAPI, getSmartBindStatusAPI } from "./../api/index.js"
export default {
  created() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.getData()
  },
  data() {
    return {
      gLang: 1,
      t_login_password: this.gL('login_password'),
      passwordBoxShown: false,
      qrPreviewBoxShown: false,
      bindInfo:[],
      pwd: '',
      staffType : 0,
      sunCode: ''
    };
  },
  methods: {
    openPwdBox(staffType) {
      self.passwordBoxShown = true
      self.staffType = staffType
    },
    closePwdBox() {
      self.passwordBoxShown = false
    },
    openQrPreviewBox(){
      self.qrPreviewBoxShown = true
    },
    closeQrPreviewBox(){
      self.qrPreviewBoxShown = false
    },
    toBind(pwd){
      if(pwd.length > 5){
        let data = {
          stuff_type: self.staffType,
          password: pwd
        }
        let loading = this.$loading();
        getSmartListAPI(data).then((response) => {
          if(response.status >= 200 && response.status < 300){
            self.sunCode = response.data.message
            self.closePwdBox()
            self.openQrPreviewBox()
          }
        }).finally(() => {
          loading.close();
        });
      }
    },
    getData(){
      let loading = this.$loading();
      getSmartBindStatusAPI().then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.bindInfo = response.data.data
        }
      }).finally(() => {
        loading.close();
      });
    }
  },
  components: {
      PasswordBox,
      QrPreview
  },
};
</script>
