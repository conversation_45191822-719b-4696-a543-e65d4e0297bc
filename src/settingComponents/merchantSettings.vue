<template>
  <div class="wrap">
    <div class="setting-box" :style="gLang == 1 ? 'direction:rtl' : ''">
      <div class="header">
        {{ gL("smallChangeSetting") }}
      </div>
      <div class="body">
        <div class="body-item">
          <div class="item-right">
            {{ gL("mostPriceSmallChange") }}
          </div>
          <div class="item-left">
            <input type="number" v-model="max_ignore_price" :placeholder="gL('requireMaxIgnorePrice')" />
          </div>
        </div>
        <div class="body-item">
          <div class="item-right">
            {{ gL("persentOfSmallChange") }}
          </div>
          <div class="item-left">
            <input type="number" v-model="max_percent_of_order" :placeholder="gL('requireMaxPercentOrder')" />
          </div>
        </div>
        <div class="body-item">
          <div class="item-right">
            {{ gL("minimumSamallChange") }}
          </div>
          <div class="item-left">
            <input type="number" v-model="min_order_price" :placeholder="gL('requireMinOrderPrice')" />
          </div>
        </div>
        <div class="body-item">
          <div class="item-right">
            {{ gL("smallChangePassword") }}
          </div>
          <div class="item-left">
            <input type="password" v-model="pass_word" :placeholder="gL('requireSmallPassword')" />
          </div>
        </div>

        <div class="btn" @click="saveSettings">
          {{ gL("save") }}
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
@bgColor: #2e3033;
@grayColor: #666666;
@graphiteColor: #cccccc;
@greenColor: #139d59;
@textColor: #1a1a1a;
@minWidth: 1366px;
@minHeight: 768px;
.wrap {
  .setting-box {
    width: 900px;
    display: flex;
    margin: 0 auto;
    flex-direction: column;
    .header {
      padding: 32px;
      font-size: 22px;
      text-align: center;
    }
    .body {
      padding: 0 32px;
      .body-item {
        display: flex;
        padding: 20px 0;
        .item-left {
          flex: 1;
          input {
            padding: 16px;
            border: 1px solid #ccc;
            width: 400px;
            outline: none;
            direction: ltr;
          }
        }
        .item-right {
          flex: 1;
          font-size: 22px;
          display: flex;
          align-items: center;
        }
      }
      .btn {
        padding: 20px 24px;
        background: #139d59;
        cursor: pointer;
        text-align: center;
        width: 50%;
        color: #fff;
        margin: 20px auto 0;
        font-size: 22px;
      }
    }
  }
}
</style>
<script>
import {
  getMerchantConfigListAPI,
  postMerchantConfigUpdateAPI
} from "./../api/index.js";
let self;
export default {
  data() {
    return {
      gLang: 1,
      max_ignore_price: null,
      max_percent_of_order: null,
      min_order_price: null,
      pass_word: "",
      type: ""
    };
  },
  created() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.getSettingList();
  },
  methods: {
    getSettingList() {
      let loading = this.$loading();
      getMerchantConfigListAPI({
        type: "ignore_price"
      }).then(response => {
        console.log(response);
        if (response.status >= 200 && response.status < 300) {
          self.max_ignore_price = response.data.data.value.max_ignore_price;
          self.max_percent_of_order =
            response.data.data.value.max_percent_of_order;
          self.min_order_price = response.data.data.value.min_order_price;
          self.pass_word = response.data.data.value.pass_word;
          self.type = response.data.data.type;
        }
      }).finally(() => {
        loading.close();
      });
    },
    saveSettings() {
      if (!this.max_ignore_price && this.max_ignore_price != 0) {
        this.$message({
          message: this.gL("requireMaxIgnorePrice"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (!this.max_percent_of_order && this.max_percent_of_order != 0) {
        this.$message({
          message: this.gL("requireMaxPercentOrder"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (this.max_percent_of_order > 30) {
        this.$message({
          message: this.gL("maxPercentOrderGreaterThirty"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (!this.min_order_price && this.min_order_price != 0) {
        this.$message({
          message: this.gL("requireMinOrderPrice"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      if (this.max_ignore_price < 0 || this.max_percent_of_order < 0 || this.min_order_price < 0) {
        this.$message({
          message: this.gL("requireNotBelowZero"),
          type: "warning",
          customClass: this.$toastClass(),
          offset: 120
        });
        return;
      }

      let data = {};
      data.max_ignore_price = self.max_ignore_price;
      data.max_percent_of_order = self.max_percent_of_order;
      data.min_order_price = self.min_order_price;
      data.pass_word = self.pass_word;
      data.type = "ignore_price";
      postMerchantConfigUpdateAPI(data).then(response => {
        console.log(response);
        if (response.status >= 200 && response.status < 300) {
          self.$message({
            message: self.gL("successfulOperation"),
            type: "success",
            customClass: self.$toastClass(),
            offset: 120
          });
        }
      });
    }
  },
  components: {}
};
</script>
