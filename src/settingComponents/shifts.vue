<template>
    <div class="wraps staff">
        <div class="top">
            <div class="serach-btn">
              <div class="serach">

              </div>
              <div class="btn" @click="addShifts()" :style="{flexDirection:gLang==1?'row':'row-reverse'}">
                  <span>{{gL('add')}}</span>
                  <span class="iconfont icon-jia-copy-copy"></span>
              </div>
            </div>
        </div>
        <div class="table">
        <el-table
        :data="tableData"
        header-cell-class-name="headers"
        :header-cell-style="{background:'#e6e6e6'}"
        row-class-name="row"
        v-loading="tableLoading"
        style="width: 100%">
        <el-table-column
          :label="gL('serialNumber')"
          type="index"
          width="70px"
          align="center">
        </el-table-column>
        <el-table-column
          prop="name"
          align="center"
          :label="gL('names')">
        </el-table-column>
        <el-table-column
          prop="sort"
          align="center"
          :label="gL('sort')">
        </el-table-column>
        <el-table-column
          align="center"
          :label="gL('state')">
          <template slot-scope="scope">
            <el-switch
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.state"
              @change="changeSwitch(scope.$index, scope.row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column :label="gL('operation')" prop="dosome" align="center" width="130px">
          <template slot-scope="scope">
            <!-- <el-button  type="success" @click="editData(scope.row)" class="iconfont icon-shiliangzhinengduixiang"  circle></el-button>
            <el-button  type="danger" @click="delData(scope.row)" class="iconfont icon-qingkong"  circle></el-button> -->
            <el-button type="text"  icon="iconfont icon-shiliangzhinengduixiang" @click="editData(scope.row)" circle></el-button>
             <span style="padding-right:9px;padding-left:15px" >
              <span class='line'></span>
            </span>
            <el-button  type="text" class="danger" @click="delData(scope.row)" icon="iconfont icon-qingkong"  circle></el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>
      <div class="mask" v-if="modal_box">
        <div class="box">
          <div class="title">
            <span></span>
            <span>{{modal_titles}}</span>
            <span class="iconfont icon-jia-copy" @click="cancels"></span>
          </div>
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
            <div class="content">
              <div class="items" :class="gLang==1?'items-ug':''">
                <div class="row">
                  <el-form-item prop="name_ug">
                    <el-input v-model="ruleForm.name_ug"  :placeholder="gL('overWorkName')+gL('ug')" class='input-ug' :class="gLang==1?'uy-input':'zh-input'" maxlength="20"></el-input>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="name_zh">
                    <el-input  v-model="ruleForm.name_zh" :placeholder="gL('overWorkName')+gL('zh')" :class="gLang==1?'uy-input':'zh-input'" maxlength="20"></el-input>
                  </el-form-item>
                </div>
                <div class="row">
                  <el-form-item prop="sort">
                    <el-input placeholder="" v-model="ruleForm.sort" type="number" oninput="if(value.length>3)value=value.slice(0,3)" @keyup.enter.native="confirm">
                      <template slot="append">{{gL('sort')}}</template>
                    </el-input>
                  </el-form-item>
                </div>
                <div class="row type">
                  <div class="check" :class="ruleForm.state==0?'active':''" @click="ruleForm.state=0">{{gL('off')}}</div>
                  <div class="check" :class="ruleForm.state==1?'active':''" @click="ruleForm.state=1">{{gL('open')}}</div>
                </div>
              </div>
              <div class="items" style="width:0">
              </div>
            </div>
          </el-form>
          <div class="btn" @click="confirm">
              {{gL('confirm')}}
          </div>
        </div>
      </div>
      <modal :number_box="confirm_box"
                 :modal_content="modal_content"
                 :modal_title="modal_title"
                 @cancel="cancel" @confirm="confirmBox"></modal>
    </div>
</template>

<script>
import modal from '../components/modal.vue'
import { getShiftListAPI, getShiftStateAPI, postShiftStoreAPI, putShiftUpdateAPI, deleteShiftDeleteAPI } from './../api/index.js'
var self;
export default {
  created: function() {
    self = this;
    self.gLang = localStorage.getItem("langId");
    self.getData();
  },
  data() {
    return {
      gLang:1,
      tableData:[],
      activeMneu:2,
      modal_box:false,
      confirm_box:false,
      modal_titles:this.gL('addShifts'),
      ruleForm:{
        state:1,
        name_zh:'',
        name_ug:'',
        sort:'',
      },
      rules: {
          name_zh: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          name_ug: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' }
          ],
          sort: [
            { required: true, message: this.gL('plaeseInput'), trigger: 'blur' },
          ],
        },
      addBox:true,
      modal_content:'',
      modal_title:'',
      tableLoading: false
    };
  },
  methods: {
    addShifts(){
      self.modal_box=true;
      self.addBox=true;
      if(self.tableData.length>0){
        self.ruleForm.sort = Math.max.apply(Math, self.tableData.map(function(o) {return o.sort}))+1;
      }
    },
    //获取数据
    getData(){
      this.tableLoading = true;
      getShiftListAPI().then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.tableData = response.data.data;
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },
     //开关
    changeSwitch(index,row){
      getShiftStateAPI(row.id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.$message({
            message:self.gL('successfulOperation'),
            type: "success",
            customClass:self.$toastClass(),
            offset: 120
          });
        }
      }).catch(err => {
        this.getData();
      });
    },
    //编辑
    editData(row){
      self.id = row.id;
      self.addBox = false;
      self.modal_box = true;
      self.ruleForm.name_ug = row.name_ug;
      self.ruleForm.name_zh = row.name_zh;
      self.ruleForm.sort = row.sort;
      self.ruleForm.state = row.state;
      self.modal_titles = self.gL('editShifts');
    },
    cancels(){
      self.modal_box = false;
      self.ruleForm.state = 1,
      self.ruleForm.name_ug = '';
      self.ruleForm.name_zh = '';
      self.ruleForm.sort = '';
      self.modal_titles = self.gL('addShifts');
    },
    confirm(){
      self.ruleForm.password_confirmation = self.ruleForm.password;
        if(self.addBox){
          self.$refs.ruleForm.validate((valid) => {
            if (valid) {
              postShiftStoreAPI(self.ruleForm).then((response) => {
                if(response.status >= 200 && response.status < 300){
                  self.$message({
                    message:self.gL('successfulOperation'),
                    type: "success",
                    customClass:self.$toastClass(),
                    offset: 120
                  });
                  self.getData();
                  self.cancels();
                }
              });
          } else {
            return false;
          }
        });
       }else{
        self.$refs.ruleForm.validate((valid) => {
        if (valid) {
          putShiftUpdateAPI(self.id, self.ruleForm).then((response) => {
            if(response.status >= 200 && response.status < 300){
              self.$message({
                message:self.gL('successfulOperation'),
                type: "success",
                customClass:self.$toastClass(),
                offset: 120
              });
              self.getData();
              self.cancels();
            }
          });
        }else {
            return false;
          }
        });
      }
    },
    //删除
    delData(row){
      self.id = row.id;
      self.confirm_box = true;
      if(self.gLang==1){
        self.modal_content="《"+row.name+"》"+self.gL('confirs')+self.gL('confirmdelete');
      }else{
        self.modal_content=self.gL('confirmdelete')+"《"+row.name+"》"+self.gL('confirs');
      }
      self.modal_title=self.gL('tips');
    },
    cancel(){
      self.confirm_box = false;
    },
    confirmBox(){
      self.cancel();
      self.delRowData();
    },
    delRowData(){
      deleteShiftDeleteAPI(self.id).then((response) => {
        if(response.status >= 200 && response.status < 300){
          self.$message({
            message:self.gL('successfulOperation'),
            type: "success",
            customClass:self.$toastClass(),
            offset: 120
          });
          self.getData();
        }
      });
    },
  },
  components:{
    modal,
  }
};
</script>

<style lang="less" scoped>
.wraps{
    width: 100%;
    height: 100%;
     .top{
        display: flex;
        justify-content: space-between;
        padding-right:20px;
        background-color: #2e3033;
        color:#ffffff;
        height: 7%;
        align-items: center;
        border-bottom: 1px solid #666666;
        flex-direction: row-reverse;
     .btn{
        background-color: #ff9c00;
        width: 150px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 25px;
        height: 90%;
        font-size: 26px;
        cursor: pointer;
        .iconfont{
            font-size: 19px;
        }
      }
      .serach-btn{
        display: flex;
        height: 100%;
        align-items: center;
      }
    }
    .table{
      height: 93%;
      overflow-y: scroll;
    }
      //s提示框
   .mask{
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      background-color: rgba(0,0,0,.5);
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 1366px;
      min-height: 768px;
      .box{
        background-color: #ffffff;
        width: 520px;
        font-size: 30px;
        .title{
          background-color: #e6e6e6;
          color:#1a1a1a;
          padding: 25px 20px;
          position: relative;
          text-align: center;
          .iconfont{
            position: absolute;
            right: 20px;
            font-size: 23px;
            color:#666666;
            cursor: pointer;
          }
        }
        .content{
          padding: 50px 75px 0 75px;
          display: flex;
          justify-content: space-between;
          .items{
            width: 100%;
            .row{
              margin-bottom: 20px;
            }
            .type{
              display: flex;
              justify-content: space-between;
              .check{
                width: 48%;
                border:1px solid #cccccc;
                color:#666666;
                font-size: 18px;
                text-align: center;
                cursor: pointer;
                padding: 10px;
              }
              .active{
                background-color: #139d59;
                color:#fff;
              }
            }
          }
        }
        .btn{
          margin: 20px 75px 20px 75px;
          background: #139d59;
          color:#ffffff;
          font-size: 26px;
          text-align: center;
          padding: 15px 0;
          cursor: pointer;
        }
      }
   }
  .avatar-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 45px;
    height: 40px;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 45px;
    height: 40px;
    line-height: 40px;
    text-align: center;
  }
  .avatar {
    width: 45px;
    height: 40px;
    display: block;
  }
  }
</style>
