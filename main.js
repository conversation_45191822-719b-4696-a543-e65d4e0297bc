const {
  app,
  B<PERSON>erWindow,
  Menu,
  ipcMain,
  dialog,
  globalShortcut,
  screen,
  powerSaveBlocker
} = require("electron");
const path = require("path");
const fs = require("fs");
const os = require("os");
const edge = require("electron-edge-js");
require("./utils/print.js")


// 判断是否win7系统禁止硬件加速
const platform = os.platform();
const release = os.release();
if (platform === "win32" && release.startsWith("6.1")) {
  app.disableHardwareAcceleration();
}
app.commandLine.appendSwitch("no-sandbox");

// // 判断是否mac系统
const isMac = platform === "darwin";

//打开远程调试
// app.commandLine.appendSwitch('remote-debugging-port', '8315')
//打开
// const httpProxy = require('http-proxy/lib/http-proxy.js');
// httpProxy.createServer({
//   target: 'ws://localhost:8315',
//   ws: true
// }).listen(8314);


// // 菜单参数
const menus = [
  {
    label: "视图",
    submenu: [
      // { label: "刷新", role: "reload" },
      ...(app.isPackaged
        ? []
        : [{ label: "打开调试工具", role: "toggleDevTools" }]), // 开发者工具
      { label: "默认大小", role: "resetZoom" },
      { label: "放大", role: "zoomIn" },
      { label: "缩小", role: "zoomOut" },
      { label: "全屏", role: "togglefullscreen" }
    ]
  },
  {
    label: "帮助",
    submenu: [
      { label: "关闭", role: isMac ? "close" : "quit" },
      { label: "关于我", role: "about" }
    ]
  }
];
const menu = Menu.buildFromTemplate(menus);
Menu.setApplicationMenu(menu);

let win = null;
let secondaryWindow = null;
const createWindow = () => {
  const size = screen.getPrimaryDisplay().size;

  win = new BrowserWindow({
    // 默认窗口模式
    width: size.width,
    height: size.height,
    show: false,
    frame: true, // 无边框窗口
    center: true, // 窗口居中
    fullscreen: false, // 是否全屏
    icon: "static/images/favicon.ico", // 图标
    autoHideMenuBar: true, // 是否隐藏菜单栏
    backgroundColor: "#333",
    webPreferences: {
      contextIsolation: true,
      nodeIntegration: false,
      preload: path.join(__dirname, "preload.js"),
      devTools: app.isPackaged ? false : true,
      webviewTag: true
    }
  });

  global.window = win;

  if (!app.isPackaged) {
    win.loadURL("http://localhost:8087/");
    setTimeout(() => {
      win.webContents.openDevTools({ mode: "bottom" });
    }, 1000)
  } else {
    win.loadFile("index.html");
  }

  win.once("ready-to-show", () => {
    win.show();
    win.maximize();
    global.window.send("set-adaption", screen.getPrimaryDisplay().scaleFactor);
  });

  win.on("close", () => {
    if (secondaryWindow != null) {
      secondaryWindow.close();
    }
  });

  const displays = screen.getAllDisplays();
  if (displays.length == 2) {
    const primaryDisplay = screen.getPrimaryDisplay();
    let secondaryDisplay = "";
    for (let i = 0; i < displays.length; i++) {
      if (displays[i].id != primaryDisplay.id) {
        secondaryDisplay = displays[i];
      }
    }

    secondaryWindow = new BrowserWindow({
      x: secondaryDisplay.bounds.x,
      y: secondaryDisplay.bounds.y,
      // width: 1920,
      // height: 1032,
      show: true,
      frame: false, // 无边框窗口
      center: true, // 窗口居中
      fullscreen: true, // 是否全屏
      parent: win,
      webPreferences: {
        contextIsolation: true,
        nodeIntegration: false,
        preload: path.join(__dirname, "secondaryPreload.js")
      }
    });

    if (!app.isPackaged) {
      secondaryWindow.loadURL("http://localhost:8087/#/secondaryMain");
      setTimeout(() => {
        secondaryWindow.webContents.openDevTools({ mode: "bottom" });
      }, 1000)
    } else {
      secondaryWindow.loadURL(
        path.resolve(__dirname) + "/index.html#/secondaryMain"
      );
    }

    secondaryWindow.once("ready-to-show", () => {
      secondaryWindow.send("set-adaption", secondaryDisplay.scaleFactor);
    });
  }

  //去掉菜单
  // win.setMenu(null);
};

const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
}

const checkUpdate = require("./utils/update.js");
let powerId = "";

app.whenReady().then(() => {
  createWindow();
  // 导入mqtt配置文件
  require("./utils/mqtt.js");

  // 阻止睡眠
  powerId = powerSaveBlocker.start("prevent-display-sleep");

  app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });

  // // 开发环境模拟正式打包
  // Object.defineProperty(app, "isPackaged", {
  //   get() {
  //     return true;
  //   }
  // });
  //检查升级
  checkUpdate();
});

app.on("second-instance", () => {
  if (win != null && win.isMinimized()) {
    win.restore();
  }
  win.focus();
})

app.on("ready", () => {
  globalShortcut.register("F5", () => {
    return false;
  });
});

app.on("will-quit", () => {
  globalShortcut.unregister("F5");
  powerSaveBlocker.stop(powerId);
});

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") app.quit();
});

// 退出窗口
ipcMain.on("quick-window", () => {
  app.quit();
});

// 将保存图片的逻辑封装成一个函数
ipcMain.handle("save-image", async (event, images) => {
  const { canceled, filePaths } = await dialog.showOpenDialog({
    properties: ["openDirectory"],
    filters: [{ name: "Images", extensions: ["jpg", "jpeg", "png", "gif"] }]
  });
  let fail = 0;
  if (canceled) {
    return 999; // 用户取消保存文件时返回 null
  } else {
    for (let i = 0; i < images.length; i++) {
      const base64 = images[i].image.replace(/^data:image\/\w+;base64,/, "");
      let dataBuffer = Buffer.from(base64, "base64");
      // 将 dataURL 保存到 filePath 的逻辑代码
      fs.writeFile(
        filePaths[0] + "/" + images[i].no + ".jpeg",
        dataBuffer,
        function(err) {
          if (err) {
            console.error(err, "保存失败");
            fail++;
          }
        }
      );
    }
    return fail;
  }
});

// 获取一些页面需要的数据
ipcMain.handle("get-apps", () => {
  return {
    version: app.getVersion(),
    platform: platform,
  };
});

// 获取全部usb设备列表
ipcMain.handle("get-usb-list", () => {
  return new Promise((resolve, reject) => {
    const edgeAdd = edge.func({
      assemblyFile: "./resources/dll/AlmasPrinter/AlmasPrinterDll.dll",
      typeName: "usb_final.UsbHandle",
      methodName: "GetAllUsbPrinterInfo"
    });
    // console.log("dll data", message);
    edgeAdd("", (err, result) => {
      if (err) {
        console.log("dll error", err);
        reject(err);
      }
      resolve(result);
    });
  });
});

// 获取全部usb设备状态
ipcMain.handle("get-printer-state", (event, data) => {
  return new Promise((resolve, reject) => {
    const edgeAdd = edge.func({
      assemblyFile: "./resources/dll/AlmasPrinter/AlmasPrinterDll.dll",
      typeName: "usb_final.UsbHandle",
      methodName: "GetEnablePrinterData"
    });
    // console.log("dll data", message);
    edgeAdd(JSON.stringify(data), (err, result) => {
      if (err) {
        console.log("dll error", err);
        reject(err);
      }
      resolve(result);
    });
  });
});

// 主屏发送副屏显示支付二维码
ipcMain.on("show-pay-qrcode", (event, data) => {
  if (secondaryWindow && secondaryWindow.webContents) {
    secondaryWindow.webContents.send("show-pay-qrcode-secondary", data);
  }
});

// 主屏发送副屏关闭支付二维码
ipcMain.on("close-pay-qrcode", event => {
  if (secondaryWindow && secondaryWindow.webContents) {
    secondaryWindow.webContents.send("close-pay-qrcode-secondary");
  }
});

// 主屏发送副屏显示会员支付二维码
ipcMain.on("show-recharge-pay-qrcode", (event, data) => {
  if (secondaryWindow && secondaryWindow.webContents) {
    secondaryWindow.webContents.send("show-recharge-pay-qrcode-secondary", data);
  }
});

// 主屏发送副屏关闭会员支付二维码
ipcMain.on("close-recharge-pay-qrcode", event => {
  if (secondaryWindow && secondaryWindow.webContents) {
    secondaryWindow.webContents.send("close-recharge-pay-qrcode-secondary");
  }
});


// 获取打印缓存
ipcMain.handle("get-printer-cache", (event, merchant_no) => {
  return new Promise((resolve, reject) => {
    const filePath = app.isPackaged ? path.resolve(__dirname, "./../config/printQueue.json") : path.resolve(__dirname, "./resources/config/printQueue.json");
    fs.readFile(filePath, (err, data) => {
      if (err) {
        resolve(0);
      }
      const res = JSON.parse(data.toString())
      if (res != null && res[merchant_no]) {
        resolve(res[merchant_no].length)
      } else {
        resolve(0)
      }
    })
  })
})

// 清空打印缓存
ipcMain.handle("clear-printer-cache", (event, merchant_no) => {
  return new Promise((resolve, reject) => {
    const filePath = app.isPackaged ? path.resolve(__dirname, "./../config/printQueue.json") : path.resolve(__dirname, "./resources/config/printQueue.json");
    fs.readFile(filePath, (err, data) => {
      if (err) {
        resolve("找不到缓存文件");
      }
      const res = JSON.parse(data.toString())
      if (res[merchant_no]) {
        res[merchant_no] = []
        fs.writeFile(filePath, JSON.stringify(res), (err) => {
          if (err) {
            resolve("缓存清空失败");
          } else {
            resolve("success");
          }
        })
      } else {
        resolve("success")
      }
    })
  })
})
