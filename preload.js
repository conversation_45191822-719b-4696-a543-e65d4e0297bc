const { contextBridge, ipcRenderer, webFrame } = require("electron");
// import html2canvas  from 'html2canvas';
// const html2canvas = require("html2canvas");

contextBridge.exposeInMainWorld("electronAPI", {
  isElectron: () => (process.versions.electron ? true : false),
  quit: () => ipcRenderer.send("quick-window"),
  print: data => ipcRenderer.invoke("print", data),
  saveImage: images => ipcRenderer.invoke("save-image", images),
  getApps: () => ipcRenderer.invoke("get-apps"),
  connectMqtt: data => ipcRenderer.invoke("connect-mqtt", data),
  closeMqtt: () => ipcRenderer.invoke("close-mqtt"),
  customPrint: data => ipcRenderer.send("custom-print", data),
  isConnectMqtt: fn => ipcRenderer.on("connected-mqtt", fn),
  printMessage: fn => ipcRenderer.on("print-message", fn),
  getPrinterList: () => ipcRenderer.invoke("get-usb-list"),
  showPayQrCode: data => ipcRenderer.send("show-pay-qrcode", data),
  closePayQrCode: () => ipcRenderer.send("close-pay-qrcode"),
  getPrinterState: data => ipcRenderer.invoke("get-printer-state", data),
  watchMQTT: fn => ipcRenderer.on("watch-mqtt", fn),
  showRechargePayQrCode: data =>
    ipcRenderer.send("show-recharge-pay-qrcode", data),
  closeRecahrgePayQrCode: () => ipcRenderer.send("close-recharge-pay-qrcode"),
  reConnectMqtt: fn => ipcRenderer.on("re-connected-mqtt", fn),
  checkUpdate: () => ipcRenderer.send("check-update"),
  updateToast: fn => ipcRenderer.on("update-toast", fn),
  updateDownload: (type) => ipcRenderer.send("update-download", type),
  getPrinterCache: (data) => ipcRenderer.invoke("get-printer-cache", data),
  clearPrinterCache: (data) => ipcRenderer.invoke("clear-printer-cache", data),
  recordPrintTime: (data) => ipcRenderer.send("record-print-time", data),
  printTask: (data) => ipcRenderer.send("printTask", data),
  printFailed: fn => ipcRenderer.on("printFailed", fn),
  slsLogSend: data => ipcRenderer.send("slsLogSend", data),
  mqttMessage: data => ipcRenderer.on("mqtt-message", data)
});

ipcRenderer.on("set-adaption", (event, scaleFactor) => {
  var devInnerHeight = 1080.0; // 开发时的InnerHeight
  var devDevicePixelRatio = 1.0; // 开发时的devicepixelratio
  var devScaleFactor = 1.1; // 开发时的ScaleFactor
  var zoomFactor =
    (window.innerHeight / devInnerHeight) *
    (window.devicePixelRatio / devDevicePixelRatio) *
    (devScaleFactor / scaleFactor);
  webFrame.setZoomFactor(zoomFactor);
});
