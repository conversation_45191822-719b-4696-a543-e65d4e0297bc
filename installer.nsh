Section ".netFramework" SecApp
  SetOutPath $INSTDIR
  File /r "F:\mulazim_project\windows_cashier_素材\net_framework\ndp472-kb4054530-x86-x64-allos-enu.exe" ; 将应用程序文件复制到安装目录
SectionEnd

Section "PreRequisites" SecPrerequisites
  ; 检查是否已安装.NET Framework 4.7.2
  ReadRegDWORD $0 HKLM "SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" "Release"
  IntCmp $0 461808 0 DotNetInstalled
    Goto DotNetInstalledEnd
  DotNetInstalled:
    ; 运行.NET Framework 4.7.2安装程序
    ExecWait '"ndp472-kb4054530-x86-x64-allos-enu.exe" /q /norestart'
    Pop $0
    StrCmp $0 0 DotNetInstalledFailed
      ; 验证.NET Framework 4.7.2是否真的安装成功
      ReadRegDWORD $1 HKLM "SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" "Release"
      IntCmp $1 461808 0 DotNetInstalledFailed
          Goto DotNetInstalledSuccess
    DotNetInstalledFailed:
      MessageBox MB_OK "安装.NET Framework 4.7.2失败"
      Quit
  DotNetInstalledSuccess:
  DotNetInstalledEnd:
SectionEnd
